from django.contrib import admin
from django.contrib.staticfiles.storage import staticfiles_storage
from django.utils.html import format_html
from .models import PaymentLog, PaymentPeriodTrailblazerHours, SkippedPaymentLog, PaymentAutomationSetting
import requests
import json
from requests.auth import HTTPBasic<PERSON><PERSON>
from decimal import Decimal, InvalidOperation
import uuid
import logging
from django.utils import timezone

# Simplified PayPalAPI class
class PayPalAPI:
    def __init__(self, client_id, client_secret, base_url):
        self.client_id = client_id
        self.client_secret = client_secret
        self.base_url = base_url
        self.access_token = self.get_access_token()
        self.payout_batch_id = None

    def get_access_token(self):
        url = f"{self.base_url}/v1/oauth2/token"
        headers = {
            "Accept": "application/json",
            "Accept-Language": "en_US"
        }
        data = {
            "grant_type": "client_credentials"
        }
        response = requests.post(url, headers=headers, data=data, auth=HTTPBasicAuth(self.client_id, self.client_secret))
        response.raise_for_status()
        return response.json()['access_token']

    def make_payout(self, items):
        url = f"{self.base_url}/v1/payments/payouts"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}"
        }
        sender_batch_id = str(uuid.uuid4())
        data = {
            "sender_batch_header": {
                "sender_batch_id": sender_batch_id,
                "recipient_type": "EMAIL",
                "email_subject": "You have money!",
                "email_message": "You received a payment. Thanks for using our service!"
            },
            "items": items
        }
        response = requests.post(url, headers=headers, data=json.dumps(data))
        response.raise_for_status()
        payout_response = response.json()
        self.payout_batch_id = payout_response['batch_header']['payout_batch_id']
        return payout_response

    def retrieve_payout_result(self):
        if not self.payout_batch_id:
            logging.error("No payout_batch_id available. Make a payout first.")
            return
        url = f"{self.base_url}/v1/payments/payouts/{self.payout_batch_id}"
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {self.access_token}"
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()
        return response.json()

# PaymentLogger class
# class PaymentLogger:
#     def log_payment(self, paypal_email, hours, amount, payment_status, error_message=None, trailblazer=None):
#         payment_date = timezone.now().isoformat()
#         PaymentLog.objects.create(
#             user=trailblazer,
#             paypal_email=paypal_email,
#             hours=hours,
#             amount=Decimal(amount),
#             payment_status=payment_status,
#             payment_date=payment_date,
#             error_message=error_message
#         )

#     def log_skipped_payment(self, paypal_email, hours, amount, reason, trailblazer=None):
#         payment_date = timezone.now().isoformat()
#         SkippedPaymentLog.objects.create(
#             paypal_email=paypal_email,
#             reason=reason,
#             created_at=payment_date,
#             trailblazer=trailblazer
#         )
@admin.register(PaymentAutomationSetting)
class PaymentAutomationSettingAdmin(admin.ModelAdmin):
    list_display = ('is_automated',)

    def has_add_permission(self, request):
        # Prevent adding new instances
        return False

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the existing instance
        return False

      


@admin.register(PaymentLog)
class PaymentLogAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'paypal_email', 'hours', 'amount', 'payment_status', 'payment_date',
        'payment_period_start', 'payment_period_end', 'payout_item_id',
        'time_processed', 'payout_batch_id', 'batch_status', 'time_completed', 'created_at', 'error_message','updated_at'
    )
    search_fields = ('paypal_email', 'payment_status', 'user__email')
    list_filter = ('payment_status', 'payment_date', 'created_at', 'payout_batch_id', 'payment_period_start', 'payment_period_end', 'payment_date')
    ordering = ('-payment_date',)
    readonly_fields = ('updated_at',)
    actions = ['mark_as_paid', 'mark_as_failed']
    fieldsets = (
        (None, {
            'fields': ('user', 'paypal_email', 'hours', 'amount', 'payment_status', 'batch_status', 'payment_period_start', 'payment_period_end', 'payment_date', 'error_message', 'created_at', 'updated_at')
        }),
        ('Advanced options', {
            'classes': ('collapse',),
            'fields': ('payout_batch_id', 'payout_item_id', 'time_processed'),
        }),
    )

    def mark_as_paid(self, request, queryset):
        queryset.update(payment_status='SUCCESS')
    mark_as_paid.short_description = "Mark selected logs as paid"

    def mark_as_failed(self, request, queryset):
        queryset.update(payment_status='FAILED')
    mark_as_failed.short_description = "Mark selected logs as failed"

    class Media:
        css = {
            'all': ('css/admin_custom.css',)
        }

@admin.register(PaymentPeriodTrailblazerHours)
class PaymentPeriodTrailblazerHoursAdmin(admin.ModelAdmin):
    exclude = ('updated_at',)
    list_display = ('user', 'paypal_email', 'total_hours', 'payment_period_start', 'payment_period_end', 'created_at')
    search_fields = ('user__email', 'status', 'payment_period_start', 'payment_period_end')
    actions = ['reset_hours']
    fieldsets = (
        (None, {
            'fields': ('user', 'total_hours')
        }),
    )

@admin.register(SkippedPaymentLog)
class SkippedPaymentLogAdmin(admin.ModelAdmin):
    list_display = (
        'user', 'paypal_email', 'hours', 'payment_status', 'payment_date',
        'payment_period_start', 'payment_period_end', 'payout_item_id',
        'time_processed', 'payout_batch_id', 'batch_status', 'time_completed', 'created_at', 'error_message', 'updated_at'
    )
    search_fields = ('paypal_email', 'user__email', 'payment_status')
    list_filter = ('created_at', 'error_message', 'payment_status')
    ordering = ('-created_at',)
    actions = ['retry_payment', 'move_to_payment_log']
    readonly_fields = ('created_at', 'updated_at')
    fieldsets = (
        (None, {
            'fields': ('user', 'paypal_email', 'hours', 'payment_status', 'payment_date', 'payment_period_start', 'payment_period_end', 'payout_batch_id', 'payout_item_id', 'error_message', 'time_processed', 'created_at', 'updated_at')
        }),
    )

    def move_to_payment_log(self, request, queryset):
        for skipped_log in queryset:
            PaymentLog.objects.create(
                user=skipped_log.user,
                paypal_email=skipped_log.user.college_student_profile.paypal_email,
                hours=skipped_log.hours,
                amount=Decimal(skipped_log.hours) * Decimal(20),  # Assuming $20 per hour
                payment_status='SUCCESS',
                payment_period_start=skipped_log.payment_period_start,
                payment_period_end=skipped_log.payment_period_end,
                payment_date=timezone.now(),
                error_message=(
                    f"Originally skipped on {skipped_log.created_at.strftime('%Y-%m-%d %I:%M %p')} "
                    f"for the payment period {skipped_log.payment_period_start.strftime('%Y-%m-%d')} - "
                    f"{skipped_log.payment_period_end.strftime('%Y-%m-%d')} due to '{skipped_log.error_message}'. "
                    f"Payment successfully processed and marked as SUCCESS on {timezone.now().strftime('%Y-%m-%d %I:%M %p')}."
                ),
                created_at=skipped_log.created_at,
                updated_at=timezone.now(),
                payout_batch_id= skipped_log.payout_batch_id,  # Replace with actual value
                payout_item_id= skipped_log.payout_item_id  # Replace with actual value
            )
            skipped_log.delete()

    move_to_payment_log.short_description = "Move selected skipped payments to payment log and mark as Success"

    

    # def retry_payment(self, request, queryset):
    # Load environment variables
        #     client_id = os.getenv('CLIENT_ID')
        # client_secret = os.getenv('CLIENT_SECRET')
        # base_url = os.getenv('PAYPAL_API_BASE_URL')

    #     # Initialize PayPal API and Payment Logger
    #     paypal_api = PayPalAPI(client_id, client_secret, base_url)
    #     payment_logger = PaymentLogger()

    #     items = []
    #     trailblazer_map = {}

    #     for log in queryset:
    #         paypal_email = log.paypal_email
    #         total_hours = log.hours
    #         amount_value = Decimal(log.amount) if log.amount else Decimal(0)
    #         trailblazer = log.trailblazer

    #         if not paypal_email or not validate_email(paypal_email):
    #             logging.warning(f"Skipping item with invalid email: {paypal_email}")
    #             payment_logger.log_skipped_payment(paypal_email, total_hours, amount_value, "Invalid email", trailblazer)
    #             continue

    #         items.append({
    #             "recipient_type": "EMAIL",
    #             "amount": {
    #                 "value": f"{amount_value:.2f}",
    #                 "currency": "USD"
    #             },
    #             "sender_item_id": str(uuid.uuid4()),
    #             "receiver": paypal_email,
    #             "trailblazer": trailblazer
    #         })

    #     if not items:
    #         logging.info("No valid items to process for payout.")
    #         return

    #     # Extract the trailblazer value and remove it from the items for payout
    #     items_for_payout = []
    #     for item in items:
    #         trailblazer_map[item['receiver']] = item.pop('trailblazer')
    #         items_for_payout.append(item)

    #     try:
    #         # Make the payout
    #         paypal_api.make_payout(items_for_payout)

    #         payout_result = paypal_api.retrieve_payout_result()
            
    #         if not payout_result:
    #             logging.error("Failed to retrieve payout result.")
    #             return
                
    #         for item in payout_result.get('items', []):
    #             paypal_email = item['payout_item']['receiver']
    #             amount = item['payout_item']['amount']['value']
    #             payment_status = item['transaction_status']
    #             hours = float(amount) / 20  
    #             trailblazer = trailblazer_map.get(paypal_email)
    #             payment_logger.log_payment(paypal_email, hours, amount, payment_status, trailblazer=trailblazer)

    #     except Exception as err:
    #         logging.error(f"An error occurred during payout processing: {err}")

    # retry_payment.short_description = "Retry payment for selected logs"
