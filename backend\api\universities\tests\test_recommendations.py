from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from api.universities.models import University, ShortlistItem
from api.users.models import HighSchoolStudentProfile

User = get_user_model()

class RecommendationsViewTests(APITestCase):
    def setUp(self):
        # Create a high school student user
        self.high_school_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        
        # Create a high school student profile with recommendations, using get_or_create to avoid duplicates
        self.profile, created = HighSchoolStudentProfile.objects.get_or_create(user=self.high_school_user)
        
        # Create test universities
        self.university1 = University.objects.create(
            unitid="12345",
            institution="Test University 1",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        self.university2 = University.objects.create(
            unitid="67890",
            institution="Test University 2",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        
        # Set recommendations for the profile
        self.profile.university_recommendations = {
            "reach": ["12345"],
            "target": ["67890"],
            "safety": []
        }
        self.profile.recommendations_status = "generated"
        self.profile.save()
        
        # Create a shortlist item for the high school user
        self.shortlist_item = ShortlistItem.objects.create(
            user=self.high_school_user,
            university=self.university1
        )
        
        # Setup the client and URL
        self.client = APIClient()
        self.url = reverse('recommendations')
    
    def test_recommendations_include_bookmarked(self):
        """Test that recommendations include the bookmarked attribute."""
        self.client.force_authenticate(user=self.high_school_user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Verify that university1 is bookmarked
        reach_unis = response.data.get('reach', [])
        self.assertEqual(len(reach_unis), 1)
        self.assertTrue(reach_unis[0]['bookmarked'])
        
        # Verify that university2 is not bookmarked
        target_unis = response.data.get('target', [])
        self.assertEqual(len(target_unis), 1)
        self.assertFalse(target_unis[0]['bookmarked'])
    
    def test_recommendations_empty(self):
        """Test retrieving recommendations when there are none."""
        # Update profile to have empty recommendations
        self.profile.university_recommendations = {}
        self.profile.save()
        
        self.client.force_authenticate(user=self.high_school_user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_recommendations_unauthenticated(self):
        """Test that unauthenticated users cannot retrieve recommendations."""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)