from rest_framework import generics, status
from rest_framework.response import Response
from django.utils import timezone
from .serializers import SendVerificationEmailSerializer, VerifyEmailSerializer
from api.users.serializers import UserSerializer
from .models import EmailVerificationCode
from api.users.models import User
from django.core.mail import send_mail
from rest_framework.permissions import IsAuthenticated
import logging
import random
from django.conf import settings

logger = logging.getLogger(__name__)

class SendVerificationEmailView(generics.GenericAPIView):
    serializer_class = SendVerificationEmailSerializer
    permission_classes = [IsAuthenticated]  # Ensure the user is authenticated

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = request.user
        code = ''.join([str(random.randint(0, 9)) for _ in range(6)])
        EmailVerificationCode.objects.create(user=user, code=code)

        # Send verification email
        subject = "Your Trailblazer Verification Code"
        message = f"Your verification code is {code}. It will expire in 30 minutes."
        from_email = settings.DEFAULT_FROM_EMAIL
        recipient_list = [user.email]
        send_mail(subject, message, from_email, recipient_list)

        logger.info(f"Verification code sent to {user.email}")
        return Response({"detail": "Verification code sent."}, status=status.HTTP_200_OK)


class VerifyEmailView(generics.GenericAPIView):
    serializer_class = VerifyEmailSerializer
    permission_classes = [IsAuthenticated]  # Ensure the user is authenticated

    def post(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data, context={'request': request})
        serializer.is_valid(raise_exception=True)
        user = request.user
        verification = serializer.validated_data['verification']

        # Mark email as verified (assuming User model has a field for this)
        user.profile.is_email_verified = True
        user.profile.save()

        # Delete the verification code after successful verification
        verification.delete()

        data = {
            "user": UserSerializer(user).data,
        }

        logger.info(f"Email verified for {user.email}")
        return Response(data, status=status.HTTP_200_OK)
