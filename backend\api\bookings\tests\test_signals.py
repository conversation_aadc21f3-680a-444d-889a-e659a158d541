from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from datetime import timedelta
from unittest.mock import patch
import json

from api.bookings.models import Booking, TrailblazerBookingStatus
from api.calendar_integration.models import MasterGoogleCredentials

User = get_user_model()


class BookingSignalsTest(TestCase):
    def setUp(self):
        # Set up the master credentials
        self.master_creds = MasterGoogleCredentials.objects.create(
            token='test_token',
            refresh_token='test_refresh',
            token_uri='https://oauth2.googleapis.com/token',
            client_id='test_client_id',
            client_secret='test_secret',
            scopes=json.dumps(['https://www.googleapis.com/auth/calendar']),
            expiry=timezone.now() + timedelta(hours=1)
        )
        self.trailblazer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.booked_by = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.start_time = timezone.now() + timedelta(days=1)
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Test booking",
            creator_status='confirmed'
        )
        self.trailblazer_status = TrailblazerBookingStatus.objects.create(
            booking=self.booking,
            trailblazer=self.trailblazer,
            status='pending'
        )

    @patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
    def test_trailblazer_status_confirmation_triggers_calendar_event(self, mock_create_events):
        # Changing status to confirmed should trigger event creation
        self.trailblazer_status.status = 'confirmed'
        self.trailblazer_status.save()
        mock_create_events.assert_called_once_with(self.booking)

    @patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
    def test_non_confirmation_status_does_not_trigger_calendar_event(self, mock_create_events):
        # Changing trailblazer status to declined should not trigger event creation
        self.trailblazer_status.status = 'declined'
        self.trailblazer_status.save()
        mock_create_events.assert_not_called()

    @patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
    def test_calendar_event_creation_error_is_logged(self, mock_create_events):
        # Simulate an error during event creation and verify logging
        mock_create_events.side_effect = Exception("Calendar API error")
        with self.assertLogs('api.bookings.signals', level='ERROR') as logs:
            self.trailblazer_status.status = 'confirmed'
            self.trailblazer_status.save()
            self.assertIn('Failed to create calendar event', logs.output[0])

    @patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
    @patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
    def test_booking_status_cancelled_triggers_calendar_event_cancellation(self, mock_create_events, mock_cancel_events):
        self.trailblazer_status.status = 'confirmed'
        self.trailblazer_status.save()
        # Changing Booking's status to cancelled should trigger event cancellation
        self.booking.creator_status = 'cancelled'
        self.booking.save()
        mock_cancel_events.assert_called_once_with(self.booking)

    @patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
    @patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
    def test_trailblazer_status_change_to_declined_triggers_booking_cancel_event(self, mock_create_events, mock_cancel_events):
        self.trailblazer_status.status = 'confirmed'
        self.trailblazer_status.save()
        # Changing trailblazer status to decline and resulting in booking status 'cancelled'
        self.trailblazer_status.status = 'declined'
        self.trailblazer_status.save()
        
        # If all trailblazers decline, booking status becomes 'cancelled'
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.status, 'declined')
        mock_cancel_events.assert_called_once_with(self.booking)

    @patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
    def test_non_cancelled_status_does_not_trigger_calendar_event_cancellation(self, mock_cancel_events):
        # Changing status to pending or confirmed should not trigger cancellation
        self.trailblazer_status.status = 'pending'
        self.trailblazer_status.save()
        mock_cancel_events.assert_not_called()
