"use client"

import { useSearchParams } from 'next/navigation'
import { MainLayout } from "@/components/ui/mainLayout"
import TrailblazerProfileView from "@/components/ui/TrailblazerProfileView"
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';


const TrailblazerProfiles = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()
    const searchParams = useSearchParams()
    const idsParam = searchParams.get('ids')
    const trailblazerIds = idsParam ? idsParam.split(',') : [];
    const hasInvalidIds = idsParam && trailblazerIds.length !== idsParam.split(',').length
    return (
        <MainLayout user={user} displaySidebarMenu={false}>
            <div className="container mx-auto px-4 pt-0 pb-8 md:pt-8">
                { !idsParam || trailblazerIds.length === 0 ? (
                    <p className="text-red-500">No Trailblazers are selected.</p>
                ) : (
                    <>
                        { hasInvalidIds && <p className="text-red-500">Some profiles could not be loaded due to invalid IDs.</p> }
                        <TrailblazerProfileView trailblazerIds={trailblazerIds} />
                    </>
                )}
            </div>
        </MainLayout>
    )
}

export default withAuth(TrailblazerProfiles)
