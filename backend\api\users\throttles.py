from rest_framework.throttling import UserRateThrottle

class LoginRateThrottle(UserRateThrottle):
    scope = 'login'


class SignupRateThrottle(UserRateThrottle):
    scope = 'signup'

class RecommendationRegenerationRateThrottle(UserRateThrottle):
    """
    Throttle for recommendation regeneration requests.
    Limits users to 3 requests per 6-hour window.
    """
    scope = 'recommendation_regeneration'
    rate = '3/6h'  # 3 requests per 6 hours

    def parse_rate(self, rate):
        if rate is None:
            return None
        try:
            num, period = rate.split('/')
            num_requests = int(num)
            # period is expected to be like "6h" where last char is unit and the rest is multiplier
            unit = period[-1]
            multiplier = int(period[:-1])
            factor = {'s': 1, 'm': 60, 'h': 3600, 'd': 86400}
            duration = multiplier * factor[unit]
            return num_requests, duration
        except Exception as e:
            # Fallback to base implementation if any error occurs
            from rest_framework.throttling import _parse_rate
            return _parse_rate(rate)

    def get_history(self, request):
        """
        Helper method to retrieve the request history timestamps for the user.
        Does NOT increment anything.
        """
        self.key = self.get_cache_key(request, self.view)
        if self.key is None:
            return []

        history = self.cache.get(self.key, [])
        return self._clean_history(history)

    def _clean_history(self, history):
        """Remove expired timestamps from history."""
        now = self.timer()
        return [timestamp for timestamp in history if timestamp > now - self.duration]

    def remaining_requests(self, request):
        history = self.get_history(request)
        num_requests, _ = self.parse_rate(self.rate)
        return max(0, num_requests - len(history))
