## Sign-Up Endpoints

### College Student Sign-Up
**URL:** `/api/v1/users/signup/college-student/`  
**Method:** `POST`  
**Description:** Register a new College Student.  
**Required Fields:** `email`, `password`, `college_major`, `college_tags`, `profile`  
**Optional Fields:** `first_name`, `last_name`, `interests`, `university`, `high_school_name`, `high_school_zip_code`, `high_school_city`, `high_school_state`  

### Counselor/Administrator Sign-Up
**URL:** `/api/v1/users/signup/counselor-administrator/`  
**Method:** `POST`  
**Description:** Register a new Counselor or Administrator.  
**Required Fields:** `email`, `password`, `organization`, `position`, `profile`  
**Optional Fields:** `first_name`, `last_name`  

### High School Student Sign-Up
**URL:** `/api/v1/users/signup/high-school-student/`  
**Method:** `POST`  
**Description:** Register a new High School Student.  
**Required Fields:** `email`, `password`, `organization`, `grade_level`, `profile`  
**Optional Fields:** `first_name`, `last_name`  
