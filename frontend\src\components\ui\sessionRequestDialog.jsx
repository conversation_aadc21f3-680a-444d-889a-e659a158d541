"use client"

import { useState, useEffect, useMemo } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogClose } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Calendar } from 'lucide-react'
import { Loader2 } from "lucide-react"
import { getFullTimeZoneName } from "@/lib/utils"

// Define the form schema using zod
const formSchema = z.object({
  studentsCount: z.number().min(1, "At least one student is required"),
  sessionTopics: z.string().min(10, "Please provide more details about the session topics").max(280, "Message cannot exceed 280 characters")
})

// SessionRequestForm component
const SessionRequestForm = ({ onSubmit, sessionInfo, isLoadingBooking, displayStudentsCount }) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      studentsCount: 1,
      sessionTopics: ""
    }
  })

  const [isFormValid, setIsFormValid] = useState(false)
  const [currentCharCount, setCurrentCharCount] = useState(0)

  // Watch for form changes to update button state
  useEffect(() => {
    const subscription = form.watch((value, { name, type }) => {
      setIsFormValid(form.formState.isValid)
      setCurrentCharCount(value.sessionTopics.length)
    })
    return () => subscription.unsubscribe()
  }, [form])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {displayStudentsCount && (
          <FormField
            control={form.control}
            name="studentsCount"
            render={({ field }) => (
              <FormItem>
                <FormLabel>How many students will attend the session?</FormLabel>
                <FormControl>
                  <Input type="number" {...field} onChange={e => field.onChange(parseInt(e.target.value))} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        )}

        <FormField
          control={form.control}
          name="sessionTopics"
          render={({ field }) => (
            <FormItem>
              <FormLabel>Let them know what you&apos;d like to cover in this session</FormLabel>
              <FormControl>
                <Textarea 
                  {...field} 
                  placeholder="What topics would you like to discuss? Is there anything specific you’d like them to know?"
                  className="resize-none"
                  rows={4}
                  maxLength={280}
                  onChange={(e) => {
                    field.onChange(e);
                    setCurrentCharCount(e.target.value.length);
                  }}
                />
              </FormControl>
              <FormMessage />
              <p className="text-sm text-gray-500 mt-1">{currentCharCount} / 280</p>
            </FormItem>
          )}
        />

        <Button type="submit" className="w-full" disabled={isLoadingBooking}>
          {isLoadingBooking && (
              <Loader2 className="animate-spin inline-block mr-2" />
          )}
            Request Session
          <Calendar className="w-5 h-5 ml-2" />
        </Button>
      </form>
    </Form>
  )
}

// Main SessionRequestDialog component
const SessionRequestDialog = ({ isOpen, onClose, onSubmit, trailblazerName, sessionDate, sessionTime, isLoadingBooking, error, displayStudentsCount }) => {
  const currentTimezone = useMemo(() => getFullTimeZoneName(), [])
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-lg p-8">
        <DialogHeader>
          <DialogTitle className="text-xl font-bold">Request a Session with {trailblazerName}</DialogTitle>
        </DialogHeader>
        <p className="text-sm mb-4">
          <div>
            {sessionDate}
          </div>
          <div>
            {sessionTime}, {currentTimezone}
          </div>
        </p>
        <SessionRequestForm onSubmit={onSubmit} isLoadingBooking={isLoadingBooking} displayStudentsCount={displayStudentsCount} />
        {error && (
          <p className="text-red-500 text-sm mt-4">{error}</p>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default SessionRequestDialog
