import { useState } from 'react'
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group"
import { cn } from "@/lib/utils"

// Interest tags
export const interestTags = [
    { label: "Arts, Media & Entertainment", value: "arts" },
    { label: "Consulting", value: "consulting" },
    { label: "Entrepreneurship", value: "entrepreneurship" },
    { label: "Engineering", value: "engineering" },
    { label: "Finance & Accounting", value: "finance" },
    { label: "Government, Policy & Law", value: "government" },
    { label: "Medicine & Health Services", value: "medicine" },
    { label: "Marketing", value: "marketing" },
    { label: "Non-profit & Social Work", value: "nonprofit" },
    { label: "Tech", value: "tech" },
]

// Interest picker component
export const InterestPicker = ({ field }) => {
    const [selectedCount, setSelectedCount] = useState(field.value.length)

    const handleValueChange = (value) => {
        if (value.length <= 3) {
            field.onChange(value)
            setSelectedCount(value.length)
        }
    }

    return (
        <div>
            <ToggleGroup
                type="multiple"
                value={field.value}
                onValueChange={handleValueChange}
                className="flex flex-wrap gap-3 justify-start"
            >
                {interestTags.map((tag) => (
                    <ToggleGroupItem
                        key={tag.value}
                        value={tag.value}
                        aria-label={tag.label}
                        disabled={selectedCount >= 3 && !field.value.includes(tag.value)}
                        className={cn(
                            "px-3 py-2 rounded-lg text-sm",
                            field.value.includes(tag.value)
                                ? "data-[state=on]:bg-[#ADFFC2] text-primary-foreground border-green-500 border-2"
                                : "bg-white border border-gray-200 text-gray-700",
                            selectedCount >= 3 && !field.value.includes(tag.value) && "opacity-50 cursor-not-allowed"
                        )}
                    >
                        {tag.label}
                    </ToggleGroupItem>
                ))}
            </ToggleGroup>
            <p className="text-sm text-gray-500 mt-4 pl-2">
                {selectedCount}/3 interests selected
            </p>
        </div>
    )
}