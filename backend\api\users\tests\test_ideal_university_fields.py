"""
Tests for the ideal university fields in the HighSchoolStudentProfile model.
"""
from django.test import TestCase
from django.contrib.auth import get_user_model

from api.users.models import HighSchoolStudentProfile

User = get_user_model()

class IdealUniversityFieldsTests(TestCase):
    """Test cases for the ideal university fields in the HighSchoolStudentProfile model."""
    
    def setUp(self):
        """Set up test data."""
        # Create a user with a high school student profile
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.profile = self.user.high_school_student_profile
    
    def test_ideal_university_fields_default_values(self):
        """Test that the ideal university fields have the correct default values."""
        self.assertIsNone(self.profile.ideal_university_description)
        self.assertIsNone(self.profile.ideal_university_embedding)
    
    def test_ideal_university_fields_can_be_set(self):
        """Test that the ideal university fields can be set and retrieved."""
        # Set the fields
        sample_description = "This is a sample ideal university description."
        sample_embedding = [0.1] * 1536
        
        self.profile.ideal_university_description = sample_description
        self.profile.ideal_university_embedding = sample_embedding
        self.profile.save()
        
        # Refresh from database
        self.profile.refresh_from_db()
        
        # Check the values
        self.assertEqual(self.profile.ideal_university_description, sample_description)
        # Cast the embedding to list if necessary to avoid ambiguous truth value error
        self.assertEqual(list(self.profile.ideal_university_embedding), sample_embedding)
    
    def test_ideal_university_fields_can_be_null(self):
        """Test that the ideal university fields can be null."""
        # Fields should be null by default
        self.assertIsNone(self.profile.ideal_university_description)
        self.assertIsNone(self.profile.ideal_university_embedding)
        
        # Set and then unset the fields
        self.profile.ideal_university_description = "Test description"
        self.profile.ideal_university_embedding = [0.1] * 1536
        self.profile.save()
        
        self.profile.ideal_university_description = None
        self.profile.ideal_university_embedding = None
        self.profile.save()
        
        # Refresh from database
        self.profile.refresh_from_db()
        
        # Check that the fields are null
        self.assertIsNone(self.profile.ideal_university_description)
        self.assertIsNone(self.profile.ideal_university_embedding)