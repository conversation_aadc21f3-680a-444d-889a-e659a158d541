from django.db.models.signals import post_save, pre_save
from django.dispatch import receiver

from api.bookings.models import Booking, TrailblazerBookingStatus
from api.calendar_integration.services import CalendarService

from api.bookings.services.email_service import BookingEmailService

import logging

logger = logging.getLogger(__name__)


@receiver(pre_save, sender=TrailblazerBookingStatus)
def cache_previous_booking_status(sender, instance, **kwargs):
    """Cache the previous booking status before saving."""
    if instance.pk:
        # Only retrieve the previous status if it's an existing object
        previous_instance = TrailblazerBookingStatus.objects.get(pk=instance.pk)
        instance._previous_booking_status = previous_instance.booking.status
    else:
        instance._previous_booking_status = None


@receiver(pre_save, sender=Booking)
def cache_previous_creator_status(sender, instance, **kwargs):
    """Cache the previous status before saving."""
    if instance.pk:
        # Only retrieve the previous status if it's an existing object
        previous_instance = Booking.objects.get(pk=instance.pk)
        instance._previous_status = previous_instance.status
    else:
        instance._previous_status = None


@receiver(post_save, sender=TrailblazerBookingStatus)
def handle_trailblazer_status_change(sender, instance, created, **kwargs):
    """Handle changes in TrailblazerBookingStatus for 'confirmed' and 'cancelled' statuses."""
    if instance._previous_booking_status != 'confirmed' and instance.booking.status == 'confirmed':
        try:
            service = CalendarService()
            service.create_events_for_booking(instance.booking)
        except Exception as e:
            logger.error(f"Failed to create calendar event for booking {instance.booking.id}: {str(e)}")
    
    # Handle status change to 'declined' or 'cancelled'
    if instance._previous_booking_status == 'confirmed' and instance.booking.status in ['cancelled', 'declined', 'pending']:
        try:
            # Call the code needed when booking is cancelled
            service = CalendarService()
            service.delete_events_for_booking(instance.booking)  # Replace this with the actual cancel logic
        except Exception as e:
            logger.error(f"Failed to cancel calendar event for booking {instance.booking.id}: {str(e)}")

    # handle email notifications
    if instance._previous_booking_status in ['pending', 'confirmed'] and instance.booking.status == 'declined':
        BookingEmailService.send_session_declined_email(instance)
    if instance._previous_booking_status == 'pending' and instance.booking.status == 'confirmed':
        BookingEmailService.send_session_confirmation_email(instance)
    if instance._previous_booking_status == 'pending' and instance.booking.status == 'cancelled':
        BookingEmailService.send_session_cancellation_email(instance.booking)


@receiver(post_save, sender=Booking)
def handle_booking_creator_status_change(sender, instance, created, **kwargs):
    """Handle changes in Booking creator_status for 'confirmed' and 'cancelled' statuses."""
    # Handle status change to 'confirmed'
    if instance._previous_status != 'confirmed' and instance.status == 'confirmed':
        try:
            service = CalendarService()
            service.create_events_for_booking(instance)
        except Exception as e:
            logger.error(f"Failed to create calendar event for booking {instance.id}: {str(e)}")
    
    # Handle status change to 'declined' or 'cancelled'
    if instance._previous_status == 'confirmed' and instance.status in ['cancelled', 'declined', 'pending']:
        try:
            # Call the code needed when booking is cancelled
            service = CalendarService()
            service.delete_events_for_booking(instance)  # Replace this with the actual cancel logic
        except Exception as e:
            logger.error(f"Failed to cancel calendar event for booking {instance.id}: {str(e)}")

    # handle email notifications
    if instance._previous_status in ['pending', 'confirmed'] and instance.status == 'cancelled':
        BookingEmailService.send_session_cancellation_email(instance)
