# Generated by Django 4.2.13 on 2025-06-03 19:27

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0003_organization_per_student_hour_cap_and_more'),
        ('users', '0025_alter_highschoolstudentprofile_preferred_radius'),
    ]

    operations = [
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='grade_level',
            field=models.IntegerField(blank=True, default=None, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='hours_used',
            field=models.FloatField(default=0.0),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='organization',
            field=models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='high_school_students', to='organizations.organization'),
        ),
    ]
