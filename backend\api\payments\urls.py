from django.urls import path
from .views import PaymentLogListCreateView, TrailblazerPaymentLogListView, PaymentPeriodTrailblazerHoursView, PaymentPeriodTrailblazerHoursListView, SkippedPaymentLogListCreateView, PaymentWebhookListenerView, get_automation_setting

urlpatterns = [
    path('payment-logs/', PaymentLogListCreateView.as_view(), name='payment-log-list-create'),
    path('skipped-payment-logs/', SkippedPaymentLogListCreateView.as_view(), name='skipped-payment-logs'),
    path('payment-logs/user/', TrailblazerPaymentLogListView.as_view(), name='trailblazer-payment-log-list'),
    path('trailblazer-hours/update-all/', PaymentPeriodTrailblazerHoursView.as_view(), name='trailblazer-hours'),
    path('trailblazer-hours/', PaymentPeriodTrailblazerHoursListView.as_view(), name='trailblazer-hours-list'),
    path('update-payment-log/', PaymentWebhookListenerView.as_view(), name='update-payment-log'),
    path('automation-setting/', get_automation_setting, name='get-automation-setting')

]