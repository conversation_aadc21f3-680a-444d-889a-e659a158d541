# Generated by Django 4.2.13 on 2025-02-23 15:29

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('payments', '0002_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='SkippedPaymentLog',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('paypal_email', models.EmailField(max_length=254)),
                ('reason', models.CharField(max_length=255)),
                ('created_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Skipped Payment Log',
                'verbose_name_plural': 'Skipped Payment Logs',
                'managed': True,
            },
        ),
        migrations.AddField(
            model_name='paymentlog',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='paymentlog',
            name='updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.AddField(
            model_name='trailblazerhours',
            name='created_at',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='trailblazerhours',
            name='status',
            field=models.CharField(default='pending', max_length=50),
        ),
        migrations.AddIndex(
            model_name='paymentlog',
            index=models.Index(fields=['user'], name='payment_log_user_id_772ec1_idx'),
        ),
        migrations.AddIndex(
            model_name='paymentlog',
            index=models.Index(fields=['paypal_email'], name='payment_log_paypal__4fd084_idx'),
        ),
        migrations.AddIndex(
            model_name='trailblazerhours',
            index=models.Index(fields=['trailblazer'], name='payments_tr_trailbl_6b2b2a_idx'),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='trailblazer',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddIndex(
            model_name='skippedpaymentlog',
            index=models.Index(fields=['paypal_email'], name='payments_sk_paypal__0d818a_idx'),
        ),
        migrations.AddIndex(
            model_name='skippedpaymentlog',
            index=models.Index(fields=['trailblazer'], name='payments_sk_trailbl_31eeaa_idx'),
        ),
    ]
