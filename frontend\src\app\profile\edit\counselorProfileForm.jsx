import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { AvatarSelection } from "@/components/ui/avatarSelection"
import { FormSection } from './formSection'
import { ZipCodeInput } from '@/components/ui/zipcodeInput'

const counselorFormSchema = z.object({
    firstName: z.string().min(2),
    lastName: z.string().min(2),
    avatar: z.string(),
    highSchoolName: z.string(),
    highSchoolZipCode: z.string(),
    highSchoolCity: z.string(),
    highSchoolState: z.string(),
})

const CounselorProfileForm = ({ userData, onSubmit }) => {
    const [selectedAvatar, setSelectedAvatar] = useState(userData.profile.avatar) // Initialize with current avatar
    const form = useForm({
        resolver: zodResolver(counselorFormSchema),
        defaultValues: {
            firstName: userData.first_name,
            lastName: userData.last_name,
            avatar: "",
            highSchoolName: userData.profile.organization.name,
            highSchoolZipCode: userData.profile.organization.zip_code,
            highSchoolCity: userData.profile.organization.city,
            highSchoolState: userData.profile.organization.state,
        }
    })

    const onZipMatch = (result) => {
        form.setValue("highSchoolCity", result.city)
        form.setValue("highSchoolState", result.state)
    }

    const onZipError = (error) => {
        if (!error) {
            // reset errors
            delete form.formState.errors.zipCode
        } else {
            form.formState.errors.zipCode = { message: error }
        }
    }

    return (
        <Form {...form}>
            <form id="profile-form" onSubmit={form.handleSubmit(onSubmit)} className="flex-1 w-full md:w-3/4 lg:w-2/3">
                <div className="mb-8">
                    <FormSection title="About You" >
                        <FormField
                            control={form.control}
                            name="firstName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>First Name*</FormLabel>
                                    <FormControl>
                                        <Input {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="lastName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Last Name*</FormLabel>
                                    <FormControl>
                                        <Input {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="avatar"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Avatar</FormLabel>
                                    <FormControl>
                                        <AvatarSelection
                                            onSelect={(avatar) => {
                                                setSelectedAvatar(avatar)
                                                field.onChange(avatar)
                                            }}
                                            selectedAvatar={selectedAvatar} // Ensure correct prop
                                            initialAvatarUrl={userData.profile.avatar}
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </FormSection>
                    <FormSection title="Your Work">
                        <FormField
                            control={form.control}
                            name="highSchoolName"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Organization or High School Name</FormLabel>
                                    <FormControl>
                                        <Input {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="highSchoolZipCode"
                            render={({ field }) => (
                                <FormItem>
                                    <FormLabel>Zip Code</FormLabel>
                                    <FormControl>
                                        <ZipCodeInput
                                            field={field}
                                            form={form}
                                            onZipMatch={onZipMatch}
                                            onZipError={onZipError}
                                            disabled
                                        />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <div className="flex flex-col sm:flex-row sm:space-x-4 w-full">
                            <FormField
                                control={form.control}
                                name="highSchoolCity"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>City</FormLabel>
                                        <FormControl>
                                            <Input {...field} disabled />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="highSchoolState"
                                render={({ field }) => (
                                    <FormItem className="w-full">
                                        <FormLabel>State</FormLabel>
                                        <FormControl>
                                            <Input {...field} disabled />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                        </div>
                    </FormSection>
                </div>
            </form>
        </Form>
    )
}

export { CounselorProfileForm }
