# Generated by Django 4.2.13 on 2024-11-07 03:04

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('calendar_integration', '0006_delete_mastergooglecredentials'),
    ]

    operations = [
        migrations.CreateModel(
            name='MasterGoogleCredentials',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.TextField(blank=True, null=True)),
                ('refresh_token', models.TextField(blank=True, null=True)),
                ('token_uri', models.TextField(blank=True, null=True)),
                ('client_id', models.TextField(blank=True, null=True)),
                ('client_secret', models.TextField(blank=True, null=True)),
                ('scopes', models.TextField(blank=True, null=True)),
                ('expiry', models.DateTimeField(blank=True, null=True)),
                ('created_at', models.DateTime<PERSON>ield(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('error', models.TextField(blank=True, null=True)),
                ('singleton_key', models.BooleanField(default=True, unique=True)),
                ('service_account_enabled', models.BooleanField(default=False)),
            ],
            options={
                'verbose_name': 'Master Google Credentials',
                'verbose_name_plural': 'Master Google Credentials',
            },
        ),
    ]
