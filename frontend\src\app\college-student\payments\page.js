
"use client"

import { useState, useEffect } from "react"
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { Button } from "@/components/ui/button";
import { MainLayout } from "@/components/ui/mainLayout";
import { Loader2 } from 'lucide-react';
import { useForm } from "react-hook-form";
import { z } from "zod"
import { Input } from "@/components/ui/input"
import { zodResolver } from '@hookform/resolvers/zod'
import { useToast } from "@/hooks/use-toast"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import PaymentHistory from '@/components/ui/paymentHistory';


const formSchema = z.object({
    paypalEmail: z.string().email({ message: "Invalid email" }),
})

const MainContent = ({ user, userLoading, userError }) => {
    const [paypalEmail, setPaypalEmail] = useState(!userLoading ? user.profile.paypal_email: "");
    const [isLoading, setIsLoading] = useState(false);
    const { getToken } = useAuth()
    const { toast } = useToast()

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            paypalEmail: paypalEmail || "",
        }
    })

    useEffect(() => {
        if (userLoading || userError) return;

        const handleBeforeUnload = (event) => {
            if (!paypalEmail) {
                event.preventDefault();
                event.returnValue = '';
            }
        };

        if (!paypalEmail) {
            window.addEventListener('beforeunload', handleBeforeUnload);
        }

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [userLoading, userError, paypalEmail]);

    useEffect(() => {
        if (!paypalEmail) {
            toast({
                title: "PayPal Email Required",
                description: "Please input your PayPal email to continue.",
                status: "warning",
            });
        }
    }, [paypalEmail]);

    const onSubmit = async (data) => {
        setIsLoading(true);

        try {
            const formData = new FormData()
            formData.append('paypal_email', data.paypalEmail)
            
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/me/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Token ${getToken()}`
                },
                body: formData
            });

            if (response.ok) {

                toast({
                    title: "Success",
                    description: "PayPal email updated successfully.",
                    status: "success",
                });

                setPaypalEmail(data.paypalEmail); 
                user.profile.paypal_email = data.paypalEmail;

            } else {
                toast({
                    title: "Error",
                    description: "Failed to update PayPal email.",
                    status: "error",
                });
            }
        } catch (error) {
            console.error('Error updating PayPal email:', error);
            toast({
                title: "Error",
                description: "An error occurred while updating PayPal email.",
                status: "error",
            });
        } finally {
            setIsLoading(false);
        }
    };


    return (
        <div className="space-y-12">
            <h1 className="text-2xl md:text-3xl font-bold mb-2 text-left">Payments</h1>
            <p className="text-base md:text-lg text-gray-600 mt-1 mb-1">
                Trailblazers are paid on a weekly basis every Monday morning for hours accrued the previous week. To recieve payments, please enter enter the email associated with your PayPal account. Contact <NAME_EMAIL> if you need to update your PayPal email on record.
            </p>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                        control={form.control}
                        name="paypalEmail"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>PayPal Email</FormLabel>
                                <FormControl>
                                    <Input {...field} disabled={!!paypalEmail} className="w-1/3 min-w-[350px] rounded-lg border border-slate-300 bg-white px-2 py-1 text-sm shadow-sm outline-none transition focus:border-blue-500 focus:ring-1 focus:ring-blue-500" />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    {!paypalEmail && (
                        <Button type="submit" variant="outline"
                            className="border border-gray-800 text-gray-800" disabled={isLoading}>
                            {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Save Changes
                        </Button>
                    )}
                </form>
            </Form>
        </div>
    )
};


// Page component
const PaymentPage = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()

    return (
        <MainLayout user={user} displaySidebarMenu={true}>
            <MainContent user={user} userLoading={userLoading} userError={userError} />
            <PaymentHistory user={user} userLoading={userLoading} userError={userError}/>
        </MainLayout>
    );
};

export default withAuth(PaymentPage)


