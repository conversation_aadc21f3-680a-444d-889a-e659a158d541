from django.conf import settings
from django.conf.urls.static import static
from django.contrib import admin
from django.urls import include
from django.urls import path
from django.views import defaults as default_views

from drf_spectacular.views import SpectacularAPIView
from drf_spectacular.views import SpectacularSwaggerView

from rest_framework.authtoken.views import obtain_auth_token
from rest_framework.routers import DefaultRouter

from api.users.views import UserViewSet, CollegeStudentViewSet, CounselorAdministratorViewSet, HighSchoolStudentViewSet, AvailabilityViewSet
from api.organizations.views import OrganizationViewSet
from api.configuration.views import ConfigurationDetailView
from api.universities.views import ShortlistView


urlpatterns = [
    # Django Admin
    path(settings.ADMIN_URL, admin.site.urls),
    *static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT),
]

# API Router
router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'college-students', CollegeStudentViewSet, basename='college-student')
router.register(r'counselors', CounselorAdministratorViewSet, basename='counselor')
router.register(r'high-school-students', HighSchoolStudentViewSet, basename='high-school-student')
router.register(r'organizations', OrganizationViewSet, basename='organization')
router.register(r'availabilities', AvailabilityViewSet, basename='availability')

# API URLS
urlpatterns += [
    # API Singup endpoints for different user types
    path('api/v1/users/', include('api.users.urls')),
    # API Email Verification
    path('api/v1/email-verification/', include('api.email_verification.urls')),
    # API Payments
    path('api/v1/payments/', include('api.payments.urls')),
    # API base url
    path('api/v1/', include(router.urls)),
    path('api/v1/configuration/', include('api.configuration.urls')),
    path('api/v1/bookings/', include('api.bookings.urls')),
    # DRF auth token
    path("api/auth-token/", obtain_auth_token),
    path("api/schema/", SpectacularAPIView.as_view(), name="api-schema"),
    path(
        "api/docs/",
        SpectacularSwaggerView.as_view(url_name="api-schema"),
        name="api-docs",
    ),
    path("api/", include("api.universities.urls")),
]

if settings.DEBUG:
    # This allows the error pages to be debugged during development, just visit
    # these url in browser to see how these error pages look like.
    urlpatterns += [
        path(
            "400/",
            default_views.bad_request,
            kwargs={"exception": Exception("Bad Request!")},
        ),
        path(
            "403/",
            default_views.permission_denied,
            kwargs={"exception": Exception("Permission Denied")},
        ),
        path(
            "404/",
            default_views.page_not_found,
            kwargs={"exception": Exception("Page not Found")},
        ),
        path("500/", default_views.server_error),
    ]
    if "debug_toolbar" in settings.INSTALLED_APPS:
        import debug_toolbar

        urlpatterns = [path("__debug__/", include(debug_toolbar.urls))] + urlpatterns