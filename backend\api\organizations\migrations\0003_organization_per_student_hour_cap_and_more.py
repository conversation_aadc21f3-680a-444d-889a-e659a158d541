# Generated by Django 4.2.13 on 2025-04-25 00:01

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0002_organizationtag'),
    ]

    operations = [
        migrations.AddField(
            model_name='organization',
            name='per_student_hour_cap',
            field=models.PositiveIntegerField(default=0, help_text='Maximum hours each student can book.'),
        ),
        migrations.AddField(
            model_name='organization',
            name='total_hours_used',
            field=models.PositiveIntegerField(default=0, help_text='Total hours used by the organization.'),
        ),
        migrations.AddField(
            model_name='organization',
            name='total_subscribed_hours',
            field=models.PositiveBigIntegerField(default=0, help_text='Total hours the organization has signed up for.'),
        ),
    ]
