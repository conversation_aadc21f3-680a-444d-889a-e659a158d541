import factory
from api.users.models import CollegeStudentProfile, HighSchoolStudentProfile, CounselorAdministratorProfile
from api.organizations.models import Organization
from django.utils import timezone


class UserFactory(factory.django.DjangoModelFactory):

    class Meta:
        model = 'users.User'
        django_get_or_create = ('username',)

    id = factory.Faker('uuid4')
    username = factory.Sequence(lambda n: f'testuser{n}')
    password = factory.Faker(
        'password',
        length=10,
        special_chars=True,
        digits=True,
        upper_case=True,
        lower_case=True
    )
    email = factory.Faker('email')
    first_name = factory.Faker('first_name')
    last_name = factory.Faker('last_name')
    is_active = True
    is_staff = False
    is_superuser = False


class CollegeStudentProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CollegeStudentProfile
    
    university = 'Test University'
    interests = 'Computer Science'
    high_school_name = 'Test High School'
    high_school_zip_code = '12345'
    high_school_city = 'Test City'
    high_school_state = 'Test State'
    college_major = 'Engineering'
    college_tags = {'type': 'public'}


class CounselorAdministratorProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = CounselorAdministratorProfile
    
    position = 'Counselor'
    organization = factory.SubFactory('organizations.tests.factories.OrganizationFactory')


class HighSchoolStudentProfileFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = HighSchoolStudentProfile
    
    grade_level = 10
    organization = factory.SubFactory('api.organizations.tests.factories.OrganizationFactory')
    
    # Academic fields
    unweighted_gpa = factory.Faker('pyfloat', min_value=0.0, max_value=4.0, right_digits=2)
    weighted_gpa = factory.Faker('pyfloat', min_value=0.0, max_value=5.0, right_digits=2)
    sat_math_score = factory.Faker('random_int', min=200, max=800)
    sat_reading_score = factory.Faker('random_int', min=200, max=800)
    act_score = factory.Faker('random_int', min=1, max=36)
    high_school_name = factory.Faker('company')
    graduation_year = factory.LazyFunction(lambda: timezone.now().year + 1)
    
    # Preference fields
    intended_majors = factory.LazyFunction(lambda: ['Computer Science', 'Engineering'])
    extracurriculars = factory.LazyFunction(lambda: ['Debate Club', 'Student Government'])
    interests = factory.LazyFunction(lambda: ['Technology', 'Science'])
    college_type_preferences = factory.LazyFunction(lambda: ['Public', 'Private'])
    location_type_preferences = factory.LazyFunction(lambda: ['Urban', 'Suburban'])
    
    # Geographic preference fields
    geographic_preference_type = 'STATES'
    preferred_states = factory.LazyFunction(lambda: ['CA', 'NY', 'MA'])
    preferred_zip = factory.Faker('zipcode')
    preferred_radius = factory.Faker('random_int', min=10, max=100)
    
    # Demographic fields
    gender = factory.Faker('random_element', elements=['Female', 'Male', 'Non-binary', 'Other'])
    ethnicity = factory.Faker('random_element', elements=[choice[0] for choice in HighSchoolStudentProfile.ETHNICITY_CHOICES])
    household_income = factory.Faker('random_element', elements=[choice[0] for choice in HighSchoolStudentProfile.INCOME_CHOICES])
    financial_aid_need = factory.Faker('random_element', elements=[choice[0] for choice in HighSchoolStudentProfile.FINANCIAL_AID_CHOICES])