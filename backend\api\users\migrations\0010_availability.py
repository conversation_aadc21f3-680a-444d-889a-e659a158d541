# Generated by Django 4.2.13 on 2024-10-10 00:49

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0009_alter_highschoolstudentprofile_organization'),
    ]

    operations = [
        migrations.CreateModel(
            name='Availability',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('time_zone', models.CharField(max_length=50)),
                ('monday_available', models.BooleanField(default=False)),
                ('tuesday_available', models.BooleanField(default=False)),
                ('wednesday_available', models.BooleanField(default=False)),
                ('thursday_available', models.BooleanField(default=False)),
                ('friday_available', models.BooleanField(default=False)),
                ('saturday_available', models.BooleanField(default=False)),
                ('sunday_available', models.<PERSON>olean<PERSON>ield(default=False)),
                ('monday_time_ranges', models.J<PERSON><PERSON>ield(blank=True, default=list)),
                ('tuesday_time_ranges', models.JSONField(blank=True, default=list)),
                ('wednesday_time_ranges', models.JSONField(blank=True, default=list)),
                ('thursday_time_ranges', models.JSONField(blank=True, default=list)),
                ('friday_time_ranges', models.JSONField(blank=True, default=list)),
                ('saturday_time_ranges', models.JSONField(blank=True, default=list)),
                ('sunday_time_ranges', models.JSONField(blank=True, default=list)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='availabilities', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'ordering': ['user', 'time_zone'],
                'unique_together': {('user', 'time_zone')},
            },
        ),
    ]
