# Generated by Django 4.2.13 on 2025-02-23 15:57

from django.conf import settings
from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('payments', '0003_skippedpaymentlog_paymentlog_created_at_and_more'),
    ]

    operations = [
        migrations.RenameModel(
            old_name='TrailblazerHours',
            new_name='PaymentPeriodTrailblazerHours',
        ),
        migrations.AlterModelOptions(
            name='paymentperiodtrailblazerhours',
            options={'managed': True, 'verbose_name': 'Payment Period Trailblazer Hours', 'verbose_name_plural': 'Payment Period Trailblazer Hours'},
        ),
        migrations.RenameIndex(
            model_name='paymentperiodtrailblazerhours',
            new_name='payments_pa_trailbl_7790e6_idx',
            old_name='payments_tr_trailbl_6b2b2a_idx',
        ),
    ]
