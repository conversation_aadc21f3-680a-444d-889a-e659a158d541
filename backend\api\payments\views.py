import logging
from rest_framework import generics
from django.db.models import Su<PERSON>, F, ExpressionWrapper, fields, FloatField
from django.utils.timezone import now, make_aware
from datetime import timezone
from rest_framework.views import APIView
from rest_framework.response import Response
from rest_framework import status
from datetime import timedelta
from django.db import transaction
from api.bookings.models import Booking, TrailblazerBookingStatus
from .models import PaymentLog, PaymentPeriodTrailblazerHours, SkippedPaymentLog
from .serializers import PaymentLogSerializer, PaymentPeriodTrailblazerHoursSerializer
from api.authentication import APIKeyAuthentication
from api.permissions import HasAP<PERSON>Key
from django.contrib.auth import get_user_model
from api.users.models import CollegeStudentProfile
from django.core.exceptions import ValidationError
from django.core.validators import validate_email
from .models import SkippedPaymentLog
from .serializers import SkippedPaymentLogSerializer
from .throttles import PayPalPaymentRateThrottle
from datetime import datetime
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
from django.db import IntegrityError, DatabaseError
from requests.exceptions import ConnectionError
from django.http import JsonResponse
from .models import PaymentAutomationSetting
from rest_framework.decorators import api_view, authentication_classes, permission_classes

User = get_user_model()

# Configure logging
logger = logging.getLogger(__name__)
logging.basicConfig(level=logging.INFO)


@api_view(['GET'])
@authentication_classes([APIKeyAuthentication])
@permission_classes([HasAPIKey])
def get_automation_setting(request):
    setting = PaymentAutomationSetting.objects.first()
    if setting:
        return JsonResponse({'is_automated': setting.is_automated})
    else:
        return JsonResponse({'is_automated': False})

class PaymentWebhookListenerView(APIView):
    authentication_classes = [APIKeyAuthentication]
    permission_classes = [HasAPIKey]
    throttle_classes = [PayPalPaymentRateThrottle]

    def post(self, request):
        try:
            data = request.data
            resource_type = data.get('resource_type')
            event_type = data.get('event_type')
            error_message = data.get('error_message', '')
            time_processed = data.get('time_processed')
            time_completed = data.get('time_completed')

            if time_processed:
                time_processed = datetime.strptime(time_processed, '%Y-%m-%dT%H:%M:%SZ')
                time_processed = make_aware(time_processed, timezone=timezone.utc)
                logger.info(f"Time processed: {time_processed}")

            if time_completed:
                time_completed = datetime.strptime(time_completed, '%Y-%m-%dT%H:%M:%SZ')
                time_completed = make_aware(time_completed, timezone=timezone.utc)
                logger.info(f"Time completed: {time_completed}")

            if resource_type == 'payouts_item':
                logger.info(f"Processing item level event: {event_type} for payout_item_id: {data.get('payout_item_id')}")
                return self.handle_item_level_event(data, event_type, error_message, time_processed)
            elif resource_type == 'payouts':
                logger.info(f"Processing batch level event: {event_type} for payout_batch_id: {data.get('payout_batch_id')}")
                return self.handle_batch_level_event(data, event_type, error_message, time_completed)
            else:
                return Response({'error': 'resource_type is required and must be either "payouts_item" or "payouts"'}, status=status.HTTP_400_BAD_REQUEST)

        except Exception as e:
            logger.error(f"Error updating payment log: {e}", exc_info=True)
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10), retry=retry_if_exception_type((DatabaseError, ConnectionError)))
    def handle_item_level_event(self, data, event_type, error_message, time_processed):
        payout_item_id = data.get('payout_item_id')
        payout_batch_id = data.get('payout_batch_id')
        transaction_status = data.get('transaction_status')

        if not transaction_status:
            return Response({'error': 'transaction_status is required for item-level updates'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            with transaction.atomic():
                payment_log = PaymentLog.objects.filter(payout_item_id=payout_item_id, payout_batch_id=payout_batch_id).first()
                if payment_log:
                    logger.info(f"Found payment log for payout_item_id: {payout_item_id}")
                    if payment_log.time_processed and payment_log.time_processed > time_processed:
                        logger.info(f"Skipping update for payout_item_id: {payout_item_id} due to older time_processed")
                        return Response({'status': 'skipped'}, status=status.HTTP_200_OK)
                    elif payment_log.time_processed and payment_log.time_processed == time_processed:
                        logger.info(f"Skipping update for payout_item_id: {payout_item_id} due to same time_processed")
                        return Response({'status': 'skipped'}, status=status.HTTP_200_OK)
                else:
                    logger.warning(f"No payment log found for payout_item_id: {payout_item_id}")

                if event_type in [
                    'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
                    'PAYMENT.PAYOUTS-ITEM.HELD',
                    'PAYMENT.PAYOUTS-ITEM.UNCLAIMED'
                ]:
                    response = self.update_payment_log(payment_log, payout_item_id, transaction_status, error_message, time_processed)
                elif event_type in [
                    'PAYMENT.PAYOUTS-ITEM.FAILED',
                    'PAYMENT.PAYOUTS-ITEM.BLOCKED',
                    'PAYMENT.PAYOUTS-ITEM.CANCELED',
                    'PAYMENT.PAYOUTS-ITEM.DENIED',
                    'PAYMENT.PAYOUTS-ITEM.REFUNDED',
                    'PAYMENT.PAYOUTS-ITEM.RETURNED'
                ]:
                    response = self.handle_failed_item(payment_log, payout_item_id, transaction_status, error_message, time_processed)
                else:
                    return Response({'error': 'Unknown event type'}, status=status.HTTP_400_BAD_REQUEST)

                # Verify the state of the database after the update
                updated_payment_log = PaymentLog.objects.filter(payout_item_id=payout_item_id, payout_batch_id=payout_batch_id).first()
                if updated_payment_log:
                    logger.info(f"Verified payment log for payout_item_id: {payout_item_id} with status: {updated_payment_log.payment_status}")
                else:
                    logger.error(f"Failed to verify payment log for payout_item_id: {payout_item_id}")

                return response
        except IntegrityError as e:
            logger.warning(f"Duplicate entry detected: {str(e)}")
            return Response({'error': 'Duplicate entry detected'}, status=status.HTTP_400_BAD_REQUEST)
        except ConnectionError as e:
            logger.error(f"Connection error occurred: {str(e)}", exc_info=True)
            raise e  # Re-raise the exception to trigger the retry mechanism
        except DatabaseError as e:
            logger.error(f"Database error occurred: {str(e)}", exc_info=True)
            raise e  # Re-raise the exception to trigger the retry mechanism
        except Exception as e:
            logger.error(f"Error occurred: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10), retry=retry_if_exception_type((DatabaseError, ConnectionError)))
    def handle_batch_level_event(self, data, event_type, error_message, time_completed):
        payout_batch_id = data.get('payout_batch_id')
        batch_status = data.get('batch_status')

        if not batch_status:
            return Response({'error': 'batch_status is required for batch-level updates'}, status=status.HTTP_400_BAD_REQUEST)

        # Define the precedence of batch statuses
        status_precedence = {
            'DENIED': 1,
            'FAILED': 2,
            'SUCCESS': 3,
            'PROCESSING': 4
        }

        try:
            with transaction.atomic():
                payment_logs = PaymentLog.objects.filter(payout_batch_id=payout_batch_id)
                for payment_log in payment_logs:
                    if payment_log.time_completed and payment_log.time_completed > time_completed:
                        logger.info(f"Skipping update for payout_item_id: {payment_log.payout_item_id} due to older time_completed")
                        continue
                    elif payment_log.time_completed and payment_log.time_completed == time_completed:
                        # Compare the precedence of the current status and the new status
                        current_status_precedence = status_precedence.get(payment_log.batch_status, float('inf'))
                        new_status_precedence = status_precedence.get(batch_status, float('inf'))
                        if new_status_precedence >= current_status_precedence:
                            logger.info(f"Skipping update for payout_item_id: {payment_log.payout_item_id} due to same time_completed and lower or equal status precedence")
                            continue

                    if batch_status in ['FAILED', 'DENIED']:
                        payment_log.delete()
                        logger.info(f"Deleted payment log for payout_item_id: {payment_log.payout_item_id}")

                        skipped_payment_log, created = SkippedPaymentLog.objects.update_or_create(
                            payout_item_id=payment_log.payout_item_id,
                            defaults={
                                'user': payment_log.user,
                                'paypal_email': payment_log.paypal_email,
                                'hours': payment_log.hours,
                                'payment_period_start': payment_log.payment_period_start,
                                'payment_period_end': payment_log.payment_period_end,
                                'payment_date': payment_log.payment_date,
                                'payment_status': batch_status,
                                'batch_status': batch_status,
                                'payout_batch_id': payout_batch_id,
                                'error_message': error_message,
                                'time_completed': time_completed
                            }
                        )
                        if created:
                            logger.info(f"Created skipped payment log for payout_item_id: {payment_log.payout_item_id}")
                        else:
                            logger.info(f"Updated skipped payment log for payout_item_id: {payment_log.payout_item_id}")
                    else:
                        payment_log.batch_status = batch_status
                        payment_log.error_message = error_message
                        payment_log.time_completed = time_completed
                        payment_log.save()
                        logger.info(f"Updated payment log for payout_item_id: {payment_log.payout_item_id} with status: {batch_status}")

                logger.info(f"Updated payment logs for payout_batch_id: {payout_batch_id} with status: {batch_status}")

            return Response({'status': 'success'}, status=status.HTTP_200_OK)
        except IntegrityError as e:
            logger.warning(f"Duplicate entry detected: {str(e)}")
            return Response({'error': 'Duplicate entry detected'}, status=status.HTTP_400_BAD_REQUEST)
        except ConnectionError as e:
            logger.error(f"Connection error occurred: {str(e)}", exc_info=True)
            raise e  # Re-raise the exception to trigger the retry mechanism
        except DatabaseError as e:
            logger.error(f"Database error occurred: {str(e)}", exc_info=True)
            raise e  # Re-raise the exception to trigger the retry mechanism
        except Exception as e:
            logger.error(f"Error occurred: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update_payment_log(self, payment_log, payout_item_id, transaction_status, error_message, time_processed):
        if payment_log:
            payment_log.payment_status = transaction_status
            payment_log.error_message = error_message
            payment_log.time_processed = time_processed
            payment_log.save()
            logger.info(f"Updated payment log for payout_item_id: {payout_item_id} with status: {transaction_status}")
        else:
            logger.warning(f"No payment log found for payout_item_id: {payout_item_id}")
        return Response({'status': 'success'}, status=status.HTTP_200_OK)

    def handle_failed_item(self, payment_log, payout_item_id, transaction_status, error_message, time_processed):
        with transaction.atomic():
            if payment_log:
                # Delete the PaymentLog entry
                payment_log.delete()
                logger.info(f"Deleted payment log for payout_item_id: {payout_item_id}")

                # Create or update the SkippedPaymentLog entry
                skipped_payment_log, created = SkippedPaymentLog.objects.update_or_create(
                    payout_item_id=payout_item_id,
                    defaults={
                        'user': payment_log.user,
                        'paypal_email': payment_log.paypal_email,
                        'hours': payment_log.hours,
                        'payment_period_start': payment_log.payment_period_start,
                        'payment_period_end': payment_log.payment_period_end,
                        'payment_date': payment_log.payment_date,
                        'payment_status': transaction_status,
                        'batch_status': payment_log.batch_status,
                        'payout_batch_id': payment_log.payout_batch_id,
                        'error_message': error_message,
                        'time_processed': time_processed
                    }
                )
                if created:
                    logger.info(f"Created skipped payment log for payout_item_id: {payout_item_id} with status: {transaction_status}")
                else:
                    logger.info(f"Updated skipped payment log for payout_item_id: {payment_log.payout_item_id} with status: {transaction_status}")
            else:
                logger.warning(f"No payment log found for payout_item_id: {payout_item_id}")

        return Response({'status': 'success'}, status=status.HTTP_200_OK)

class PaymentLogListCreateView(generics.ListCreateAPIView):
    queryset = PaymentLog.objects.all()
    serializer_class = PaymentLogSerializer
    authentication_classes = [APIKeyAuthentication]
    permission_classes = [HasAPIKey]
    throttle_classes = [PayPalPaymentRateThrottle]

    def create(self, request, *args, **kwargs):
        user_id = request.data.get('user')

        if not user_id:
            logger.warning("User ID is required but not provided.")
            return Response({'error': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.warning(f"User with ID {user_id} does not exist.")
            return Response({'error': f"User with ID {user_id} does not exist."}, status=status.HTTP_400_BAD_REQUEST)

        # Log the request data for debugging
        logger.debug(f"Request data before setting user: {request.data}")

        # Set the 'user' field in the request data to the User instance
        request.data['user'] = user.id

        # Log the request data after setting the user
        logger.debug(f"Request data after setting user: {request.data}")

        try:
            # Call the parent class's create method to handle the creation of the PaymentLog instance
            response = super().create(request, *args, **kwargs)
            logger.info(f"Payment Log created successfully for user: {user}")
            return response
        except Exception as e:
            logger.error(f"Error creating Payment Log: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class SkippedPaymentLogListCreateView(generics.ListCreateAPIView):
    queryset = SkippedPaymentLog.objects.all()
    serializer_class = SkippedPaymentLogSerializer
    authentication_classes = [APIKeyAuthentication]
    permission_classes = [HasAPIKey]
    throttle_classes = [PayPalPaymentRateThrottle]

    def create(self, request, *args, **kwargs):
        user_id = request.data.get('user')

        if not user_id:
            logger.warning("User ID is required but not provided.")
            return Response({'error': 'User ID is required'}, status=status.HTTP_400_BAD_REQUEST)

        try:
            user = User.objects.get(id=user_id)
        except User.DoesNotExist:
            logger.warning(f"User with ID {user_id} does not exist.")
            return Response({'error': f"User with ID {user_id} does not exist."}, status=status.HTTP_400_BAD_REQUEST)

        # Log the request data for debugging
        logger.debug(f"Request data before setting user: {request.data}")

        # Set the 'user' field in the request data to the User instance
        request.data['user'] = user.id

        # Log the request data after setting the user
        logger.debug(f"Request data after setting user: {request.data}")

        try:
            # Call the parent class's create method to handle the creation of the PaymentLog instance
            response = super().create(request, *args, **kwargs)
            logger.info(f" Skipped Payment Log created successfully for user: {user}")
            return response
        except Exception as e:
            logger.error(f"Error creating Skipped Payment Log: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
        
class TrailblazerPaymentLogListView(generics.ListAPIView):
    serializer_class = PaymentLogSerializer

    def get_queryset(self):
        user_id = self.request.query_params.get('user')
        if user_id:
            logger.info(f"Fetching payment logs for user ID: {user_id}")
            return PaymentLog.objects.filter(user_id=user_id)
        logger.info("No user ID provided, returning empty queryset.")
        return PaymentLog.objects.none()  
    
class PaymentPeriodTrailblazerHoursListView(generics.ListAPIView):
    queryset = PaymentPeriodTrailblazerHours.objects.all().order_by('id')  # Add ordering by 'id' or any other field
    serializer_class = PaymentPeriodTrailblazerHoursSerializer
    authentication_classes = [APIKeyAuthentication]
    permission_classes = [HasAPIKey]
    
def calculate_payment_period(pay_roll_date=None):
    if pay_roll_date is None:
        pay_roll_date = now().astimezone(timezone.utc)
        
    logger.debug(f"Calculating payment period for payroll run date: {pay_roll_date}")
    # Calculate the most recent Monday at 12:00 AM
    current_monday = pay_roll_date - timedelta(days=pay_roll_date.weekday())
    current_monday = current_monday.replace(hour=0, minute=0, second=0, microsecond=0)
    
    # Calculate the previous Monday at 12:00 AM
    previous_monday = current_monday - timedelta(days=7)

    logger.debug(f"Payment period start: {previous_monday}, end: {current_monday}")
    return previous_monday, current_monday

def calculate_total_hours(payment_period_start, payment_period_end):
    logger.debug(f"Calculating total hours from {payment_period_start} to {payment_period_end}")
    trailblazer_hours = TrailblazerBookingStatus.objects.filter(
                booking__start_time__gte=payment_period_start,
                booking__start_time__lt=payment_period_end,
                booking__creator_status='confirmed', 
                status='confirmed'
            ).values('trailblazer').annotate(
                total_hours=Sum(
                    ExpressionWrapper(
                        (F('booking__end_time') - F('booking__start_time')),
                        output_field=fields.DurationField()
                    )
                )
            )
    if not trailblazer_hours:
        logger.info("No trailblazer hours found for the given period.")
        return []
    logger.debug(f"Trailblazer hours calculated: {trailblazer_hours}")
    return trailblazer_hours

def convert_total_hours(trailblazer_hours):
    logger.debug("Converting total hours from timedelta to float (hours).")
    for entry in trailblazer_hours:
        if entry['total_hours'] is None:
            entry['total_hours'] = 0.0
        else:
            entry['total_hours'] = entry['total_hours'].total_seconds() / 3600.0
    logger.debug(f"Converted trailblazer hours: {trailblazer_hours}")
    return trailblazer_hours

def update_payment_period_trailblazer_hours_table(trailblazer_hours, payment_period_start, payment_period_end):
    logger.info("Updating PaymentPeriodTrailblazerHours table.")
    with transaction.atomic():
        PaymentPeriodTrailblazerHours.objects.all().delete()
        for entry in trailblazer_hours:
            trailblazer_id = entry['trailblazer']
            try:
                trailblazer = User.objects.get(id=trailblazer_id)
            except User.DoesNotExist:
                logger.error(f"User with ID {trailblazer_id} does not exist.")
                continue

            total_hours = round(entry['total_hours'], 2) if entry['total_hours'] else 0
            PaymentPeriodTrailblazerHours.objects.update_or_create(
                user=trailblazer,
                paypal_email=trailblazer.college_student_profile.paypal_email,
                defaults={
                    'total_hours': total_hours,
                    'payment_period_start': payment_period_start,
                    'payment_period_end': payment_period_end,
                    'updated_at': now()
                }
            )
    logger.info("PaymentPeriodTrailblazerHours table updated successfully.")

class PaymentPeriodTrailblazerHoursView(APIView):
    authentication_classes = [APIKeyAuthentication]
    permission_classes = [HasAPIKey]

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=2, max=10), retry=retry_if_exception_type((DatabaseError, ConnectionError)))
    def get(self, request):
        try:
            # Calculate the date range
            payment_period_start, payment_period_end = calculate_payment_period()

            # Calculate total hours
            trailblazer_hours = calculate_total_hours(payment_period_start, payment_period_end)

            # Convert total hours from timedelta to float (hours)
            trailblazer_hours = convert_total_hours(trailblazer_hours)

            # Update the TrailblazerHours table
            update_payment_period_trailblazer_hours_table(trailblazer_hours, payment_period_start, payment_period_end)

            # Serialize the data
            trailblazer_hours_data = PaymentPeriodTrailblazerHours.objects.all()
            serializer = PaymentPeriodTrailblazerHoursSerializer(trailblazer_hours_data, many=True)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except IntegrityError as e:
            logger.warning(f"Duplicate entry detected: {str(e)}")
            return Response({'error': 'Duplicate entry detected'}, status=status.HTTP_400_BAD_REQUEST)
        except ConnectionError as e:
            logger.error(f"Connection error occurred: {str(e)}", exc_info=True)
            raise e  # Re-raise the exception to trigger the retry mechanism
        except DatabaseError as e:
            logger.error(f"Database error occurred: {str(e)}", exc_info=True)
            raise e  # Re-raise the exception to trigger the retry mechanism
        except Exception as e:
            logger.error(f"Error occurred: {str(e)}", exc_info=True)
            return Response({'error': str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)



