from django.contrib import admin
from django.utils import timezone
from datetime import timedelta
from .models import Booking, TrailblazerBookingStatus

class UpcomingBookingsFilter(admin.SimpleListFilter):
    title = 'Upcoming Bookings'
    parameter_name = 'upcoming_bookings'

    def lookups(self, request, model_admin):
        return (
            ('upcoming', 'Upcoming Bookings'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'upcoming':
            return queryset.filter(start_time__gte=timezone.now())
        return queryset

class CurrentWeekBookingsFilter(admin.SimpleListFilter):
    title = 'Current Week Bookings'
    parameter_name = 'current_week_bookings'

    def lookups(self, request, model_admin):
        return (
            ('current_week', 'Current Week Bookings'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'current_week':
            start_of_week = timezone.now().date() - timedelta(days=timezone.now().date().weekday())
            end_of_week = start_of_week + timedelta(days=6)
            return queryset.filter(start_time__date__gte=start_of_week, start_time__date__lte=end_of_week)
        return queryset

class TrailblazerBookingStatusUpcomingBookingsFilter(admin.SimpleListFilter):
    title = 'Upcoming Bookings'
    parameter_name = 'upcoming_bookings'

    def lookups(self, request, model_admin):
        return (
            ('upcoming', 'Upcoming Bookings'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'upcoming':
            return queryset.filter(booking__start_time__gte=timezone.now())
        return queryset
    
class TrailblazerBookingStatusCurrentWeekBookingsFilter(admin.SimpleListFilter):
    title = 'Current Week Bookings'
    parameter_name = 'current_week_bookings'

    def lookups(self, request, model_admin):
        return (
            ('current_week', 'Current Week Bookings'),
        )

    def queryset(self, request, queryset):
        if self.value() == 'current_week':
            start_of_week = timezone.now().date() - timedelta(days=timezone.now().date().weekday())
            end_of_week = start_of_week + timedelta(days=6)
            return queryset.filter(booking__start_time__date__gte=start_of_week, booking__start_time__date__lte=end_of_week)
        return queryset

@admin.register(Booking)
class BookingAdmin(admin.ModelAdmin):
    list_display = ('id', 'get_trailblazers', 'booked_by', 'start_time', 'end_time', 'creator_status', 'created_at', 'proposed_times', 'proposed_time_confirmed')
    list_filter = ('creator_status', 'created_at', UpcomingBookingsFilter, CurrentWeekBookingsFilter)
    search_fields = ('trailblazer_statuses__trailblazer__email', 'booked_by__email', 'message')

    def get_trailblazers(self, obj):
        # Retrieves trailblazers associated with the booking through TrailblazerBookingStatus
        return ", ".join([str(status.trailblazer) for status in obj.trailblazer_statuses.all()])

    get_trailblazers.short_description = 'Trailblazers'

@admin.register(TrailblazerBookingStatus)
class TrailblazerBookingStatusAdmin(admin.ModelAdmin):
    list_display = ('booking', 'trailblazer', 'status', 'updated_at')
    list_filter = ('status', 'updated_at', TrailblazerBookingStatusUpcomingBookingsFilter, TrailblazerBookingStatusCurrentWeekBookingsFilter)
    search_fields = ('booking__id', 'trailblazer__email')
