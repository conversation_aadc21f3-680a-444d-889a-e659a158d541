"use client"

import { useState, useEffect, useMemo } from 'react'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm, useFieldArray } from "react-hook-form"
import { z } from "zod"
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, DialogTitle } from "@/components/ui/dialog"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Button } from "@/components/ui/button"
import { Calendar, Loader2, X, Clock } from 'lucide-react'
import { getFullTimeZoneName } from "@/lib/utils"
import { Input } from "@/components/ui/input"

// Custom validation function to check if the date and time are in the past
const isFutureDateTime = (date, time) => {
  const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
  const currentDateTime = new Date();
  const proposedDateTime = new Date(`${date}T${time}`);

  const proposedDateTimeLocal = new Date(proposedDateTime.toLocaleString("en-US", { timeZone: userTimeZone }));

  return proposedDateTimeLocal > currentDateTime;
};

// Generate 30-minute interval times in 12-hour format
const generateTimeOptions = () => {
  const times = [];
  for (let hour = 0; hour < 24; hour++) {
    for (let minute = 0; minute < 60; minute += 30) {
      const period = hour < 12 ? 'AM' : 'PM';
      const hour12 = hour % 12 === 0 ? 12 : hour % 12;
      const time = `${String(hour12).padStart(2, '0')}:${String(minute).padStart(2, '0')} ${period}`;
      times.push(time);
    }
  }
  return times;
};

const timeOptions = generateTimeOptions();

// Custom TimePicker component
const TimePicker = ({ value, onChange, ...props }) => (
  <div className="relative">
    <select value={value} onChange={onChange} {...props} className="flex h-12 w-40 rounded-md border border-input bg-white py-2 px-2 text-sm shadow-sm transition-colors appearance-none placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50 pl-7">
      <option value="">_ _ : _ _  _ _</option>
      {timeOptions.map((time) => (
        <option key={time} value={time}>
          {time}
        </option>
      ))}
    </select>
    <Clock className="absolute right-8 top-1/2 transform -translate-y-1/2 w-4 h-4 text-grey-400 pointer-events-none" />
  </div>
);

// Utility function to convert 12-hour time to 24-hour time
const convertTo24HourFormat = (time) => {
  const [timePart, modifier] = time.split(' ');
  let [hours, minutes] = timePart.split(':');

  if (hours === '12') {
    hours = '00';
  }

  if (modifier === 'PM') {
    hours = parseInt(hours, 10) + 12;
  }

  return `${String(hours).padStart(2, '0')}:${minutes}`;
};

// CancelSessionForm component
const CancelSessionForm = ({ isDecline, onSubmit, isLoading, onClose }) => {

  // Define the form schema dynamically using isDecline
  const formSchema = z.object({
    cancelReason: z.string()
      .min(10, `Please provide more details about why you’re ${isDecline ? 'declining' : 'canceling'} the session`)
      .max(280, "Message cannot exceed 280 characters"),
    proposedTimes: z.array(z.object({
      date: z.string(),
      time: z.string()
    }))
      .max(3, "You can propose up to 3 times")
  });

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      cancelReason: "",
      proposedTimes: [{ date: "", time: "" }]
    }
  });

  const { fields, append, remove } = useFieldArray({
    control: form.control,
    name: "proposedTimes"
  });

  const [currentCharCount, setCurrentCharCount] = useState(0);
  const [customErrors, setCustomErrors] = useState({});
  const [loading, setLoading] = useState(false);

  // Update character count when the text area value changes
  useEffect(() => {
    const subscription = form.watch((value, { name }) => {
      if (name === 'cancelReason') {
        setCurrentCharCount(value.cancelReason.length);
      }
    });
    return () => subscription.unsubscribe();
  }, [form]);

  const handleSubmit = async (data) => {
    const errors = {};

    // Validate proposedTimes
    data.proposedTimes.forEach((proposedTime, index) => {
        if (!proposedTime.date || !proposedTime.time) {
            errors[`proposedTimes.${index}`] = "Both date and time are required";
        } else if (!isFutureDateTime(proposedTime.date, convertTo24HourFormat(proposedTime.time))) {
            errors[`proposedTimes.${index}.time`] = "Time cannot be in the past";
        }
    });

    if (Object.keys(errors).length > 0) {
        setCustomErrors(errors);
    } else {
        // Convert time fields to 24-hour format
        data.proposedTimes = data.proposedTimes.map(proposedTime => ({
            ...proposedTime,
            time: convertTo24HourFormat(proposedTime.time)
        }));

        // Remove proposedTimes if it's empty
        if (!data.proposedTimes || data.proposedTimes.length === 0) {
            delete data.proposedTimes;
        }

        setCustomErrors({});
        setLoading(true);
        await new Promise(resolve => setTimeout(resolve, 0)); // Ensure state update is processed
        try {
            await onSubmit(data);
            onClose(); // Close the dialog after successful submission
        } finally {
            setLoading(false);
        }
    }
  };

  return (
    <>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
          <FormField
            control={form.control}
            name="cancelReason"
            render={({ field }) => (
              <FormItem>
                <FormLabel className="text-sm font-medium text-gray-700">Let them know why you&apos;re {isDecline ? 'declining' : 'canceling'} the session</FormLabel>
                <FormControl>
                  <Textarea
                    {...field}
                    placeholder="Are you looking to reschedule? Please provide details about your cancellation."
                    className="resize-none w-full border border-gray-300 rounded-lg p-2 text-sm min-h-[100px]"
                    maxLength={280}
                  />
                </FormControl>
                <FormMessage />
                <p className="text-sm text-gray-500 mt-1">{currentCharCount} / 280</p>
              </FormItem>
            )}
          />

          <div className="space-y-4 overflow-auto max-h-96">
            <FormLabel className="text-sm font-semibold text-gray-800">Propose New Meeting Times (Optional):</FormLabel>
            {fields.map((field, index) => (
              <div key={field.id} className="flex flex-col space-y-2 pb-4 pl-2 items-center relative">
                <div className="flex space-x-2 items-center w-full">
                  <FormField
                    control={form.control}
                    name={`proposedTimes.${index}.date`}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <Input type="date" {...field} className="text-sm relative w-full border border-gray-300 rounded-md text-center" />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                  <FormField
                    control={form.control}
                    name={`proposedTimes.${index}.time`}
                    render={({ field }) => (
                      <FormItem>
                        <FormControl>
                          <TimePicker {...field} />
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />

                  <Button
                    type="button"
                    onClick={() => remove(index)}
                    className="text-sm p-0"
                    style={{ color: '#6B7280', background: 'transparent', border: 'none', boxShadow: 'none' }}
                  >
                    <X className="w-4 h-4" />
                  </Button>

                </div>
                {customErrors[`proposedTimes.${index}`] && (
                  <p className="text-red-500 text-xs mt-1">
                    {customErrors[`proposedTimes.${index}`]}
                  </p>
                )}
                {customErrors[`proposedTimes.${index}.time`] && (
                  <p className="text-red-500 text-xs mt-1">
                    {customErrors[`proposedTimes.${index}.time`]}
                  </p>
                )}
              </div>
            ))}
            {fields.length < 3 && (
              <Button type="button" onClick={() => append({ date: "", time: "" })} className="text-sm mt-4">
                Add another time
              </Button>
            )}
          </div>

          {isLoading || loading ? (
            <Loader2 className="w-5 h-5 animate-spin" />
          ) : (
            <Button
              type="submit"
              className="w-full bg-gray-900 text-white rounded-lg px-4 py-2 flex items-center justify-center space-x-2 hover:bg-gray-600 transition-colors"
              disabled={isLoading || loading}
            >
              <span>{isDecline ? 'Decline' : 'Cancel'} Session</span>
              <Calendar className="w-5 h-5" />
            </Button>
          )}
        </form>
      </Form>
    </>
  );
};

// Main CancelSessionDialog component
const CancelSessionDialog = ({ isDecline, isOpen, onClose, onSubmit, bookingNames, sessionDate, sessionTime, isLoading, error }) => {
  const currentTimezone = useMemo(() => getFullTimeZoneName(), [])
  // Function to handle dialog close
  const handleClose = () => {
    if (typeof onClose === 'function') {
      onClose()
    }
  }

  return (
    <Dialog open={isOpen} onOpenChange={handleClose}>
      <DialogContent className="sm:max-w-lg p-8">
        <DialogHeader>
          <div className="flex justify-between items-center">
            <DialogTitle className="text-xl font-bold">
              {isDecline ? 'Declining' : 'Canceling'} the session with {bookingNames}
            </DialogTitle>
          </div>
        </DialogHeader>
        <p className="text-sm mb-4">
          <div>
            {sessionDate}
          </div>
          <div>
            {sessionTime}, {currentTimezone}
          </div>
        </p>
        <CancelSessionForm isDecline={isDecline} onSubmit={onSubmit} isLoading={isLoading} onClose={handleClose} />
        {error && (
          <p className="text-red-500 text-sm mt-4">{error}</p>
        )}
      </DialogContent>
    </Dialog>
  )
}

export default CancelSessionDialog;
