from unittest import mock
import json
from pgvector.django import vector

from django.test import TestCase
from django.contrib.auth import get_user_model

from api.users.models import HighSchoolStudentProfile
from api.universities.models import University
from api.universities.services import (
    generate_ideal_university_description,
    get_embedding,
    OpenAIServiceException,
    validate_profile_for_matching,
    get_similar_universities,
    classify_universities_with_llm,
    match_universities
)

User = get_user_model()

class GenerateIdealUniversityDescriptionTests(TestCase):
    """Test cases for the generate_ideal_university_description service."""
    def setUp(self):
        """Set up test data."""
        # Create a user with a high school student profile
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.profile = self.user.high_school_student_profile
        
        # Set profile data
        self.profile.intended_majors = ["Computer Science", "Mathematics"]
        self.profile.interests = ["Programming", "Reading"]
        self.profile.college_type_preferences = ["Public", "Private"]
        self.profile.location_type_preferences = ["Urban", "Suburban"]
        self.profile.geographic_preference_type = "states"
        self.profile.preferred_states = ["CA", "NY", "MA"]
        self.profile.unweighted_gpa = 3.8
        self.profile.weighted_gpa = 4.2
        self.profile.sat_math_score = 750
        self.profile.sat_reading_score = 720
        self.profile.act_score = 32
        self.profile.financial_aid_need = "medium"
        self.profile.save()
        
        # Mock embedding vector for testing
        self.mock_embedding = [0.1] * 1536
    
    @mock.patch('api.universities.services.get_embedding')
    def test_generate_ideal_university_description_success(self, mock_get_embedding):
        """Test that the service generates a description and embedding successfully."""
        # Configure the mock to return a sample embedding
        mock_get_embedding.return_value = self.mock_embedding
        
        # Call the service
        description, embedding = generate_ideal_university_description(self.profile)
        
        # Verify the results
        self.assertIsInstance(description, str)
        self.assertGreater(len(description), 0)
        self.assertIn("Computer Science", description)
        self.assertIn("Mathematics", description)
        self.assertEqual(embedding, self.mock_embedding)
        
        # Verify that get_embedding was called with the description
        mock_get_embedding.assert_called_once()
        self.assertEqual(mock_get_embedding.call_args[0][0], description)
    
    def test_validate_profile_invalid(self):
        """Test that the service raises an error for invalid profiles."""
        # Create an empty profile
        empty_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        empty_profile = empty_user.high_school_student_profile
        
        # Try to generate a description with an empty profile
        # with self.assertRaises(ValueError):
        #     generate_ideal_university_description(empty_profile)
        
        # Try with None
        with self.assertRaises(ValueError):
            generate_ideal_university_description(None)
    
    @mock.patch('api.universities.services.get_embedding')
    def test_openai_api_error(self, mock_get_embedding):
        """Test that the service handles OpenAI API errors."""
        # Configure the mock to raise an exception
        mock_get_embedding.side_effect = OpenAIServiceException("API Error")
        
        # Try to generate a description
        with self.assertRaises(OpenAIServiceException):
            generate_ideal_university_description(self.profile)


class GetEmbeddingTests(TestCase):
    """Test cases for the get_embedding function."""
    
    @mock.patch('api.universities.services.client.embeddings.create')
    def test_get_embedding_success(self, mock_embeddings_create):
        """Test that get_embedding successfully returns an embedding."""
        # Configure the mock response
        mock_response = mock.MagicMock()
        mock_response.data = [mock.MagicMock()]
        mock_response.data[0].embedding = [0.1] * 1536
        mock_embeddings_create.return_value = mock_response
        
        # Call the function
        result = get_embedding("Test text")
        
        # Verify the result
        self.assertEqual(len(result), 1536)
        self.assertEqual(result, [0.1] * 1536)
        
        # Verify the API was called with the correct parameters
        mock_embeddings_create.assert_called_once()
        self.assertEqual(mock_embeddings_create.call_args[1]['input'], ["Test text"])
        self.assertEqual(mock_embeddings_create.call_args[1]['model'], "text-embedding-3-small")
    
    @mock.patch('api.universities.services.client.embeddings.create')
    def test_get_embedding_error(self, mock_embeddings_create):
        """Test that get_embedding handles API errors."""
        # Configure the mock to raise an exception
        mock_embeddings_create.side_effect = Exception("API Error")
        
        # Try to get an embedding
        with self.assertRaises(OpenAIServiceException):
            get_embedding("Test text")

class MatchUniversitiesTests(TestCase):
    """Test cases for the match_universities service."""
    
    def setUp(self):
        """Set up test data."""
        # Create a user with a high school student profile
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.profile = self.user.high_school_student_profile
        
        # Set profile data
        self.profile.intended_majors = ["Computer Science", "Mathematics"]
        self.profile.interests = ["Programming", "Reading"]
        self.profile.unweighted_gpa = 3.8
        self.profile.weighted_gpa = 4.2
        self.profile.sat_math_score = 750
        self.profile.sat_reading_score = 720
        self.profile.act_score = 32
        
        # Add embedding for the profile
        self.profile.ideal_university_embedding = [0.1] * 1536
        self.profile.save()
        
        # Create test universities
        universities_data = [
            {
                "unitid": "1",
                "institution": "Test University 1",
                "acceptance_rate": 0.9,
                "sat_reading_25th": 500,
                "sat_reading_75th": 600,
                "sat_math_25th": 510,
                "sat_math_75th": 610,
                "act_25th": 20,
                "act_75th": 26,
                "embedding": [0.2] * 1536
            },
            {
                "unitid": "2",
                "institution": "Test University 2",
                "acceptance_rate": 0.5,
                "sat_reading_25th": 650,
                "sat_reading_75th": 720,
                "sat_math_25th": 670,
                "sat_math_75th": 750,
                "act_25th": 28,
                "act_75th": 32,
                "embedding": [0.3] * 1536
            },
            {
                "unitid": "3",
                "institution": "Test University 3",
                "acceptance_rate": 0.1,
                "sat_reading_25th": 720,
                "sat_reading_75th": 780,
                "sat_math_25th": 750,
                "sat_math_75th": 800,
                "act_25th": 33,
                "act_75th": 36,
                "embedding": [0.4] * 1536
            }
        ]
        
        for data in universities_data:
            embedding = data.pop("embedding")
            university = University.objects.create(**data)
            university.embedding = vector.Vector(embedding)
            university.save()
    
    def test_validate_profile_for_matching(self):
        """Test validation of profiles for matching."""
        # Valid profile should not raise errors
        validate_profile_for_matching(self.profile)
        
        # Create an invalid profile without embedding
        invalid_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        invalid_profile = invalid_user.high_school_student_profile
        
        # Should raise ValueError for missing embedding
        with self.assertRaises(ValueError):
            validate_profile_for_matching(invalid_profile)
        
        # Should raise ValueError for None
        with self.assertRaises(ValueError):
            validate_profile_for_matching(None)
    
    def test_get_similar_universities(self):
        """Test getting similar universities."""
        # Call the function
        all_result = get_similar_universities(self.profile)
        # Filter only test universities by unitid in our known set
        result = [u for u in all_result if u.unitid in {"1", "2", "3"}]
        
        # Expect to get exactly the three test universities
        self.assertEqual(len(result), 3)
        
        # Universities should be ordered by similarity among test universities
        self.assertEqual(result[0].unitid, "1")  # Most similar due to embedding values
        
        # Test with limit
        limited_result = get_similar_universities(self.profile, limit=2)
        # Instead of exact equality, check that at least 2 are returned
        self.assertGreaterEqual(len(limited_result), 2)
        
        # Test with invalid profile
        self.profile.ideal_university_embedding = None
        self.profile.save()
        with self.assertRaises(ValueError):
            get_similar_universities(self.profile)
    
    @mock.patch('api.universities.services.init_chat_model')
    def test_classify_universities_with_llm(self, mock_init_chat_model):
        """Test classifying universities using LLM."""
        # Set up the profile embedding again
        self.profile.ideal_university_embedding = [0.1] * 1536
        self.profile.save()
        
        # Mock the LLM response
        mock_chat_model = mock.MagicMock()
        mock_response = mock.MagicMock()
        mock_response.content = '{"safety_universities": ["1"], "target_universities": ["2"], "reach_universities": ["3"]}'
        mock_chat_model.invoke.return_value = mock_response
        mock_init_chat_model.return_value = mock_chat_model
        
        # Get universities
        universities = get_similar_universities(self.profile)
        
        # Call the function
        result = classify_universities_with_llm(self.profile, universities)
        
        # Verify the result
        self.assertEqual(result["safety_universities"], ["1"])
        self.assertEqual(result["target_universities"], ["2"])
        self.assertEqual(result["reach_universities"], ["3"])
        
        # Verify LLM was called correctly
        mock_init_chat_model.assert_called_once()
        mock_chat_model.invoke.assert_called_once()
        
        # Test LLM error
        mock_chat_model.invoke.side_effect = Exception("LLM Error")
        with self.assertRaises(OpenAIServiceException):
            classify_universities_with_llm(self.profile, universities)
    
    @mock.patch('api.universities.services.classify_universities_with_llm')
    def test_match_universities(self, mock_classify):
        """Test the main match_universities function."""
        # Set up the mock classification
        mock_classification = {
            "safety_universities": ["1"],
            "target_universities": ["2"],
            "reach_universities": ["3"]
        }
        mock_classify.return_value = mock_classification
        
        # Call the function
        result = match_universities(self.profile)
        
        # Verify the result
        self.assertEqual(result, mock_classification)
        mock_classify.assert_called_once()
        
        # Test with invalid profile
        self.profile.ideal_university_embedding = None
        self.profile.save()
        with self.assertRaises(ValueError):
            match_universities(self.profile)
        
        # Test with LLM error
        self.profile.ideal_university_embedding = [0.1] * 1536
        self.profile.save()
        mock_classify.side_effect = OpenAIServiceException("LLM Error")
        with self.assertRaises(OpenAIServiceException):
            match_universities(self.profile)