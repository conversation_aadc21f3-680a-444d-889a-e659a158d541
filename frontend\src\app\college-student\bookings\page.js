"use client"

import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { MainLayout } from "@/components/ui/mainLayout"
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import BookingList from "@/components/ui/BookingList";
import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation'


// Statistics card component
const StatisticsCard = ({ title, value, unit, description, loading }) => (
    <Card className="bg-white p-4 rounded-lg shadow w-1/2 lg:w-1/4">
      <CardContent className="p-0">
        {loading ? (
          <>
            <Skeleton className="h-4 w-1/3 mb-2" />
            <div className="flex items-baseline mt-1">
              <Skeleton className="h-6 w-1/4" />
              <Skeleton className="h-4 w-1/6 ml-1" />
            </div>
            <Skeleton className="h-4 w-3/4 mt-1" />
          </>
        ) : (
          <>
            <h2 className="text-xs">{title}</h2>
            <div className="flex items-baseline mt-1">
              <span className="text-xl font-bold">{value}</span>
              <span className="ml-1 text-xs">{unit}</span>
            </div>
            <p className="mt-1 text-xs">{description}</p>
          </>
        )}
      </CardContent>
    </Card>
  );

// Main content component
const MainContent = ({ user, userLoading, userError }) => {
    const router = useRouter()
    const [totalHours, setTotalHours] = useState(0)
    const [totalSchoolsConnected, setTotalSchoolsConnected] = useState(0)
    const [statsLoading, setStatsLoading] = useState(true)
    const [statsError, setStatsError] = useState(null)
    const { getToken } = useAuth()

    useEffect(() => {
        if (userLoading || userError) return

        if(user.profile.paypal_email == "" || user.profile.paypal_email == null){
          router.push('/college-student/payments')
        }
        const fetchImpactStatistics = async () => {
            setStatsLoading(true)
            setStatsError(null)
            try {
                const authToken = getToken()
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/trailblazer/impact-statistics/`, {
                    headers: {
                        'Authorization': `Token ${authToken}`,
                        'Content-Type': 'application/json',
                    },
                })
                if (!response.ok) {
                    throw new Error('Failed to fetch impact statistics')
                }
                const data = await response.json()
                setTotalHours(data.total_hours)
                setTotalSchoolsConnected(data.total_schools_connected)
            } catch (error) {
                setStatsError(error.message)
            } finally {
                setStatsLoading(false)
            }
        }
        fetchImpactStatistics()
    }, [userLoading, userError, user])

    return (
        <div className="space-y-12">
            <h1 className="text-2xl md:text-3xl font-bold mb-8 text-left">Bookings</h1>
            {statsError && <p className="text-red-500 text-center mt-4">{statsError}</p>}
            <div className="flex flex-row gap-6">
                <StatisticsCard
                    title="You've dedicated"
                    value={totalHours}
                    unit="hours"
                    description="to making a difference through peer mentoring"
                    loading={statsLoading}
                />
                <StatisticsCard
                    title="You've connected with"
                    value={totalSchoolsConnected}
                    unit="schools & community organizations"
                    description="and their students"
                    loading={statsLoading}
                />
            </div>
            <BookingList user={user} userLoading={userLoading} userError={userError}/>
        </div>
    );
};

// Page component
const BookingsPage = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()

    return (
        <MainLayout user={user} displaySidebarMenu={true}>
            <MainContent user={user} userLoading={userLoading} userError={userError} />
        </MainLayout>
    );
};

export default withAuth(BookingsPage);
