from django.urls import reverse
from django.utils import timezone
from datetime import timedelta
from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from api.email_verification.models import EmailVerificationCode

User = get_user_model()

class SendVerificationEmailViewTest(APITestCase):

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.url = reverse('send-verification-email')

    def test_send_verification_email(self):
        self.client.force_authenticate(self.user)
        response = self.client.post(self.url, {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertTrue(EmailVerificationCode.objects.filter(user=self.user).exists())

    def test_send_verification_email_unauthenticated(self):
        response = self.client.post(self.url, {}, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)


class VerifyEmailViewTest(APITestCase):

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.verification = EmailVerificationCode.objects.create(user=self.user, code='123456')
        self.url = reverse('verify-email')

    def test_verify_email_success(self):
        data = {'code': '123456'}
        self.client.force_authenticate(self.user)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.profile.refresh_from_db()
        self.assertTrue(self.user.profile.is_email_verified)
        self.assertFalse(EmailVerificationCode.objects.filter(user=self.user).exists())

    def test_verify_email_invalid_code(self):
        data = {'code': '654321'}
        self.client.force_authenticate(self.user)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.user.profile.refresh_from_db()
        self.assertFalse(self.user.profile.is_email_verified)

    def test_verify_email_expired_code(self):
        self.verification.created_at = timezone.now() - timedelta(minutes=31)
        self.verification.save()
        data = {'code': '123456'}
        self.client.force_authenticate(self.user)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.user.profile.refresh_from_db()
        self.assertFalse(self.user.profile.is_email_verified)
