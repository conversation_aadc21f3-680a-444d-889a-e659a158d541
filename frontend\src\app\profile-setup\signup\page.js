"use client"

import * as React from "react"
import { use<PERSON>out<PERSON> } from "next/navigation"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON>, Eye, EyeOff, ArrowRight, Loader2 } from "lucide-react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { useToast } from "@/hooks/use-toast"
import { useAuth } from '../../../context/AuthProvider'
import Link from "next/link"

// Zod schema for comprehensive form validation
const formSchema = z.object({
  email: z
    .string()
    .min(1, { message: "Email is required." })
    .email({ message: "Please enter a valid email address." }),
  password: z
    .string()
    .min(1, { message: "Password is required." })
    .min(8, { message: "Password must be at least 8 characters long." }),
  terms: z.boolean().refine((val) => val === true, {
    message: "You must accept the Terms of Service and Privacy Policy to continue.",
  }),
})

export default function CreateAccountPage() {
  const router = useRouter()
  const { toast } = useToast()
  const { profileData, clearProfileData, loginWithToken } = useAuth()

  // State management for password functionality
  const [passwordVisible, setPasswordVisible] = React.useState(false)
  const [passwordValue, setPasswordValue] = React.useState("")
  const [passwordStrength, setPasswordStrength] = React.useState({
    level: "",
    feedback: "Password strength: Enter at least 8 characters",
  })
  const [isLoading, setIsLoading] = React.useState(false)
  const [formError, setFormError] = React.useState("")

  // Initialize React Hook Form with Zod validation
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      email: "",
      password: "",
      terms: false,
    },
    mode: "onChange", // Enable real-time validation
  })

  // Password strength calculation effect
  React.useEffect(() => {
    if (passwordValue.length === 0) {
      setPasswordStrength({
        level: "",
        feedback: "Password strength: Enter at least 8 characters",
      })
    } else if (passwordValue.length < 8) {
      setPasswordStrength({ 
        level: "weak", 
        feedback: "Password strength: Too weak - needs at least 8 characters" 
      })
    } else if (passwordValue.length < 12) {
      // Check for additional complexity for medium strength
      const hasNumbers = /\d/.test(passwordValue)
      const hasSpecialChars = /[!@#$%^&*(),.?":{}|<>]/.test(passwordValue)
      const hasUpperCase = /[A-Z]/.test(passwordValue)
      
      if (hasNumbers || hasSpecialChars || hasUpperCase) {
        setPasswordStrength({ 
          level: "medium", 
          feedback: "Password strength: Medium - consider adding more characters" 
        })
      } else {
        setPasswordStrength({ 
          level: "weak", 
          feedback: "Password strength: Weak - add numbers, symbols, or uppercase letters" 
        })
      }
    } else {
      setPasswordStrength({ 
        level: "strong", 
        feedback: "Password strength: Strong" 
      })
    }
  }, [passwordValue])

  // Handle password input changes while maintaining RHF sync
  const handlePasswordChange = (event) => {
    const value = event.target.value
    setPasswordValue(value)
    form.setValue("password", value, { 
      shouldValidate: true,
      shouldDirty: true 
    })
  }
  
  // Toggle password visibility
  const togglePasswordVisibility = () => {
    setPasswordVisible(!passwordVisible)
  }

  // Form submission handler with comprehensive error handling
  async function onSubmit(values) {
    try {
      setIsLoading(true)

      const signupResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify({
          email: values.email,
          password: values.password,
          terms_accepted: values.terms, // added terms acceptance checkbox value
          user_type: "HighSchoolStudent",
        })
      })
      if (!signupResponse.ok) {
        const errorData = await signupResponse.json()
        if (errorData) {
          Object.keys(errorData).forEach((field) => {
            if (field === "error") {
              setFormError(errorData[field])
            } else {
              // Map backend field "terms_accepted" to frontend "terms"
              const formField = field === "terms_accepted" ? "terms" : field
              form.setError(formField, { message: errorData[field][0] })
            }
          })
        }
        setIsLoading(false)
        return
      }
      const signupData = await signupResponse.json()
      // Expect token from signup response, e.g., signupData.token

      // Successful registration
      toast({
        title: "Account Created Successfully!",
        description: "Welcome to Trailblazer. Redirecting you to your recommendations...",
      })

      loginWithToken(signupData.auth_token)

      if (profileData) {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/high-school-student-profile/`, {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            "Authorization": `Token ${signupData.auth_token}`
          },
          body: JSON.stringify(profileData)
        })
        if (!res.ok) {
          const errorData = await res.json()
          setFormError(errorData.error || "Failed to update profile")
          setIsLoading(false)
          return
        } else {
          clearProfileData()
          // Redirect to recommendations page after short delay
          setTimeout(() => {
            router.push("/recommendations")
          }, 1000)
        }
      }


    } catch (error) {
      // Handle unexpected errors
      console.error("Registration error:", error)
      toast({
        variant: "destructive",
        title: "Something went wrong",
        description: "An unexpected error occurred. Please try again later.",
      })
    } finally {
      setIsLoading(false)
    }
  }
  
  // Progress stepper configuration
  const steps = [
    { name: "Profile", status: "completed" },
    { name: "Account", status: "active" },
    { name: "Recommendations", status: "upcoming" },
  ]

  return (
    <>
      {/* Global styles for custom components */}
      <style jsx>{`
        /* Floating label styles */
        .form-floating {
          position: relative;
        }

        .form-floating input {
          height: 3.5rem;
          padding: 1rem 0.75rem 0.5rem 0.75rem;
          font-size: 1rem;
          line-height: 1.25;
        }

        .form-floating label {
          position: absolute;
          top: 0;
          left: 0;
          height: 100%;
          padding: 1rem 0.75rem;
          pointer-events: none;
          border: 1px solid transparent;
          transform-origin: 0 0;
          transition: opacity 0.1s ease-in-out, transform 0.1s ease-in-out;
          font-size: 1rem;
          line-height: 1.25;
        }

        .form-floating input:focus ~ label,
        .form-floating input:not(:placeholder-shown) ~ label {
          opacity: 0.65;
          transform: scale(0.85) translateY(-0.5rem) translateX(0.15rem);
        }

        /* Password strength indicator styles */
        .password-strength {
          height: 4px;
          background-color: #e5e7eb;
          border-radius: 2px;
          overflow: hidden;
        }

        .password-strength-bar {
          height: 100%;
          width: 0%;
          transition: width 0.3s ease, background-color 0.3s ease;
          border-radius: 2px;
        }

        .password-strength-bar.weak {
          width: 33%;
          background-color: #ef4444;
        }

        .password-strength-bar.medium {
          width: 66%;
          background-color: #f59e0b;
        }

        .password-strength-bar.strong {
          width: 100%;
          background-color: #10b981;
        }

        /* Stepper styles */
        .stepper-item {
          flex: 1;
          display: flex;
          justify-content: center;
        }

        .stepper-circle {
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.875rem;
          font-weight: 600;
          background-color: #e5e7eb;
          color: #6b7280;
          border: 2px solid #e5e7eb;
          transition: all 0.2s ease;
        }

        .stepper-item.completed .stepper-circle {
          background-color: hsl(var(--primary));
          color: white;
          border-color: hsl(var(--primary));
        }

        .stepper-item.active .stepper-circle {
          background-color: white;
          color: hsl(var(--primary));
          border-color: hsl(var(--primary));
          box-shadow: 0 0 0 4px rgb(59 130 246 / 0.1);
        }

        /* Button styles */
        .btn {
          display: inline-flex;
          align-items: center;
          justify-content: center;
          border-radius: 0.5rem;
          font-size: 0.875rem;
          font-weight: 500;
          transition: all 0.2s ease;
          border: none;
          cursor: pointer;
        }

        .btn-primary {
          background-color: hsl(var(--primary));
          color: white;
        }

        .btn-primary:hover:not(:disabled) {
          background-color: rgb(37 99 235);
        }

        .btn-primary:disabled {
          opacity: 0.7;
          cursor: not-allowed;
        }
      `}</style>

      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
        {/* Main Content Card */}
        <div className="w-full max-w-sm mx-auto bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-primary">
          {/* Progress Stepper - Shows current step in signup process */}
          <div className="px-6 pt-6">
            <div className="flex justify-between mb-6">
              {steps.map((step, index) => (
                <div
                  key={step.name}
                  className={`stepper-item ${step.status === 'completed' ? 'completed' : ''} ${step.status === 'active' ? 'active' : ''}`}
                >
                  <div className="flex flex-col items-center">
                    <div className="stepper-circle">
                      {step.status === 'completed' ? (
                        <Check className="w-3 h-3" strokeWidth={3} />
                      ) : (
                        index + 1
                      )}
                    </div>
                    <div className="text-xs mt-2 font-medium text-gray-600">{step.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Logo and Header */}
          <div className="px-6 pb-2 text-center">
            <div className="flex justify-center mb-8">
              <img
                alt="Trailblazer Logo"
                className="h-8"
                src="/logo.svg"
                onError={(e) => { 
                  e.currentTarget.src = 'https://placehold.co/120x32/3b82f6/ffffff?text=Trailblazer'
                  e.currentTarget.onerror = null
                }}
              />
            </div>
            <h1 className="text-2xl font-bold mb-1 text-gray-900">Create Your Account</h1>
            <p className="text-gray-600 text-sm mb-6">
              Almost there! Set up your login details to view your college recommendations.
            </p>
          </div>

          {/* Registration Form */}
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="px-6 pt-2 pb-4">
              {/* Email Input Field with validation */}
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="mb-5">
                    <div className="form-floating">
                      <FormControl>
                        <Input
                          id="email"
                          type="email"
                          placeholder="Email Address"
                          className="border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500"
                          {...field}
                        />
                      </FormControl>
                    </div>
                    <FormMessage className="mt-1 text-xs text-red-600" />
                  </FormItem>
                )}
              />

              {/* Password Input Field with strength indicator and visibility toggle */}
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="mb-2">
                    <div className="form-floating relative">
                      <FormControl>
                        <Input
                          id="password"
                          type={passwordVisible ? "text" : "password"}
                          placeholder="Password"
                          className="border border-gray-300 rounded-lg pr-10 focus:outline-none focus:ring-2 focus:ring-green-500/20 focus:border-green-500"
                          value={passwordValue}
                          onChange={handlePasswordChange}
                          onBlur={field.onBlur}
                          ref={field.ref}
                          name={field.name}
                        />
                      </FormControl>
                      {/* Password visibility toggle button */}
                      <Button
                        type="button"
                        variant="ghost"
                        size="sm"
                        className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-primary h-auto px-0 py-0"
                        onClick={togglePasswordVisibility}
                        aria-label={passwordVisible ? "Hide password" : "Show password"}
                      >
                        {passwordVisible ? <EyeOff className="w-5 h-5" /> : <Eye className="w-5 h-5" />}
                      </Button>
                    </div>
                    <FormMessage className="mt-1 text-xs text-red-600" />
                  </FormItem>
                )}
              />
              
              {/* Password Strength Visual Indicator */}
              <div className="password-strength mt-2">
                <div
                  className={`password-strength-bar ${passwordStrength.level}`}
                  role="progressbar"
                  aria-label="Password strength indicator"
                ></div>
              </div>
              <p className="text-xs text-gray-500 mt-1" id="passwordFeedback">
                {passwordStrength.feedback}
              </p>

              {/* Terms and Conditions Acceptance Checkbox */}
              <FormField
                control={form.control}
                name="terms"
                render={({ field }) => (
                  <FormItem className="mb-6 flex flex-row items-start space-x-3 space-y-0 mt-4">
                    <FormControl>
                      <Checkbox
                        id="terms"
                        checked={field.value}
                        onCheckedChange={field.onChange}
                        className="mt-1"
                      />
                    </FormControl>
                    <div className="space-y-1 leading-none">
                      <FormLabel htmlFor="terms" className="text-sm text-gray-600 font-normal cursor-pointer">
                        I agree to the{" "}
                        <a 
                          href="/terms" 
                          className="text-primary hover:underline focus:underline focus:outline-none"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Terms of Service
                        </a>
                        {" "}and{" "}
                        <a 
                          href="/privacy" 
                          className="text-primary hover:underline focus:underline focus:outline-none"
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          Privacy Policy
                        </a>
                      </FormLabel>
                      <FormMessage className="text-xs text-red-600" />
                    </div>
                  </FormItem>
                )}
              />

              {/* Submit Button with Loading State */}
              {!isLoading ? (
                <Button 
                  type="submit" 
                  className="w-full btn btn-primary flex items-center justify-center h-12 rounded-lg bg-primary hover:bg-green-600 hover:text-white text-white disabled:opacity-50"
                  disabled={!form.formState.isValid}
                >
                  <span>Create Account</span>
                  <ArrowRight className="w-4 h-4 ml-2" />
                </Button>
              ) : (
                <Button 
                  disabled 
                  className="w-full btn btn-primary flex items-center justify-center h-12 rounded-lg bg-green-500/70 text-white cursor-not-allowed"
                >
                  <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                  <span>Creating Account...</span>
                </Button>
              )}
              {formError && <p className="text-red-600 mt-2">{formError}</p>}
            </form>
          </Form>

          {/* Already Have Account Link */}
          <div className="px-6 pb-6 text-center">
            <p className="text-sm text-gray-600">
              Already have an account?{" "}
              <Link href="/profile-setup/login" className="text-primary font-medium hover:underline focus:underline focus:outline-none">
                Sign in
              </Link>
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>© 2024 Trailblazer. All rights reserved.</p>
        </div>
      </div>
    </>
  )
}