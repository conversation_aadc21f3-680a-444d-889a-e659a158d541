"use client"

import { useState } from 'react'
import { useForm } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form"
import { Textarea } from "@/components/ui/textarea"
import { Input } from "@/components/ui/input"
import { AvatarSelection, avatarToFile } from "@/components/ui/avatarSelection"
import { Button } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { ArrowRight } from 'lucide-react'
import { useRouter } from 'next/navigation'
import { getNextOnboardingStep } from '@/lib/utils'
import withAuth from '@/hoc/withAuth'
import { useAuth } from '@/context/AuthProvider'
import { Loader2 } from 'lucide-react'


// Define the form schema with Zod
const formSchema = z.object({
    firstName: z.string().min(2, { message: "First name is required" }),
    lastName: z.string().min(2, { message: "Last name is required" }),
    paypalEmail: z.string().email({ message: "Invalid email address" }).min(1, { message: "Paypal email is required" }),
    avatar: z.string().min(1, { message: "Avatar is required" }),
    bio: z.string().min(
        1, { message: "Bio is required" }
    ).max(280, { message: "Bio must be less than 280 characters" }),
})

// Profile Completion Form component
const ProfileCompletionForm = () => {
    const [selectedAvatar, setSelectedAvatar] = useState('')
    const [isLoading, setIsLoading] = useState(false)
    const { toast } = useToast()
    const router = useRouter()
    const { getToken } = useAuth()

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: "",
            lastName: "",
            paypalEmail: "",
            avatar: "",
            bio: "",
        }
    })

    const onSubmit = async (data) => {
        setIsLoading(true)
        const authToken = getToken()
        try {
            const formData = new FormData()
            formData.append('step', 'basic_info')
            formData.append('first_name', data.firstName)
            formData.append('last_name', data.lastName)
            formData.append('bio', data.bio)
            formData.append('paypal_email', data.paypalEmail)

            // Fetch the avatar image as a File
            const avatarFile = await avatarToFile(data.avatar)

            // Append the avatar file to the form data
            formData.append('avatar', avatarFile)
            
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Token ${authToken}`
                },
                body: formData
            })
                        
            if (!response.ok) {
                const result = await response.json()
                Object.keys(result).forEach((key) => {
                    form.setError(key, { message: result[key][0] })
                })
                console.error('Failed to complete profile:', result)
                setIsLoading(false)
            } else {
                router.push(getNextOnboardingStep('CollegeStudent', 'profile'))
            }
        } catch (error) {
            // Handle network errors    
            console.error('An unexpected error happened:', error)
            toast({
                variant: 'destructive',
                description: 'An unexpected error happened. Please try again.',
                status: "error",
                duration: 5000,
                isClosable: true,
            })
            setIsLoading(false)
        }
    }

    return (
        <div className="">
            <h1 className="text-4xl font-bold mb-8">Complete your profile</h1>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>First Name*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your first name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Last Name*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your last name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField 
                        control={form.control}
                        name="paypalEmail"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Paypal Email*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your paypal email" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="bio"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Write a short bio* (280 characters)</FormLabel>
                                <FormControl>
                                    <Textarea className="bg-white" placeholder="Write about you" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="avatar"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Pick an avatar</FormLabel>
                                <FormControl>
                                    <AvatarSelection
                                        onSelect={(avatar) => {
                                            setSelectedAvatar(avatar)
                                            field.onChange(avatar)
                                        }}
                                        selectedAvatar={selectedAvatar}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div>
                        <Button type="submit" className="mt-8" disabled={isLoading}>
                            {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Complete Profile <ArrowRight className="ml-2 w-6 h-6" />
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    )
}

// Main Page component
const ProfileCompletionPage = () => {
    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="py-10">
                    <StepProgressDisplay currentStep={4} totalSteps={6} />
                </div>
                <div className="w-full md:w-5/6 lg:w-5/6">
                    <ProfileCompletionForm />
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(ProfileCompletionPage)
