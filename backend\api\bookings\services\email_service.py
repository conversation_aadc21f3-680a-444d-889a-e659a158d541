import logging
import pytz
from django.core.mail import send_mail
from django.template.loader import render_to_string
from django.conf import settings
from datetime import datetime, timedelta

logger = logging.getLogger(__name__)


def format_session_time(start_time, end_time, timezone_name=None):
    """
    Convert and format session start and end times to the given time zone.
    
    Args:
        start_time (datetime): The session start time in UTC.
        end_time (datetime): The session end time in UTC.
        timezone_name (str, optional): The name of the time zone to convert to. Defaults to UTC.
    
    Returns:
        tuple: A tuple containing the formatted session date and time range.
    """
    try:
        target_tz = pytz.timezone(timezone_name) if timezone_name else pytz.UTC
    except (pytz.UnknownTimeZoneError, AttributeError, TypeError):
        target_tz = pytz.UTC  # Fallback to UTC if the time zone is invalid or missing

    # Convert times to the target time zone
    start_time_local = start_time.astimezone(target_tz)
    end_time_local = end_time.astimezone(target_tz)

    # Format session date and time
    session_date = start_time_local.strftime('%B %d, %Y')
    session_time = f"{start_time_local.strftime('%I:%M %p')} - {end_time_local.strftime('%I:%M %p')}"
    
    return session_date, session_time


class BookingEmailService:
    @staticmethod
    def send_session_request_email(booking):
        """
        Send email notifications for new session request to all trailblazers
        """
        try:
            trailblazers = booking.trailblazers
            if not trailblazers.exists():
                logger.warning(f"No trailblazers associated with booking {booking.id}")
                return

            for trailblazer in trailblazers:
                session_date, session_time = format_session_time(
                    booking.start_time,
                    booking.end_time,
                    getattr(getattr(trailblazer, 'availability', None), 'time_zone', None)
                )

                context = {
                    'session_with': f"{booking.booked_by.profile} at {booking.booked_by.profile.organization.name if booking.booked_by.profile.organization else ''}",
                    'session_date': session_date,
                    'session_time': session_time,
                    'session_type': 'Group session' if trailblazers.count() > 1 else 'Individual session',
                    'message': booking.message,
                    'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
                }

                html_message = render_to_string('emails/session_request.html', context)

                send_mail(
                    subject='[Trailblazer] New Session Request',
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[trailblazer.email],
                    html_message=html_message
                )

                logger.info(f"Session request email sent to {trailblazer.email} for booking {booking.id}")

        except Exception as e:
            logger.error(f"Failed to send session request email for booking {booking.id}: {str(e)}")

    @staticmethod
    def send_session_confirmation_email(trailblazer_status):
        """
        Send confirmation emails to booked_by user and all trailblazers
        when a session is confirmed. Each recipient gets a personalized email.
        """
        try:
            booking = trailblazer_status.booking
            confirmed_trailblazers = booking.trailblazers.filter(booking_statuses__status='confirmed')

            if booking.booked_by and booking.booked_by.email:
                session_date, session_time = format_session_time(
                    booking.start_time,
                    booking.end_time,
                    booking.creator_timezone
                )

                context = {
                    'session_with': ', '.join([f"{trailblazer.first_name} {trailblazer.last_name}" for trailblazer in confirmed_trailblazers]),
                    'session_date': session_date,
                    'session_time': session_time,
                    'session_type': 'Group session' if booking.trailblazers.count() > 1 else 'Individual session',
                    'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
                }

                html_message = render_to_string('emails/session_confirmed.html', context)

                send_mail(
                    subject='[Trailblazer] Session Confirmation',
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[booking.booked_by.email],
                    html_message=html_message
                )

                logger.info(f"Session confirmation email sent to requester {booking.booked_by.email} for booking {booking.id}")

            for trailblazer in confirmed_trailblazers:
                session_date, session_time = format_session_time(
                    booking.start_time,
                    booking.end_time,
                    getattr(getattr(trailblazer, 'availability', None), 'time_zone', None)
                )

                context = {
                    'session_with': f"{booking.booked_by.first_name} {booking.booked_by.last_name}",
                    'session_date': session_date,
                    'session_time': session_time,
                    'session_type': 'Group session' if booking.trailblazers.count() > 1 else 'Individual session',
                    'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
                }

                html_message = render_to_string('emails/session_confirmed.html', context)

                send_mail(
                    subject='[Trailblazer] Session Confirmation',
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[trailblazer.email],
                    html_message=html_message
                )

                logger.info(f"Session confirmation email sent to trailblazer {trailblazer.email} for booking {booking.id}")

        except Exception as e:
            logger.error(f"Failed to send confirmation emails for booking {trailblazer_status.booking.id}: {str(e)}")

    @staticmethod
    def send_session_cancellation_email(booking):
        """
        Send cancelled emails to booked_by user and all trailblazers
        when a session is cancelled. Each recipient gets a personalized email.
        """
        try:
            trailblazers = booking.trailblazers

            if booking.booked_by and booking.booked_by.email:
                session_date, session_time = format_session_time(
                    booking.start_time,
                    booking.end_time,
                    booking.creator_timezone
                )

                context = {
                    'session_with': ', '.join([f"{trailblazer.first_name} {trailblazer.last_name}" for trailblazer in trailblazers]),
                    'session_date': session_date,
                    'session_time': session_time,
                    'session_type': 'Group session' if trailblazers.count() > 1 else 'Individual session',
                    'reason': booking.creator_cancel_reason or '',
                    'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
                }

                html_message = render_to_string('emails/session_cancelled.html', context)

                send_mail(
                    subject='[Trailblazer] Session Cancellation',
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[booking.booked_by.email],
                    html_message=html_message
                )

                logger.info(f"Session cancelled email sent to requester {booking.booked_by.email} for booking {booking.id}")

            for trailblazer in trailblazers:
                session_date, session_time = format_session_time(
                    booking.start_time,
                    booking.end_time,
                    getattr(getattr(trailblazer, 'availability', None), 'time_zone', None)
                )

                context = {
                    'session_with': f"{booking.booked_by.first_name} {booking.booked_by.last_name}",
                    'session_date': session_date,
                    'session_time': session_time,
                    'session_type': 'Group session' if trailblazers.count() > 1 else 'Individual session',
                    'reason': booking.creator_cancel_reason or '',
                    'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
                }

                html_message = render_to_string('emails/session_cancelled.html', context)

                send_mail(
                    subject='[Trailblazer] Session Cancellation',
                    message='',
                    from_email=settings.DEFAULT_FROM_EMAIL,
                    recipient_list=[trailblazer.email],
                    html_message=html_message
                )

                logger.info(f"Session cancelled email sent to trailblazer {trailblazer.email} for booking {booking.id}")

        except Exception as e:
            logger.error(f"Failed to send cancelled emails for booking {booking.id}: {str(e)}")

    @staticmethod
    def send_session_declined_email(trailblazer_status):
        """
        Send email notification when a session is declined.
        The email is sent to the user who created the booking.
        """
        try:
            booking = trailblazer_status.booking
            trailblazers = booking.trailblazers.all()

            if not booking.booked_by or not booking.booked_by.email:
                logger.warning(f"No valid email for booking creator {booking.id}")
                return

            session_date, session_time = format_session_time(
                booking.start_time,
                booking.end_time,
                booking.creator_timezone
            )

            context = {
                'session_with': ', '.join([f"{trailblazer.first_name} {trailblazer.last_name}" for trailblazer in trailblazers]),
                'session_date': session_date,
                'session_time': session_time,
                'session_type': 'Group session' if trailblazers.count() > 1 else 'Individual session',
                'reasons': [
                    {
                        "name": f"{status.trailblazer.first_name} {status.trailblazer.last_name}",
                        "message": status.decline_reason
                    }
                    for status in booking.trailblazer_statuses.all()
                ],
                'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
            }

            html_message = render_to_string('emails/session_declined.html', context)

            send_mail(
                subject='[Trailblazer] Session Declined',
                message='',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[booking.booked_by.email],
                html_message=html_message
            )

            logger.info(f"Declined session email sent to {booking.booked_by.email} for booking {booking.id}")

        except Exception as e:
            logger.error(f"Failed to send declined session email for booking {booking.id}: {str(e)}")

    @staticmethod
    def send_proposed_times_email(booking, proposed_times):
        """
        Send email notification when a trailblazer proposes new session times.
        The email is sent to the user who created the booking.
        """
        try:
            if not booking.booked_by or not booking.booked_by.email:
                logger.warning(f"No valid email for booking creator {booking.id}")
                return

            session_date, session_time = format_session_time(
                booking.start_time,
                booking.end_time,
                booking.creator_timezone
            )

            context = {
                'session_with': f"{booking.trailblazers[0].first_name} {booking.trailblazers[0].last_name}",
                'session_date': session_date,
                'session_time': session_time,
                'session_type': 'Individual session',
                'reasons': [
                    {
                        "name": f"{status.trailblazer.first_name} {status.trailblazer.last_name}",
                        "message": status.decline_reason
                    }
                    for status in booking.trailblazer_statuses.all()
                ],
                'proposed_times': [
                    {
                        "date": format_session_time(
                            datetime.fromisoformat(proposed_time['start_time'].replace('Z', '+00:00')),
                            (datetime.fromisoformat(proposed_time['start_time'].replace('Z', '+00:00')) + timedelta(hours=1)),
                            booking.creator_timezone
                        )[0],  
                        "session_time": format_session_time(
                            datetime.fromisoformat(proposed_time['start_time'].replace('Z', '+00:00')),
                            (datetime.fromisoformat(proposed_time['start_time'].replace('Z', '+00:00')) + timedelta(hours=1)),
                            booking.creator_timezone
                        )[1],
                       
                    }
                    for proposed_time in proposed_times
                ],
            'logo_path': f"{settings.FRONTEND_BASE_URL}/logo_email.png",
                }

            html_message = render_to_string('emails/proposed_times.html', context)

            send_mail(
                subject='[Trailblazer] New Proposed Times',
                message='',
                from_email=settings.DEFAULT_FROM_EMAIL,
                recipient_list=[booking.booked_by.email],
                html_message=html_message
            )

            logger.info(f"Proposed times email sent to {booking.booked_by.email} for booking {booking.id}")

        except Exception as e:
            logger.error(f"Failed to send proposed times email for booking {booking.id}: {str(e)}")
