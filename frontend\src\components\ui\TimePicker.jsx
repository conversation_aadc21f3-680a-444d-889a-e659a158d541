import React, { useState, useEffect, useRef } from "react";
import { createPortal } from "react-dom";
import { Input } from "@/components/ui/input";

const TimePickerInput = ({
  value,
  onChange,
  onBlur,
  name,
  disabled,
  threshold = "08:00 AM",
}) => {
  const [inputValue, setInputValue] = useState("");
  const [open, setOpen] = useState(false);
  const [times24, setTimes24] = useState([]);
  const [times12, setTimes12] = useState([]);
  const [highlightedIndex, setHighlightedIndex] = useState(0);
  const inputRef = useRef(null);
  const listRef = useRef(null);
  const popoverRef = useRef(null);
  const defaultHighlightedIndexRef = useRef(0);
  const thresholdMinutesRef = useRef(0);

  const parseTimeToMinutes = (time) => {
    const [hourMinute, ampm] = time.split(" ");
    const [hour, minute] = hourMinute.split(":").map(Number);
    return (hour % 12) * 60 + minute + (ampm === "PM" ? 720 : 0);
  };

  const convert24To12 = (time24) => {
    const [hourStr, minuteStr] = time24.split(":");
    let hour = parseInt(hourStr, 10);
    const minute = minuteStr;
    const ampm = hour >= 12 ? "PM" : "AM";
    hour = hour % 12 || 12; // Convert '0' and '12' to '12'
    return `${hour.toString().padStart(2, "0")}:${minute} ${ampm}`;
  };

  useEffect(() => {
    const timesList24 = [];
    const timesList12 = [];
    for (let hour = 0; hour < 24; hour++) {
      for (let minute of ["00", "30"]) {
        const time24 = `${hour.toString().padStart(2, "0")}:${minute}`;
        timesList24.push(time24);

        const hour12 = (hour % 12 === 0 ? 12 : hour % 12)
          .toString()
          .padStart(2, "0");
        const ampm = hour < 12 ? "AM" : "PM";
        const time12 = `${hour12}:${minute} ${ampm}`;
        timesList12.push(time12);

        if (time12 === threshold) {
          defaultHighlightedIndexRef.current = timesList12.length - 1;
        }
      }
    }
    setTimes24(timesList24);
    setTimes12(timesList12);

    thresholdMinutesRef.current = parseTimeToMinutes(threshold);
    setHighlightedIndex(defaultHighlightedIndexRef.current);
  }, [threshold]);

  useEffect(() => {
    if (value) {
      const index = times24.indexOf(value);
      if (index !== -1) {
        setInputValue(times12[index]);
      } else {
        // If the value isn't in the predefined list, convert it
        setInputValue(convert24To12(value));
      }
    } else {
      setInputValue("");
    }
  }, [value, times12, times24]);

  const normalizeInput = (input) =>
    input?.replace(/^0+/, "").trim().toLowerCase();

  const findClosestIndex = (input) => {
    const normalizedInput = normalizeInput(input);
    if (!normalizedInput) return defaultHighlightedIndexRef.current;

    let isPMExplicitlySet = false;
    let isPM = false;

    if (normalizedInput.includes("p")) {
      isPM = true;
      isPMExplicitlySet = true;
    }
    if (normalizedInput.includes("a")) {
      isPM = false;
      isPMExplicitlySet = true;
    }

    try {
      const inputTime = normalizedInput.replace(/[^\d]/g, "");
      let hour = null;
      let minute = null;

      if (inputTime.length === 1 || inputTime.length === 2) {
        hour = parseInt(inputTime, 10);
        minute = 0;
      } else if (inputTime.length === 3) {
        hour = parseInt(inputTime[0], 10);
        minute = parseInt(inputTime.slice(1), 10);
      } else if (inputTime.length === 4) {
        hour = parseInt(inputTime.slice(0, 2), 10);
        minute = parseInt(inputTime.slice(2), 10);
      }

      let inputMinutes = (hour % 12) * 60 + (minute || 0);

      if (!isPMExplicitlySet) {
        if (inputMinutes < thresholdMinutesRef.current) {
          isPM = true;
        }
      }

      inputMinutes += isPM ? 720 : 0;

      let closestIndex = 0;
      let smallestDifference = Number.MAX_VALUE;

      times12.forEach((time, index) => {
        const timeMinutes = parseTimeToMinutes(time);
        const difference = Math.abs(timeMinutes - inputMinutes);
        if (difference < smallestDifference) {
          smallestDifference = difference;
          closestIndex = index;
        }
      });

      return closestIndex;
    } catch {
      return defaultHighlightedIndexRef.current;
    }
  };

  useEffect(() => {
    const closestIndex = findClosestIndex(inputValue);
    setHighlightedIndex(closestIndex);
  }, [inputValue]);

  const adjustScroll = () => {
    if (listRef.current) {
      const listElement = listRef.current;
      const itemHeight = listElement.scrollHeight / times12.length;
      const targetScrollTop = highlightedIndex * itemHeight;
      listElement.scrollTo({ top: targetScrollTop, behavior: "auto" });
    }
  };

  useEffect(() => {
    if (open) adjustScroll();
  }, [open, highlightedIndex]);

  const handleSelect = (selectedTime) => {
    const selectedIndex = times12.indexOf(selectedTime);
    const value24 = times24[selectedIndex]; // Get the corresponding 24-hour format value
    setInputValue(selectedTime);
    onChange(value24); // Pass the 24-hour format value to the parent
    setOpen(false);
  };

  const handleInputChange = (e) => {
    const newValue = e.target.value;
    setInputValue(newValue);
    const closestIndex = findClosestIndex(newValue);
    onChange(times24[closestIndex]); // Pass the corresponding 24-hour format to the parent
    setOpen(true);
  };

  const handleKeyDown = (e) => {
    if (!open) return;
    switch (e.key) {
      case "ArrowDown":
        e.preventDefault();
        setHighlightedIndex((prev) =>
          Math.min(prev + 1, times12.length - 1)
        );
        break;
      case "ArrowUp":
        e.preventDefault();
        setHighlightedIndex((prev) => Math.max(prev - 1, 0));
        break;
      case "Enter":
        e.preventDefault();
        if (times12[highlightedIndex]) handleSelect(times12[highlightedIndex]);
        break;
      case "Tab":
        if (times12[highlightedIndex]) handleSelect(times12[highlightedIndex]);
        setOpen(false);
        break;
      case "Escape":
        e.preventDefault();
        setOpen(false);
        break;
      default:
        break;
    }
  };

  const handleBlur = (e) => {
    const relatedTarget = e.relatedTarget || document.activeElement;
    if (
      popoverRef.current &&
      popoverRef.current.contains(relatedTarget)
    ) {
      return;
    }

    setTimeout(() => {
      if (!times12.includes(inputValue)) {
        setInputValue("");
        onChange("");
      }
      setOpen(false);
    }, 150);
    if (onBlur) onBlur(e);
  };

  const handleFocus = () => {
    if (disabled) return;
    setOpen(true);
    adjustScroll();
  };

  const handleClick = () => {
    if (disabled) return;
    setOpen(true);
    adjustScroll();
  };

  const getPopoverStyles = () => {
    if (!inputRef.current) return {};
    const rect = inputRef.current.getBoundingClientRect();
    return {
      position: "absolute",
      top: `${rect.bottom + window.scrollY}px`,
      left: `${rect.left + window.scrollX}px`,
      width: `${rect.width}px`,
      zIndex: 1000,
    };
  };

  return (
    <div className="relative" onBlur={handleBlur} onFocus={handleFocus} tabIndex="-1">
      <Input
        ref={inputRef}
        value={inputValue}
        onChange={handleInputChange}
        onKeyDown={handleKeyDown}
        onClick={handleClick} // Trigger dropdown on click
        disabled={disabled}
        name={name}
        placeholder="--:-- --"
        className=""
        autoComplete="off"
      />
      {open &&
        createPortal(
          <div
            style={getPopoverStyles()}
            className="bg-white border rounded-md shadow-lg text-sm p-0"
            ref={popoverRef}
          >
            <ul className="max-h-60 overflow-y-auto" ref={listRef} tabIndex="-1">
              {times12.map((time, index) => (
                <li
                  key={time}
                  id={`time-option-${index}`}
                  onMouseDown={(e) => e.preventDefault()}
                  onClick={() => handleSelect(time)}
                  className={`px-2 py-1 cursor-pointer ${
                    highlightedIndex === index ? "bg-blue-200 font-bold" : ""
                  }`}
                  role="option"
                  aria-selected={highlightedIndex === index}
                >
                  {time}
                </li>
              ))}
            </ul>
          </div>,
          document.body
        )}
    </div>
  );
};

export default TimePickerInput;
