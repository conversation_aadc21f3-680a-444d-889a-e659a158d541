"use client"

import * as React from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { useRouter } from "next/navigation"
import { HelpCircle, ArrowLeft, ArrowRight, Check, ChevronsUpDown, X } from "lucide-react"
import { useAuth } from '@/context/AuthProvider';

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  <PERSON><PERSON><PERSON>,
  <PERSON>lt<PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>,
  Tooltip<PERSON>rigger,
} from "@/components/ui/tooltip"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
} from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"

// Comprehensive mock data for selects/comboboxes
const graduationYears = ["2025", "2026", "2027", "2028", "2029", "2030"].map(year => ({ value: year, label: year }))

const majors = [
  { value: "accounting", label: "Accounting" },
  { value: "actuarial_sciences", label: "Actuarial Sciences" },
  { value: "advertising_public_relations", label: "Advertising & Public Relations" },
  { value: "aerospace_aeronautical_engineering", label: "Aerospace & Aeronautical Engineering" },
  { value: "african_african_american_studies", label: "African & African-American Studies" },
  { value: "agricultural_economics", label: "Agricultural Economics" },
  { value: "agriculture", label: "Agriculture" },
  { value: "agriculture_production_management", label: "Agriculture Production & Management" },
  { value: "animal_sciences", label: "Animal Sciences" },
  { value: "anthropology", label: "Anthropology" },
  { value: "applied_mathematics", label: "Applied Mathematics" },
  { value: "arabic_language_literature", label: "Arabic Language & Literature" },
  { value: "archaeology", label: "Archaeology" },
  { value: "architectural_engineering", label: "Architectural Engineering" },
  { value: "architecture", label: "Architecture" },
  { value: "art_art_history", label: "Art & Art History" },
  { value: "astronomy_astrophysics", label: "Astronomy & Astrophysics" },
  { value: "atmospheric_sciences_meteorology", label: "Atmospheric Sciences & Meteorology" },
  { value: "biochemistry", label: "Biochemistry" },
  { value: "biological_engineering", label: "Biological Engineering" },
  { value: "biology", label: "Biology" },
  { value: "biomedical_engineering", label: "Biomedical Engineering" },
  { value: "botany", label: "Botany" },
  { value: "business_economics", label: "Business Economics" },
  { value: "business_management_administration", label: "Business Management & Administration" },
  { value: "chemical_engineering", label: "Chemical Engineering" },
  { value: "chemistry", label: "Chemistry" },
  { value: "chinese_language_literature", label: "Chinese Language & Literature" },
  { value: "civil_engineering", label: "Civil Engineering" },
  { value: "clinical_psychology", label: "Clinical Psychology" },
  { value: "cognitive_science_biopsychology", label: "Cognitive Science & Biopsychology" },
  { value: "commercial_art_graphic_design", label: "Commercial Art & Graphic Design" },
  { value: "communication_technologies", label: "Communication Technologies" },
  { value: "communications", label: "Communications" },
  { value: "community_public_health", label: "Community & Public Health" },
  { value: "comparative_language_literature", label: "Comparative Language & Literature" },
  { value: "computer_information_systems", label: "Computer & Information Systems" },
  { value: "computer_engineering", label: "Computer Engineering" },
  { value: "computer_networking_telecommunications", label: "Computer Networking & Telecommunications" },
  { value: "computer_programming_data_processing", label: "Computer Programming & Data Processing" },
  { value: "computer_science", label: "Computer Science" },
  { value: "construction_services", label: "Construction Services" },
  { value: "cosmetology_services", label: "Cosmetology Services" },
  { value: "counseling_psychology", label: "Counseling Psychology" },
  { value: "criminal_justice", label: "Criminal Justice" },
  { value: "criminology", label: "Criminology" },
  { value: "culinary_arts", label: "Culinary Arts" },
  { value: "cybersecurity_network_security", label: "Cybersecurity & Network Security" },
  { value: "drama_theater_arts", label: "Drama & Theater Arts" },
  { value: "early_childhood_education", label: "Early Childhood Education" },
  { value: "east_asian_studies", label: "East Asian Studies" },
  { value: "ecology", label: "Ecology" },
  { value: "economics", label: "Economics" },
  { value: "education", label: "Education" },
  { value: "educational_administration_supervision", label: "Educational Administration & Supervision" },
  { value: "educational_psychology", label: "Educational Psychology" },
  { value: "electrical_engineering", label: "Electrical Engineering" },
  { value: "elementary_education", label: "Elementary Education" },
  { value: "engineering", label: "Engineering" },
  { value: "engineering_technologies", label: "Engineering Technologies" },
  { value: "english_language_literature", label: "English Language & Literature" },
  { value: "environmental_engineering", label: "Environmental Engineering" },
  { value: "environmental_sciences", label: "Environmental Sciences" },
  { value: "ethnic_civilization_studies", label: "Ethnic & Civilization Studies" },
  { value: "european_history", label: "European History" },
  { value: "film_video_photographic_arts", label: "Film, Video, & Photographic Arts" },
  { value: "finance", label: "Finance" },
  { value: "fine_arts", label: "Fine Arts" },
  { value: "folklore_mythology", label: "Folklore & Mythology" },
  { value: "forestry", label: "Forestry" },
  { value: "french_language_literature", label: "French Language & Literature" },
  { value: "general_medical_health_services", label: "General Medical & Health Services" },
  { value: "genetics", label: "Genetics" },
  { value: "geography", label: "Geography" },
  { value: "geological_geophysical_engineering", label: "Geological & Geophysical Engineering" },
  { value: "geology_earth_sciences", label: "Geology & Earth Sciences" },
  { value: "geosciences", label: "Geosciences" },
  { value: "german_language_literature", label: "German Language & Literature" },
  { value: "government_political_sciences", label: "Government & Political Sciences" },
  { value: "health_medical_administration", label: "Health & Medical Administration" },
  { value: "hispanic_latin_american_studies", label: "Hispanic & Latin American Studies" },
  { value: "history", label: "History" },
  { value: "hospitality_management", label: "Hospitality Management" },
  { value: "human_development_family_studies", label: "Human Development & Family Studies" },
  { value: "human_resources_personnel_management", label: "Human Resources & Personnel Management" },
  { value: "human_services_community_organization", label: "Human Services & Community Organization" },
  { value: "humanities", label: "Humanities" },
  { value: "industrial_manufacturing_engineering", label: "Industrial & Manufacturing Engineering" },
  { value: "industrial_organizational_psychology", label: "Industrial & Organizational Psychology" },
  { value: "intercultural_international_studies", label: "Intercultural & International Studies" },
  { value: "interior_design", label: "Interior Design" },
  { value: "international_business", label: "International Business" },
  { value: "international_relations", label: "International Relations" },
  { value: "italian_language_literature", label: "Italian Language & Literature" },
  { value: "japanese_language_literature", label: "Japanese Language & Literature" },
  { value: "journalism", label: "Journalism" },
  { value: "kinesiology_exercise_science", label: "Kinesiology & Exercise Science" },
  { value: "korean_language_literature", label: "Korean Language & Literature" },
  { value: "latin_language_literature", label: "Latin Language & Literature" },
  { value: "liberal_arts", label: "Liberal Arts" },
  { value: "linguistics", label: "Linguistics" },
  { value: "marketing_marketing_management", label: "Marketing & Marketing Management" },
  { value: "materials_engineering", label: "Materials Engineering" },
  { value: "materials_science", label: "Materials Science" },
  { value: "mathematics", label: "Mathematics" },
  { value: "mathematics_teacher_education", label: "Mathematics Teacher Education" },
  { value: "mechanical_engineering", label: "Mechanical Engineering" },
  { value: "medical_assisting_services", label: "Medical Assisting Services" },
  { value: "medical_technologies", label: "Medical Technologies" },
  { value: "metallurgical_engineering", label: "Metallurgical Engineering" },
  { value: "microbiology", label: "Microbiology" },
  { value: "military_technologies", label: "Military Technologies" },
  { value: "mining_mineral_engineering", label: "Mining & Mineral Engineering" },
  { value: "molecular_cellular_biology", label: "Molecular & Cellular Biology" },
  { value: "music", label: "Music" },
  { value: "native_american_languages_linguistics", label: "Native American Languages & Linguistics" },
  { value: "native_american_studies", label: "Native American Studies" },
  { value: "natural_resources_management", label: "Natural Resources Management" },
  { value: "naval_architecture_marine_engineering", label: "Naval Architecture & Marine Engineering" },
  { value: "near_middle_eastern_studies", label: "Near & Middle Eastern Studies" },
  { value: "neuroscience", label: "Neuroscience" },
  { value: "nuclear_engineering", label: "Nuclear Engineering" },
  { value: "nursing", label: "Nursing" },
  { value: "nutrition_food_sciences", label: "Nutrition & Food Sciences" },
  { value: "oceanography", label: "Oceanography" },
  { value: "operations_logistics_e_commerce", label: "Operations, Logistics, & E-Commerce" },
  { value: "petroleum_engineering", label: "Petroleum Engineering" },
  { value: "pharmacology", label: "Pharmacology" },
  { value: "pharmacy_pharmaceutical_sciences_administration", label: "Pharmacy, Pharmaceutical Sciences, & Administration" },
  { value: "philosophy", label: "Philosophy" },
  { value: "physical_health_education_teaching", label: "Physical & Health Education Teaching" },
  { value: "physical_sciences", label: "Physical Sciences" },
  { value: "physics", label: "Physics" },
  { value: "physiology", label: "Physiology" },
  { value: "plant_science_agronomy", label: "Plant Science & Agronomy" },
  { value: "portuguese_language_literature", label: "Portuguese Language & Literature" },
  { value: "pre_law_legal_studies", label: "Pre-Law & Legal Studies" },
  { value: "psychology", label: "Psychology" },
  { value: "public_administration", label: "Public Administration" },
  { value: "public_policy", label: "Public Policy" },
  { value: "russian_language_literature", label: "Russian Language & Literature" },
  { value: "school_student_counseling", label: "School Student Counseling" },
  { value: "science_computer_teacher_education", label: "Science & Computer Teacher Education" },
  { value: "secondary_teacher_education", label: "Secondary Teacher Education" },
  { value: "slavic_language_literature", label: "Slavic Language & Literature" },
  { value: "social_psychology", label: "Social Psychology" },
  { value: "social_science_history_teacher_education", label: "Social Science Or History Teacher Education" },
  { value: "social_sciences", label: "Social Sciences" },
  { value: "social_work_services", label: "Social Work & Services" },
  { value: "sociology", label: "Sociology" },
  { value: "south_asian_studies", label: "South Asian Studies" },
  { value: "spanish_language_literature", label: "Spanish Language & Literature" },
  { value: "special_needs_education", label: "Special Needs Education" },
  { value: "statistics", label: "Statistics" },
  { value: "studio_arts", label: "Studio Arts" },
  { value: "theology_religious_studies", label: "Theology & Religious Studies" },
  { value: "transportation_sciences_technologies", label: "Transportation Sciences & Technologies" },
  { value: "treatment_therapy_professions", label: "Treatment Therapy Professions" },
  { value: "united_states_history", label: "United States History" },
  { value: "urban_studies", label: "Urban Studies" },
  { value: "visual_performing_arts", label: "Visual & Performing Arts" },
  { value: "womens_gender_sexuality_studies", label: "Women's, Gender, & Sexuality Studies" },
  { value: "writing_composition_rhetoric", label: "Writing, Composition, & Rhetoric" },
  { value: "zoology", label: "Zoology" }
]

const collegeTypes = [
  { value: "public", label: "Public College" },
  { value: "private", label: "Private College" },
  { value: "liberal_arts", label: "Liberal Arts College" },
  { value: "hbcu", label: "Historically Black College and University (HBCU)" },
  { value: "hsi", label: "Hispanic-Serving Institution (HSI)" },
  { value: "aanapisi", label: "Asian American and Native American Pacific Islander-Serving Institution (AANAPISI)" },
  { value: "tcu", label: "Tribal College or University (TCU)" },
  { value: "ivy_league", label: "Ivy League" },
  { value: "single_gender", label: "Single-Gender" },
  { value: "religious_affiliation", label: "Religious Affiliation" },
  { value: "community_college", label: "Community College" },
  { value: "military_academy", label: "Military Academy" },
  { value: "technical_college", label: "Technical/Trade College" },
  { value: "no_preference", label: "No Preference" }
]

const states = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AS", label: "American Samoa" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "DC", label: "District of Columbia" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  { value: "GU", label: "Guam" },
  { value: "HI", label: "Hawaii" },
  { value: "ID", label: "Idaho" },
  { value: "IL", label: "Illinois" },
  { value: "IN", label: "Indiana" },
  { value: "IA", label: "Iowa" },
  { value: "KS", label: "Kansas" },
  { value: "KY", label: "Kentucky" },
  { value: "LA", label: "Louisiana" },
  { value: "ME", label: "Maine" },
  { value: "MD", label: "Maryland" },
  { value: "MA", label: "Massachusetts" },
  { value: "MI", label: "Michigan" },
  { value: "MN", label: "Minnesota" },
  { value: "MS", label: "Mississippi" },
  { value: "MO", label: "Missouri" },
  { value: "MT", label: "Montana" },
  { value: "NE", label: "Nebraska" },
  { value: "NV", label: "Nevada" },
  { value: "NH", label: "New Hampshire" },
  { value: "NJ", label: "New Jersey" },
  { value: "NM", label: "New Mexico" },
  { value: "NY", label: "New York" },
  { value: "NC", label: "North Carolina" },
  { value: "ND", label: "North Dakota" },
  { value: "MP", label: "Northern Mariana Islands" },
  { value: "OH", label: "Ohio" },
  { value: "OK", label: "Oklahoma" },
  { value: "OR", label: "Oregon" },
  { value: "PA", label: "Pennsylvania" },
  { value: "PR", label: "Puerto Rico" },
  { value: "RI", label: "Rhode Island" },
  { value: "SC", label: "South Carolina" },
  { value: "SD", label: "South Dakota" },
  { value: "TN", label: "Tennessee" },
  { value: "TX", label: "Texas" },
  { value: "UT", label: "Utah" },
  { value: "VT", label: "Vermont" },
  { value: "VA", label: "Virginia" },
  { value: "VI", label: "U.S. Virgin Islands" },
  { value: "WA", label: "Washington" },
  { value: "WV", label: "West Virginia" },
  { value: "WI", label: "Wisconsin" },
  { value: "WY", label: "Wyoming" },
]

const mileageRadii = [
  { value: "0-20", label: "0-20 miles (short drive)" },
  { value: "21-50", label: "21-50 miles (medium drive)" },
  { value: "51-200", label: "51-200 miles (long drive)" },
  { value: "201-500", label: "201-500 miles (short flight" },
  { value: "501+", label: "501+ miles (long flight)" },
]

const zipCodeRegex = /^\d{5}(-\d{4})?$/

// Enhanced Zod Schema for form validation with step-specific validation
const formSchema = z.object({
  unweighted_gpa: z.string().min(1, "Unweighted GPA is required").regex(/^(4(\.0{1,2})?|[0-3](\.\d{1,2})?)$/, "Invalid GPA format"),
  weighted_gpa: z
  .string()
  .transform(val => val.trim() === "" ? undefined : val)
  .optional()
  .refine(
    val => val === undefined || /^(5(\.0{1,2})?|[0-4](\.\d{1,2})?)$/.test(val),
    { message: "Invalid GPA format" }
  )
,
  sat_math_score: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseInt(val)
      return !isNaN(num) && num >= 200 && num <= 800
    }, "SAT Math score must be between 200 and 800"),
  sat_reading_score:z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseInt(val)
      return !isNaN(num) && num >= 200 && num <= 800
    }, "SAT Reading/Writing score must be between 200 and 800"),
  act_score: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseInt(val)
      return !isNaN(num) && num >= 1 && num <= 36
    }, "ACT score must be between 1 and 36"),
  high_school_name: z.string().min(1, "High school name is required"),
  graduation_year: z.string().min(1, "Graduation year is required"),
  intended_majors: z.array(z.string()).optional(),
  extracurriculars: z.string().optional(),
  interests: z.string().optional(),
  college_type_preferences: z.array(z.string()).optional(),
  location_type_preferences: z.array(z.string()).optional(),
  geographic_preference_type: z.enum(["states", "zip", "none"]).default("states"),
  preferred_states: z.array(z.string()).optional(),
  preferred_zip: z.string().optional(),
  preferred_radius: z.string().optional(),
  country: z.string().default("usa"),
  current_zip_code: z.string().optional(),
  gender: z.string().optional(),
  ethnicity: z.string().optional(),
  household_income: z.string().optional(),
  financial_aid_need: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.geographic_preference_type === "zip") {
    if (!data.preferred_zip || data.preferred_zip.trim() === "") {
      ctx.addIssue({
        path: ["preferred_zip"],
        code: z.ZodIssueCode.custom,
        message: "Preferred ZIP code is required."
      })
    } else if (!zipCodeRegex.test(data.preferred_zip)) {
      ctx.addIssue({
        path: ["preferred_zip"],
        code: z.ZodIssueCode.custom,
        message: "ZIP code must be in 5-digit (12345) or 5+4 (12345-6789) format."
      })
    }
  }

  if (data.country === "usa") {
    if (!data.current_zip_code || data.current_zip_code.trim() === "") {
      ctx.addIssue({
        path: ["current_zip_code"],
        code: z.ZodIssueCode.custom,
        message: "ZIP code is required for USA addresses.",
      })
    } else if (!zipCodeRegex.test(data.current_zip_code)) {
      ctx.addIssue({
        path: ["current_zip_code"],
        code: z.ZodIssueCode.custom,
        message: "ZIP code must be in 5-digit (12345) or 5+4 (12345-6789) format.",
      })
    }
  }
})

// Enhanced Stepper Component with better visual indication
const Stepper = ({ steps, currentStep }) => (
  <div className="flex justify-between mb-8 mt-8">
    {steps.map((step, index) => (
      <StepperItem
        key={step.id}
        number={index + 1}
        label={step.name}
        isActive={currentStep === index}
        isCompleted={currentStep > index}
        isLast={index === steps.length - 1}
        isNextStepActiveOrCompleted={currentStep >= index + 1}
      />
    ))}
  </div>
)

const StepperItem = ({ number, label, isActive, isCompleted, isLast, isNextStepActiveOrCompleted }) => (
  <div className={cn(
    "relative flex-1",
    !isLast && "after:content-[''] after:absolute after:top-3 after:left-1/2 after:w-full after:h-0.5 after:z-0",
    !isLast && isNextStepActiveOrCompleted ? "after:bg-primary" : "after:bg-gray-200"
  )}>
    <div className="flex flex-col items-center relative z-10">
      <div className={cn(
        "flex justify-center items-center w-6 h-6 rounded-full text-xs font-semibold transition-colors",
        isActive ? "bg-primary text-white ring-2 ring-primary ring-offset-2" :
        isCompleted ? "bg-primary text-white" : "bg-gray-200 text-gray-600"
      )}>
        {isCompleted && !isActive ? <Check className="w-3 h-3" /> : number}
      </div>
      <div className={cn(
        "text-xs mt-2 font-medium text-center max-w-20",
        isActive || isCompleted ? "text-primary" : "text-gray-600"
      )}>
        {label}
      </div>
    </div>
  </div>
)

// Enhanced TooltipIcon Component
const TooltipIcon = ({ tooltipText }) => (
  <TooltipProvider>
    <Tooltip>
      <TooltipTrigger type="button" className="ml-1">
        <HelpCircle className="w-4 h-4 text-gray-400 hover:text-gray-600 transition-colors" />
      </TooltipTrigger>
      <TooltipContent className="w-60">
        <p>{tooltipText}</p>
      </TooltipContent>
    </Tooltip>
  </TooltipProvider>
)

// Enhanced MultiSelectCombobox Component with better UX
const MultiSelectCombobox = ({ field, options, placeholder }) => {
  const [open, setOpen] = React.useState(false)
  const selectedValues = field.value || []

  const handleSelect = (currentValue) => {
    const newSelectedValues = selectedValues.includes(currentValue)
      ? selectedValues.filter((v) => v !== currentValue)
      : [...selectedValues, currentValue]
    field.onChange(newSelectedValues)
  }

  const handleRemove = (valueToRemove) => {
    field.onChange(selectedValues.filter((v) => v !== valueToRemove))
  }

  const clearAll = () => {
    field.onChange([])
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between h-auto min-h-[2.5rem] py-2 px-3"
        >
          <div className="flex flex-wrap gap-1 items-center flex-1">
            {selectedValues.length > 0
              ? selectedValues.map(val => {
                  const option = options.find(opt => opt.value === val)
                  return (
                    <Badge key={val} variant="secondary" className="mr-1">
                      {option ? option.label : val}
                      <span
                        role="button"
                        tabIndex={0}
                        aria-label={`Remove ${option?.label || val}`}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRemove(val)
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault()
                            handleRemove(val)
                          }
                        }}
                        className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer"
                      >
                        <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                      </span>
                    </Badge>
                  )
                })
              : <span className="text-muted-foreground">{placeholder}</span>
            }
          </div>
          <div className="flex items-center gap-1">
            {selectedValues.length > 0 && (
              <span
                role="button"
                tabIndex={0}
                aria-label="Clear all selections"
                onClick={(e) => {
                  e.stopPropagation()
                  clearAll()
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    clearAll()
                  }
                }}
                className="text-muted-foreground hover:text-foreground cursor-pointer"
              >
                <X className="h-4 w-4" />
              </span>
            )}
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command>
          <CommandInput placeholder={`Search ${placeholder.toLowerCase()}...`} />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value) ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

// Academic Form Component
const AcademicForm = ({ form }) => (
  <Card className="
    form-card active bg-white p-6 rounded-lg shadow-sm mb-6
    border-primary
  ">
    <CardHeader className="p-0 mb-4">
      <CardTitle className="text-lg font-semibold text-gray-800">Your Academic Journey</CardTitle>
    </CardHeader>
    <CardContent className="p-0">
      <Alert className="bg-primary/5 border-l-4 border-t-0 border-r-0 border-b-0 border-primary p-3 mb-4 rounded-md text-sm">
        <AlertDescription>
          Note: If both GPAs are provided, we convert your Weighted GPA to a 4.0 scale and use the higher of the Unweighted and converted Weighted GPA to match how colleges report GPA. We always use the most favorable GPA for your college recommendations.
        </AlertDescription>
      </Alert>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <FormField name="unweighted_gpa" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center">
              Unweighted GPA (4.0 Scale) 
              <TooltipIcon tooltipText="Enter your GPA on a 4.0 scale, without extra points for AP/IB or honors classes." />
            </FormLabel>
            <FormControl>
              <Input placeholder="e.g. 3.7" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
        <FormField name="weighted_gpa" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel className="flex items-center">
              Weighted GPA (e.g., 5.0 Scale) 
              <TooltipIcon tooltipText="Enter your GPA if your school uses a weighted scale (e.g., 5.0), including points for AP/IB/honors." />
            </FormLabel>
            <FormControl>
              <Input placeholder="e.g. 4.2" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <FormField name="sat_math_score" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>SAT Math Score (optional)</FormLabel>
            <FormControl>
              <Input placeholder="200-800" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
        <FormField name="sat_reading_score" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>SAT Reading/Writing Score (optional)</FormLabel>
            <FormControl>
              <Input placeholder="200-800" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
      </div>
      <div className="mb-4">
        <FormField name="act_score" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>ACT Composite Score (optional)</FormLabel>
            <FormControl>
              <Input placeholder="1-36" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
      </div>
      <div className="mb-4">
        <FormField name="high_school_name" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>Current High School Name *</FormLabel>
            <FormControl>
              <Input placeholder="Enter your high school name" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
      </div>
      <FormField name="graduation_year" control={form.control} render={({ field }) => (
        <FormItem>
          <FormLabel>High School Graduation Year *</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select year" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              {graduationYears.map(opt => (
                <SelectItem key={opt.value} value={opt.value}>
                  {opt.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )} />
    </CardContent>
  </Card>
)

// Enhanced Interests Form Component with improved location type logic
const InterestsForm = ({ form }) => {
  const locationPreferenceType = form.watch("geographic_preference_type")
  const locationTypes = form.watch("location_type_preferences") || []

  // Handle location type changes with mutual exclusivity for "No Preference"
  const handleLocationTypeChange = (value, checked) => {
    const currentValues = locationTypes
    
    if (value === "no_preference") {
      // If selecting "No Preference", clear all other selections
      if (checked) {
        form.setValue("location_type_preferences", ["no_preference"])
      } else {
        form.setValue("location_type_preferences", [])
      }
    } else {
      // If selecting any other option, remove "No Preference" if it exists
      let newValues = checked
        ? [...currentValues.filter(v => v !== "no_preference"), value]
        : currentValues.filter(v => v !== value)
      
      form.setValue("location_type_preferences", newValues)
    }
  }

  const handleCollegeTypeChange = (newValues) => {
    const lastSelected = newValues[newValues.length - 1]
    const hasNoPreference = newValues.includes("no_preference")
  
    if (lastSelected === "no_preference") {
      // User just selected "no_preference" → reset to only that
      form.setValue("college_type_preferences", ["no_preference"])
    } else if (hasNoPreference) {
      // User selected something else while "no_preference" was selected → remove it
      form.setValue("college_type_preferences", newValues.filter((v) => v !== "no_preference"))
    } else {
      // Normal behavior
      form.setValue("college_type_preferences", newValues)
    }
  }

  // Handle geographic preference type changes with data clearing
  const handleGeographicPreferenceChange = (newType) => {
    const currentType = form.getValues("geographic_preference_type")
    
    if (currentType !== newType) {
      // Clear related fields when switching types
      if (currentType === "states") {
        form.setValue("preferred_states", [])
      } else if (currentType === "zip") {
        form.setValue("preferred_zip", "")
        form.setValue("preferred_radius", "")
      }
    }
    
    form.setValue("geographic_preference_type", newType)
  }

  return (
    <Card className="
      form-card active bg-white p-6 rounded-lg shadow-sm mb-6
      border-primary
    ">
      <CardHeader className="p-0 mb-4">
        <CardTitle className="text-lg font-semibold text-gray-800">College Interests & Preferences</CardTitle>
      </CardHeader>
      <CardContent className="p-0 space-y-4">
        {/* Intended Major Selection with comprehensive search */}
        <FormField control={form.control} name="intended_majors" render={({ field }) => (
          <FormItem>
            <FormLabel>Intended Major(s)</FormLabel>
            <MultiSelectCombobox 
              field={field} 
              options={majors} 
              placeholder="Search and select majors..." 
            />
            <FormDescription>
              You can search and select multiple majors. Type to search through our comprehensive list.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )} />

        {/* Extracurricular Activities with auto-expanding textarea */}
        <FormField control={form.control} name="extracurriculars" render={({ field }) => (
          <FormItem>
            <FormLabel>Extracurricular Activities</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="List your key extracurricular activities, sports, clubs, volunteer work, leadership roles, etc."
                className="min-h-[100px] resize-none"
                {...field} 
              />
            </FormControl>
            {/* <FormDescription>
              Include activities, sports, clubs, volunteer work, jobs, and leadership positions.
            </FormDescription> */}
            <FormMessage />
          </FormItem>
        )} />

        {/* Interests & Hobbies with auto-expanding textarea */}
        <FormField control={form.control} name="interests" render={({ field }) => (
          <FormItem>
            <FormLabel>Interests & Hobbies</FormLabel>
            <FormControl>
              <Textarea 
                placeholder="Share your personal interests and hobbies. What do you enjoy doing in your free time? What are you passionate about?"
                className="min-h-[100px] resize-none"
                {...field} 
              />
            </FormControl>
            {/* <FormDescription>
              Tell us about your passions, creative pursuits, and what you enjoy doing outside of school.
            </FormDescription> */}
            <FormMessage />
          </FormItem>
        )} />

        {/* College Type Preferences with comprehensive options */}
        <FormField control={form.control} name="college_type_preferences" render={({ field }) => (
          <FormItem>
            <FormLabel>College Type Preferences</FormLabel>
            <MultiSelectCombobox 
              field={{
                ...field,
                onChange: (newValues) => {
                  handleCollegeTypeChange(newValues)
                }
              }} 
              options={collegeTypes} 
              placeholder="Search and select college types..." 
            />
            <FormDescription>
              Select the types of colleges you're interested in. You can choose multiple options.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )} />

        {/* Location Type Preferences with mutual exclusivity */}
        <FormField control={form.control} name="location_type_preferences" render={({ field }) => (
          <FormItem>
            <FormLabel>Location Type Preferences</FormLabel>
            <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-4 gap-y-2 mt-2">
              {[
                { value: "city", label: "City" },
                { value: "suburban", label: "Suburban" },
                { value: "small_town", label: "Small Town" },
                { value: "rural", label: "Rural" },
                { value: "no_preference", label: "No Preference" }
              ].map((item) => (
                <FormItem key={item.value} className="flex flex-row items-start space-x-3 space-y-0">
                  <FormControl>
                    <Checkbox
                      checked={locationTypes.includes(item.value)}
                      onCheckedChange={(checked) => handleLocationTypeChange(item.value, checked)}
                    />
                  </FormControl>
                  <FormLabel className="font-normal">{item.label}</FormLabel>
                </FormItem>
              ))}
            </div>
            <FormDescription>
              Select your preferred campus environment types. "No Preference" will clear other selections.
            </FormDescription>
            <FormMessage />
          </FormItem>
        )} />

        {/* Geographic Preferences with enhanced data clearing */}
        <div className="pt-4 border-t border-gray-200">
          <h4 className="text-sm font-medium text-gray-700 mb-2">Geographic Preferences (Optional)</h4>
          <FormField control={form.control} name="geographic_preference_type" render={({ field }) => (
            <FormItem className="space-y-3">
              <FormLabel className="text-sm">How would you like to specify your geographic preference?</FormLabel>
              <FormControl>
                <RadioGroup 
                  onValueChange={handleGeographicPreferenceChange} 
                  defaultValue={field.value} 
                  className="flex flex-wrap gap-x-6 gap-y-2 mt-2"
                >
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <RadioGroupItem value="states" />
                    </FormControl>
                    <FormLabel className="font-normal">By U.S. States/Territories</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <RadioGroupItem value="zip" />
                    </FormControl>
                    <FormLabel className="font-normal">By ZIP Code & Radius</FormLabel>
                  </FormItem>
                  <FormItem className="flex items-center space-x-2">
                    <FormControl>
                      <RadioGroupItem value="none" />
                    </FormControl>
                    <FormLabel className="font-normal">No Geographic Preference</FormLabel>
                  </FormItem>
                </RadioGroup>
              </FormControl>
              <FormMessage />
            </FormItem>
          )} />

          {/* State Selection */}
          {locationPreferenceType === "states" && (
            <FormField control={form.control} name="preferred_states" render={({ field }) => (
              <FormItem className="mt-4">
                <FormLabel className="text-sm">Preferred U.S. States/Territories</FormLabel>
                <MultiSelectCombobox 
                  field={field} 
                  options={states} 
                  placeholder="Search and select states..." 
                />
                <FormDescription>
                  Select the states and territories where you'd like to attend college. You can choose multiple options.
                </FormDescription>
                <FormMessage />
              </FormItem>
            )} />
          )}

          {/* ZIP Code and Radius Selection */}
          {locationPreferenceType === "zip" && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormField control={form.control} name="preferred_zip" render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Preferred ZIP Code</FormLabel>
                  <FormControl>
                    <Input placeholder="Enter ZIP code" {...field} />
                  </FormControl>
                  <FormDescription>
                    Enter the ZIP code you'd like to be near.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
              <FormField control={form.control} name="preferred_radius" render={({ field }) => (
                <FormItem>
                  <FormLabel className="text-sm">Preferred Mileage Radius</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select radius" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {mileageRadii.map(opt => (
                        <SelectItem key={opt.value} value={opt.value}>
                          {opt.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormDescription>
                    How far are you willing to travel from the ZIP code?
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}

// Demographics Form Component
const DemographicsForm = ({ form }) => (
  <Card className="
    form-card active bg-white p-6 rounded-lg shadow-sm mb-6
    border-primary
  ">
    <CardHeader className="p-0 mb-4">
      <CardTitle className="text-lg font-semibold text-gray-800">Tell Us About Yourself</CardTitle>
    </CardHeader>
    <CardContent className="p-0 space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField name="country" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>Country</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select country" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="usa">USA</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )} />
        <FormField name="current_zip_code" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>ZIP Code (if in USA)</FormLabel>
            <FormControl>
              <Input placeholder="e.g. 90210" {...field} />
            </FormControl>
            <FormMessage />
          </FormItem>
        )} />
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <FormField name="gender" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>Gender (optional)</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select gender" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="female">Female</SelectItem>
                <SelectItem value="male">Male</SelectItem>
                <SelectItem value="non_binary">Non-binary</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )} />
        <FormField name="ethnicity" control={form.control} render={({ field }) => (
          <FormItem>
            <FormLabel>Ethnicity (optional)</FormLabel>
            <Select onValueChange={field.onChange} defaultValue={field.value}>
              <FormControl>
                <SelectTrigger>
                  <SelectValue placeholder="Select ethnicity" />
                </SelectTrigger>
              </FormControl>
              <SelectContent>
                <SelectItem value="black">Black or African American</SelectItem>
                <SelectItem value="east_asian">East Asian</SelectItem>
                <SelectItem value="hispanic">Hispanic or Latine</SelectItem>
                <SelectItem value="middle_eastern">Middle Eastern or North African</SelectItem>
                <SelectItem value="native_american">Native American or Alaska Native</SelectItem>
                <SelectItem value="native_hawaiian">Native Hawaiian or Other Pacific Islander</SelectItem>
                <SelectItem value="south_asian">South Asian</SelectItem>
                <SelectItem value="white">White or Caucasian</SelectItem>
                <SelectItem value="multiracial">Multiracial</SelectItem>
                <SelectItem value="other">Other</SelectItem>
              </SelectContent>
            </Select>
            <FormMessage />
          </FormItem>
        )} />
      </div>
      <FormField name="household_income" control={form.control} render={({ field }) => (
        <FormItem>
          <FormLabel>Household Income (optional)</FormLabel>
          <Select onValueChange={field.onChange} defaultValue={field.value}>
            <FormControl>
              <SelectTrigger>
                <SelectValue placeholder="Select income range" />
              </SelectTrigger>
            </FormControl>
            <SelectContent>
              <SelectItem value="under_30k">Under $30,000</SelectItem>
              <SelectItem value="30k_48k">$30,001 - $48,000</SelectItem>
              <SelectItem value="48k_75k">$48,001 - $75,000</SelectItem>
              <SelectItem value="75k_100k">$75,001 - $100,000</SelectItem>
              <SelectItem value="100k_150k">$110,001 - $150,000</SelectItem>
              <SelectItem value="150k_plus">$150,001+</SelectItem>
            </SelectContent>
          </Select>
          <FormMessage />
        </FormItem>
      )} />
      <FormField control={form.control} name="financial_aid_need" render={({ field }) => (
        <FormItem className="space-y-2">
          <FormLabel>Financial Aid Need</FormLabel>
          <FormControl>
            <RadioGroup 
              onValueChange={field.onChange} 
              defaultValue={field.value} 
              className="flex flex-wrap gap-x-4 gap-y-2 mt-1"
            >
              <FormItem className="flex items-center space-x-2">
                <FormControl>
                  <RadioGroupItem value="high" />
                </FormControl>
                <FormLabel className="font-normal">High Need</FormLabel>
              </FormItem>
              <FormItem className="flex items-center space-x-2">
                <FormControl>
                  <RadioGroupItem value="medium" />
                </FormControl>
                <FormLabel className="font-normal">Medium Need</FormLabel>
              </FormItem>
              <FormItem className="flex items-center space-x-2">
                <FormControl>
                  <RadioGroupItem value="low" />
                </FormControl>
                <FormLabel className="font-normal">Low Need</FormLabel>
              </FormItem>
              <FormItem className="flex items-center space-x-2">
                <FormControl>
                  <RadioGroupItem value="no_need" />
                </FormControl>
                <FormLabel className="font-normal">No Need</FormLabel>
              </FormItem>
            </RadioGroup>
          </FormControl>
          <FormDescription>
            This helps us find colleges with appropriate financial aid opportunities.
          </FormDescription>
          <FormMessage />
        </FormItem>
      )} />
    </CardContent>
  </Card>
)

// Enhanced Review Form Component with better data display
const ReviewForm = ({ form }) => {
  const data = form.watch()
  
  const getSelectLabel = (options, value) => 
    options.find(opt => opt.value === value)?.label || value || '-'
  
  const getMultiSelectLabels = (options, values) => 
    values?.length ? values.map(v => getSelectLabel(options, v)).join(', ') : '-'

  const formatLocationTypes = (types) => {
    if (!types || types.length === 0) return '-'
    return types.map(type => {
      switch(type) {
        case 'city': return 'City'
        case 'suburban': return 'Suburban'
        case 'small_town': return 'Small Town'
        case 'rural': return 'Rural'
        case 'no_preference': return 'No Preference'
        default: return type
      }
    }).join(', ')
  }

  const COUNTRY_LABELS = {
    usa: "USA",
    other: "Other",
  };
  
  const GENDER_LABELS = {
    female: "Female",
    male: "Male",
    non_binary: "Non-binary",
    other: "Other",
    prefer_not_to_say: "Prefer not to say",
  };
  
  const ETHNICITY_LABELS = {
    native_american: "Native American or Alaska Native",
    east_asian: "East Asian",
    south_asian: "South Asian",
    black: "Black or African American",
    hispanic: "Hispanic or Latine",
    middle_eastern: "Middle Eastern or North African",
    native_hawaiian: "Native Hawaiian or Other Pacific Islander",
    white: "White",
    multiracial: "Multiracial",
    other: "Other",
  };
  
  const HOUSEHOLD_INCOME_LABELS = {
    under_30k: "Under $30,000",
    "30k_48k": "$30,001 - $48,000",
    "48k_75k": "$48,001 - $75,000",
    "75k_100k": "$75,001 - $100,000",
    "100k_150k": "$100,001 - $150,000",
    "150k_plus": "$150,001+",
  };
  
  const FINANCIAL_AID_NEED_LABELS = {
    high: "High Need",
    medium: "Medium Need",
    low: "Low Need",
    "no_need": "No Need",
  };

  function formatLabel(value, map) {
    return map?.[value] || "-";
  }

  return (
    <Card className="
      form-card active bg-white p-6 rounded-lg shadow-sm mb-6
      border-primary
    ">
      <CardHeader className="p-0 mb-4">
        <CardTitle className="text-lg font-semibold text-gray-800">Almost There! Let's Review</CardTitle>
        <p className="text-sm text-gray-600">Please review your information before submitting.</p>
      </CardHeader>
      <CardContent className="p-0 space-y-4">
        {/* Academic Information */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2 text-gray-700">Academic Information</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-sm">
            <div>Unweighted GPA:</div>
            <div className="font-medium text-gray-800">{data.unweighted_gpa || '-'}</div>
            <div>Weighted GPA:</div>
            <div className="font-medium text-gray-800">{data.weighted_gpa || '-'}</div>
            <div>SAT Math:</div>
            <div className="font-medium text-gray-800">{data.sat_math_score || '-'}</div>
            <div>SAT Reading/Writing:</div>
            <div className="font-medium text-gray-800">{data.sat_reading_score || '-'}</div>
            <div>ACT Score:</div>
            <div className="font-medium text-gray-800">{data.act_score || '-'}</div>
            <div>High School:</div>
            <div className="font-medium text-gray-800">{data.high_school_name || '-'}</div>
            <div>Graduation Year:</div>
            <div className="font-medium text-gray-800">{getSelectLabel(graduationYears, data.graduation_year)}</div>
          </div>
        </div>

        {/* Interests & Preferences */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2 text-gray-700">Interests & Preferences</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-sm">
            <div className="font-medium">Intended Major(s):</div>
            <div className="text-gray-800 mt-1">{getMultiSelectLabels(majors, data.intended_majors)}</div>

            <div className="font-medium">Extracurriculars:</div>
            <div className="text-gray-800 mt-1">{data.extracurriculars || '-'}</div>

            <div className="font-medium">Interests/Hobbies:</div>
            <div className="text-gray-800 mt-1">{data.interests || '-'}</div>

            <div className="font-medium">College Type:</div>
            <div className="text-gray-800 mt-1">{getMultiSelectLabels(collegeTypes, data.college_type_preferences)}</div>

            <div className="font-medium">Location Type:</div>
            <div className="text-gray-800 mt-1">{formatLocationTypes(data.location_type_preferences)}</div>

            {data.geographic_preference_type === "states" && (
              <>
                <div className="font-medium">Preferred States:</div>
                <div className="text-gray-800 mt-1">{getMultiSelectLabels(states, data.preferred_states)}</div>
              </>
            )}

            {data.geographic_preference_type === "zip" && (
              <>
                  <div className="font-medium">Preferred ZIP:</div>
                  <div className="text-gray-800 mt-1">{data.preferred_zip || '-'}</div>
                  <div className="font-medium">Mileage Radius:</div>
                  <div className="text-gray-800 mt-1">{getSelectLabel(mileageRadii, data.preferred_radius)}</div>
              </>
            )}
          </div>
        </div>

        {/* Demographics */}
        <div className="bg-gray-50 p-4 rounded-lg">
          <h4 className="font-medium mb-2 text-gray-700">Demographics & Financial Information</h4>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-x-4 gap-y-1 text-sm">
            <div>Country:</div>
            <div className="font-medium text-gray-800">{formatLabel(data.country, COUNTRY_LABELS)}</div>

            <div>ZIP Code:</div>
            <div className="font-medium text-gray-800">{data.current_zip_code || '-'}</div>

            <div>Gender:</div>
            <div className="font-medium text-gray-800">{formatLabel(data.gender, GENDER_LABELS)}</div>

            <div>Ethnicity:</div>
            <div className="font-medium text-gray-800">{formatLabel(data.ethnicity, ETHNICITY_LABELS)}</div>

            <div>Household Income:</div>
            <div className="font-medium text-gray-800">{formatLabel(data.household_income, HOUSEHOLD_INCOME_LABELS)}</div>

            <div>Financial Aid Need:</div>
            <div className="font-medium text-gray-800">{formatLabel(data.financial_aid_need, FINANCIAL_AID_NEED_LABELS)}</div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}

// Main Page Component with enhanced navigation and validation
export default function ProfileSetupPage() {
  const [currentStep, setCurrentStep] = React.useState(0)
  const router = useRouter()
  const { updateProfileData } = useAuth()

  const steps = [
    { id: "academic", name: "Academic", component: AcademicForm },
    { id: "interests", name: "Interests & Preferences", component: InterestsForm },
    { id: "demographics", name: "Demographics", component: DemographicsForm },
    { id: "review", name: "Review", component: ReviewForm },
  ]
  const totalSteps = steps.length

  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      unweighted_gpa: "",
      weighted_gpa: "",
      sat_math_score: "",
      sat_reading_score: "",
      act_score: "",
      high_school_name: "",
      graduation_year: "",
      intended_majors: [],
      extracurriculars: "",
      interests: "",
      college_type_preferences: [],
      location_type_preferences: [],
      geographic_preference_type: "states",
      preferred_states: [],
      preferred_zip: "",
      preferred_radius: "",
      country: "usa",
      current_zip_code: "",
      gender: "",
      ethnicity: "",
      household_income: "",
      financial_aid_need: "",
    },
  })

  // Enhanced form submission with better error handling
  const onSubmit = (data) => {
    const mappedData = {
      ...data,
      unweighted_gpa: data.unweighted_gpa === "" ? null : data.unweighted_gpa,
      weighted_gpa: data.weighted_gpa === "" ? null : data.weighted_gpa,
      sat_math_score: data.sat_math_score === "" ? null : data.sat_math_score,
      sat_reading_score: data.sat_reading_score === "" ? null : data.sat_reading_score,
      act_score: data.act_score === "" ? null : data.act_score,
    }
    updateProfileData(mappedData)
    router.push("/profile-setup/signup/")
  }

  // Enhanced step validation
  const validateCurrentStep = async () => {
    let fieldsToValidate = []
    
    switch (currentStep) {
      case 0: // Academic step
        fieldsToValidate = ["unweighted_gpa", "weighted_gpa", "high_school_name", "graduation_year", "sat_math_score", "sat_reading_score", "act_score"]
        break
      case 1: // Interests step
        fieldsToValidate = ["preferred_zip"]
        break
      case 2: // Demographics step
        fieldsToValidate = ["current_zip_code"]
        break
      case 3: // Review step
        // Validate all required fields
        fieldsToValidate = ["high_school_name", "graduation_year"]
        break
      default:
        return true
    }

    if (fieldsToValidate.length > 0) {
      const isValid = await form.trigger(fieldsToValidate)
      return isValid
    }
    
    return true
  }

  // Enhanced next step function with validation
  const nextStep = async () => {
    const isValid = await validateCurrentStep()
    
    if (!isValid) {
      // Scroll to first error
      const firstError = document.querySelector('[data-invalid="true"]')
      if (firstError) {
        firstError.scrollIntoView({ behavior: 'smooth', block: 'center' })
      }
      return
    }

    if (currentStep < totalSteps - 1) {
      setCurrentStep(prev => prev + 1)
      window.scrollTo({ top: 0, behavior: 'smooth' })
    } else {
      // Final step - submit the form
      form.handleSubmit(onSubmit)()
    }
  }

  // Enhanced previous step function
  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(prev => prev - 1)
      window.scrollTo({ top: 0, behavior: 'smooth' })
    }
  }

  const CurrentFormComponent = steps[currentStep].component

  return (
    <div className="max-w-3xl mx-auto p-4 md:p-6 bg-gray-50 min-h-screen">
      {/* Enhanced stepper styles */}
      {/* <style jsx global>{`
        .stepper-item {
          position: relative;
          flex: 1;
        }
        .stepper-item::after {
          content: '';
          position: absolute;
          top: 12px;
          left: 50%;
          width: 100%;
          height: 2px;
          background-color: #E5E7EB;
          z-index: 0;
        }
        .stepper-item:last-child::after {
          display: none;
        }
        :root {
          --primary: 142 69 173;
          --primary-rgb: 142, 69, 173;
        }
      `}</style> */}

      <header className="mb-8">
        <div className="flex items-center justify-between mb-6">
          <div className="flex items-center">
            <img 
              alt="Trailblazer Logo" 
              className="h-6" 
              src="/logo.svg" 
            />
          </div>
          <div className="text-sm text-gray-500 hidden sm:inline">Find your perfect college match</div>
        </div>
        <div className="mb-4">
          <h2 className="text-xl font-semibold mb-2 text-black">Let's Get to Know You!</h2>
          <p className="text-gray-600">
            Share a bit about yourself so we can find colleges that are the perfect fit for you.
          </p>
        </div>
        <Stepper steps={steps} currentStep={currentStep} />
      </header>

      <main className="mb-24">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)}>
            <CurrentFormComponent form={form} />
          </form>
        </Form>
      </main>

      {/* Enhanced footer with better styling */}
      <footer className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-lg z-50">
        <div className="flex justify-between items-center max-w-3xl mx-auto">
          {currentStep > 0 ? (
            <Button 
              variant="outline" 
              onClick={prevStep} 
              className="flex items-center hover:bg-gray-50"
            >
              <ArrowLeft className="w-4 h-4 mr-2" /> 
              Back
            </Button>
          ) : (
            <div className="w-[152px]" />
          )}
          
          <div className="text-sm text-gray-500 font-medium hidden sm:inline">
            Step {currentStep + 1} of {totalSteps}
          </div>
          
          <Button 
            onClick={nextStep} 
            disabled={form.formState.isSubmitting} 
            className="flex items-center bg-primary text-white hover:bg-primary hover:text-white md:hover:bg-green-600 md:hover:text-white w-[151px]"
          >
            {currentStep === totalSteps - 1 ? "Find Colleges" : "Next"}
            {currentStep < totalSteps - 1 && <ArrowRight className="w-4 h-4 ml-2" />}
          </Button>
        </div>
      </footer>
    </div>
  )
}