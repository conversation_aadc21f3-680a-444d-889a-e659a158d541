from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from django.contrib.auth import get_user_model
from django.test import TestCase
from rest_framework.test import APIClient
from api.users.models import HighSchoolStudentProfile, CollegeStudentProfile, User
from api.universities.models import University, ShortlistItem

class RecommendationsViewTests(APITestCase):
    def setUp(self):
        User = get_user_model()
        # Create a high school student user.
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        # Use get_or_create to avoid unique constraint errors if profile already exists.
        self.profile, _ = HighSchoolStudentProfile.objects.get_or_create(
            user=self.user,
            defaults={
                "recommendations_status": "not_requested",
                "university_recommendations": None
            }
        )
        # Authenticate the client.
        self.client.force_authenticate(user=self.user)
        self.url = reverse("recommendations")

    def test_recommendations_ready(self):
        """
        When recommendations_status is 'ready' and recommendations exist, the view
        should return serialized university data.
        """
        # Create dummy University objects.
        uni1 = University.objects.create(unitid=1001, institution="Alpha University")
        uni2 = University.objects.create(unitid=1002, institution="Beta College")
        # Update profile with dummy recommendations and mark as ready.
        dummy_recommendations = {"Reach": [uni1.unitid, uni2.unitid], "Target": [], "Safety": []}
        HighSchoolStudentProfile.objects.filter(pk=self.profile.pk).update(
            recommendations_status="ready",
            university_recommendations=dummy_recommendations
        )
        self.profile.refresh_from_db()

        response = self.client.get(self.url, {"ordering": "institution"})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn("Reach", response.data)
        # Further assertions to check serialized data can go here

    def test_no_recommendations_available(self):
        """
        When the profile has no university recommendations, the view should return 404.
        """
        HighSchoolStudentProfile.objects.filter(pk=self.profile.pk).update(
            university_recommendations=None
        )
        self.profile.refresh_from_db()

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertIn("detail", response.data)
        self.assertEqual(response.data["detail"], "No recommendations available.")

    def test_invalid_ordering_field(self):
        """
        Providing an invalid ordering field should return a 400 with a descriptive message.
        """
        uni = University.objects.create(unitid=2001, institution="Gamma Institute")
        dummy_recommendations = {"Reach": [uni.unitid], "Target": [], "Safety": []}
        HighSchoolStudentProfile.objects.filter(pk=self.profile.pk).update(
            recommendations_status="ready",
            university_recommendations=dummy_recommendations
        )
        self.profile.refresh_from_db()

        response = self.client.get(self.url, {"ordering": "nonexistent_field"})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn("detail", response.data)
        self.assertIn("Invalid sorting field", response.data["detail"])

    def test_unauthenticated_access(self):
        """
        Requests without authentication should return 401.
        """
        self.client.force_authenticate(user=None)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertIn("detail", response.data)

class UniversityDetailViewTests(TestCase):
    """Tests for the UniversityDetailView"""
    
    def setUp(self):
        """Set up test data"""
        self.client = APIClient()
        
        # Create a test university
        self.university = University.objects.create(
            unitid="123456",
            institution="Test University",
            city="Test City",
            state_territory="Test State",
            zip_code="12345",
            institution_website="https://www.testuniversity.edu",
            public_private="Public",
            locale="Urban",
            undergrad_count=15000,
            student_faculty_ratio=0.1,
            acceptance_rate=0.65,
            sat_reading_25th=550,
            sat_reading_75th=650,
            sat_math_25th=560,
            sat_math_75th=670,
            act_25th=25,
            act_75th=30,
            retention_rate=0.85,
            graduation_rate=0.78,
            has_open_admissions=False,
            pct_degrees_computer_science=0.15,
            pct_degrees_engineering=0.2,
            pct_degrees_biological=0.12,
            pct_degrees_business=0.25,
            pct_degrees_social_science=0.1,
            avg_annual_cost=25000,
            avg_net_price_0_30k=5000,
            avg_net_price_30k_48k=10000,
            avg_net_price_48k_75k=15000,
            avg_net_price_75k_110k=20000,
            avg_net_price_110k_plus=25000,
            pct_students_pell=0.35,
            median_debt_pell=12000,
            median_earnings_10yrs=60000,
            earnings_25th_pctl_10yrs=45000,
            earnings_75th_pctl_10yrs=85000,
            pct_white_students=0.5,
            pct_black_students=0.15,
            pct_hispanic_students=0.2,
            pct_asian_students=0.1,
            pct_men_students=0.48,
            pct_women_students=0.52,
            is_hbcu=False,
            is_hsi=True
        )
        
        # Create a test user and college student profile
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        
        # Create college student profiles associated with the university
        for i in range(5):
            user = User.objects.create_user(
                email=f"student{i}@example.com",
                password="testpassword",
                user_type=User.COLLEGE_STUDENT_TYPE
            )
            profile = user.college_student_profile
            profile.university = self.university.institution
            profile.save()
        
        # URL for university detail
        self.url = reverse('university-detail', kwargs={'unitid': self.university.unitid})
    
    def test_get_university_detail_success(self):
        """Test retrieving university details with a valid unitid"""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)
        
        # Check status code
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check basic fields
        self.assertEqual(response.data['unitid'], self.university.unitid)
        self.assertEqual(response.data['name'], self.university.institution)
        self.assertEqual(response.data['location'], f"{self.university.city}, {self.university.state_territory}")
        self.assertEqual(response.data['type'], f"{self.university.public_private} University")
        self.assertEqual(response.data['website'], self.university.institution_website)
        
        # Check stats section
        stats = response.data['stats']
        self.assertEqual(stats['undergrad_students'], f"{self.university.undergrad_count:,}")
        self.assertEqual(stats['student_faculty_ratio'], "10:1")
        self.assertEqual(stats['acceptance_rate'], "65%")
        self.assertEqual(stats['sat_reading_writing'], "550-650")
        self.assertEqual(stats['sat_math'], "560-670")
        self.assertEqual(stats['act_composite'], "25-30")
        self.assertEqual(stats['retention_rate'], "85%")
        self.assertEqual(stats['graduation_rate'], "78%")
        self.assertEqual(stats['open_admissions'], "No")
        
        # Check facts section
        facts = response.data['facts']
        self.assertIn("Public", facts)
        self.assertIn("Urban", facts)
        self.assertIn("Hispanic-serving Institution", facts)
        
        # Check programs section
        programs = response.data['programs']
        self.assertEqual(len(programs), 5)
        # The top 5 programs should be included
        self.assertIn("Business & Marketing", programs)
        self.assertIn("Engineering", programs)
        self.assertIn("Computer Science & Related Fields", programs)
        self.assertIn("Biology & Biomedical Sciences", programs)
        self.assertIn("Social Sciences", programs)
        
        # Check financials section
        financials = response.data['financials']
        self.assertEqual(financials['cost_after_aid'], "$25,000")
        
        # Check cost by income
        cost_by_income = financials['cost_by_income']
        self.assertEqual(len(cost_by_income), 5)
        self.assertEqual(cost_by_income[0]['income'], "$0-$30,000")
        self.assertEqual(cost_by_income[0]['cost'], "$5,000")
        
        # Check earnings
        self.assertEqual(financials['median_annual_earnings'], "$60,000")
        self.assertEqual(financials['earnings_range']['min'], "$45,000")
        self.assertEqual(financials['earnings_range']['median'], "$60,000")
        self.assertEqual(financials['earnings_range']['max'], "$85,000")
        
        # Check scorecard URL
        self.assertEqual(financials['scorecard_url'], f"https://collegescorecard.ed.gov/school/?{self.university.unitid}")
        
        # Check pell section
        pell = response.data['pell']
        self.assertEqual(pell['students_awarded'], "35%")
        self.assertEqual(pell['median_debt'], "$12,000")
        
        # Check demographics section
        demographics = response.data['demographics']
        
        # Check gender
        gender = demographics['gender']
        self.assertEqual(len(gender), 2)
        male_data = next(item for item in gender if item["label"] == "Male")
        female_data = next(item for item in gender if item["label"] == "Female")
        self.assertEqual(male_data['value'], 48)
        self.assertEqual(female_data['value'], 52)
        
        # Check ethnicity
        ethnicity = demographics['ethnicity']
        self.assertEqual(len(ethnicity), 4)
        
        # Check trailblazers count
        self.assertEqual(response.data['connect_with_trailblazers'], 5)
    
    def test_university_not_found(self):
        """Test retrieving university details with an invalid unitid"""
        invalid_url = reverse('university-detail', kwargs={'unitid': 'nonexistent'})
        self.client.force_authenticate(user=self.user)
        response = self.client.get(invalid_url)
        
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
    
    def test_university_detail_missing_fields(self):
        """Test that the endpoint handles universities with missing fields"""
        # Create a university with minimal fields
        minimal_university = University.objects.create(
            unitid="654321",
            institution="Minimal University",
            city="Minimal City",
            state_territory="Minimal State"
        )
        
        url = reverse('university-detail', kwargs={'unitid': minimal_university.unitid})
        self.client.force_authenticate(user=self.user)
        response = self.client.get(url)
        
        # Check status code
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that missing fields are handled gracefully
        self.assertEqual(response.data['unitid'], minimal_university.unitid)
        self.assertEqual(response.data['name'], minimal_university.institution)
        self.assertEqual(response.data['location'], f"{minimal_university.city}, {minimal_university.state_territory}")
        
        # Stats should exist but have null values for missing data
        self.assertIn('stats', response.data)
        self.assertIsNone(response.data['stats']['undergrad_students'])
        self.assertIsNone(response.data['stats']['acceptance_rate'])

class UniversitySearchViewTests(APITestCase):
    def setUp(self):
        # Clear existing universities to ensure only test data is present
        University.objects.all().delete()
        
        # Create test users
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.other_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        
        # Create test universities
        self.university1 = University.objects.create(
            unitid="12345",
            institution="Harvard University",
            city="Cambridge",
            state_territory="Massachusetts",
            zip_code="02138",
            public_private="Private",
            undergrad_count=6755
        )
        self.university2 = University.objects.create(
            unitid="67890",
            institution="Stanford University",
            city="Stanford",
            state_territory="California",
            zip_code="94305",
            public_private="Private",
            undergrad_count=7645
        )
        self.university3 = University.objects.create(
            unitid="24680",
            institution="University of California, Berkeley",
            city="Berkeley",
            state_territory="California",
            zip_code="94720",
            public_private="Public",
            undergrad_count=31780
        )
        
        # Create a shortlist item for the user
        self.shortlist_item = ShortlistItem.objects.create(
            user=self.user,
            university=self.university1
        )
        
        # Setup the client and URL
        self.client = APIClient()
        self.url = reverse('university-search')
    
    def test_search_unauthenticated(self):
        """Test that unauthenticated users cannot access the search endpoint."""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_search_without_query(self):
        """Test search without a query parameter returns all universities."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 3)
        
        # Check the response structure
        results = response.data['results']
        self.assertEqual(len(results), 3)
        
        # Check that pagination metadata is included
        self.assertIn('count', response.data)
        self.assertIn('next', response.data)
        self.assertIn('previous', response.data)
    
    def test_search_by_name(self):
        """Test searching by university name."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f"{self.url}?search=harvard")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        
        # Check that the right university is returned
        results = response.data['results']
        self.assertEqual(results[0]['unitid'], "12345")
        self.assertEqual(results[0]['name'], "Harvard University")
    
    def test_search_by_city(self):
        """Test searching by city."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f"{self.url}?search=berkeley")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        
        # Check that the right university is returned
        results = response.data['results']
        self.assertEqual(results[0]['unitid'], "24680")
        self.assertEqual(results[0]['name'], "University of California, Berkeley")
    
    def test_search_by_state(self):
        """Test searching by state."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f"{self.url}?search=california")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 2)
        
        # Check that both California universities are returned
        results = response.data['results']
        self.assertEqual(len(results), 2)
        unitids = [result['unitid'] for result in results]
        self.assertIn("67890", unitids)
        self.assertIn("24680", unitids)
    
    def test_search_partial_match(self):
        """Test that partial matches work."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f"{self.url}?search=stan")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        
        # Check that Stanford is returned
        results = response.data['results']
        self.assertEqual(results[0]['unitid'], "67890")
        self.assertEqual(results[0]['name'], "Stanford University")
    
    def test_search_case_insensitive(self):
        """Test that search is case-insensitive."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f"{self.url}?search=HARVARD")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        
        # Check that Harvard is returned despite uppercase search
        results = response.data['results']
        self.assertEqual(results[0]['unitid'], "12345")
        self.assertEqual(results[0]['name'], "Harvard University")
    
    def test_bookmarked_field(self):
        """Test that is_bookmarked field is correctly set."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that the bookmarked university shows is_bookmarked=True
        results = response.data['results']
        harvard = next(result for result in results if result['unitid'] == "12345")
        stanford = next(result for result in results if result['unitid'] == "67890")
        
        self.assertTrue(harvard['is_bookmarked'])
        self.assertFalse(stanford['is_bookmarked'])
    
    def test_bookmarked_field_other_user(self):
        """Test that is_bookmarked is specific to the current user."""
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that no universities are bookmarked for the other user
        results = response.data['results']
        harvard = next(result for result in results if result['unitid'] == "12345")
        
        self.assertFalse(harvard['is_bookmarked'])
    
    def test_response_structure(self):
        """Test that the response has the correct structure and fields."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check the structure of a result
        result = response.data['results'][0]
        self.assertIn('unitid', result)
        self.assertIn('name', result)
        self.assertIn('location', result)
        self.assertIn('type', result)
        self.assertIn('undergrad_count', result)
        self.assertIn('is_bookmarked', result)
        
        # Check that location is formatted correctly
        self.assertRegex(result['location'], r'^.+, .+$')
    
    def test_invalid_query_parameter(self):
        """Test that invalid query parameters return a 400 error."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(f"{self.url}?invalid=parameter")
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_pagination(self):
        """Test that pagination works correctly."""
        # Create enough universities to trigger pagination
        for i in range(10):
            University.objects.create(
                unitid=f"test{i}",
                institution=f"Test University {i}",
                city="Test City",
                state_territory="Test State",
                zip_code="12345",
                public_private="Private"
            )
        
        self.client.force_authenticate(user=self.user)
        # Set page_size to a small number to test pagination
        response = self.client.get(f"{self.url}?page_size=5")
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check that we have the right number of results per page
        self.assertEqual(len(response.data['results']), 5)
        
        # Check that we have next page link
        self.assertIsNotNone(response.data['next'])
        
        # Get the next page
        next_page_url = response.data['next'].split('http://testserver')[1]
        response = self.client.get(next_page_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsNotNone(response.data['previous'])