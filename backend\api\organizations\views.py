from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import viewsets, status
from rest_framework.permissions import IsAuthenticated, AllowAny
from api.organizations.models import Organization, OrganizationTag
from api.organizations.serializers import OrganizationSerializer
from api.organizations.permissions import IsAdminOrReadOnly
from api.organizations.throttles import OrganizationNameThrottle
from django.db.models import Q

class OrganizationViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing and editing organization instances.
    """
    queryset = Organization.objects.all().order_by("name")
    serializer_class = OrganizationSerializer
    permission_classes = [IsAuthenticated, IsAdminOrReadOnly]

    @action(
        detail=True,
        methods=["get"],
        permission_classes=[AllowAny],
        throttle_classes=[OrganizationNameThrottle],
        url_path='name',
        url_name='organization-name'
    )
    def get_name(self, request, pk=None):
        """
        Custom action to retrieve only the name of an organization.
        """
        try:
            organization = self.get_object()
            return Response({"name": organization.name})
        except Organization.DoesNotExist:
            return Response({"detail": "Organization not found."}, status=404)

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[AllowAny],
        url_path="tags/list",
        url_name="organization-tags-list"
    )
    def get_tags(self, request):
        """
        Retrieve a list of verified organization tags with their IDs and names.
        """
        # Filter for verified tags only
        tags = OrganizationTag.objects.filter(verified=True).distinct().order_by('name')

        # Serialize the tags as a list of dictionaries with id and name
        tags_data = [{"id": tag.id, "name": tag.name} for tag in tags]

        return Response({"tags": tags_data}, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        permission_classes=[IsAuthenticated],
        url_path="tags/create",  # Differentiated path for POST
        url_name="organization-tags-create"
    )
    def create_tag(self, request):
        name = request.data.get('label', '').strip()

        if not name:
            return Response({"error": "Tag name is required."}, status=status.HTTP_400_BAD_REQUEST)

        standardized_name = name.title()
        existing_tag = OrganizationTag.objects.filter(name__iexact=standardized_name).first()

        if existing_tag:
            if existing_tag.verified:
                return Response({"error": "This organization already exists in the list."}, status=status.HTTP_400_BAD_REQUEST)
            else:
                # If the tag exists but is not verified, return its ID
                return Response({"id": existing_tag.id, "message": "This tag already exists but is not verified."}, status=status.HTTP_200_OK)

        # Create a new tag if it doesn't exist
        tag = OrganizationTag.objects.create(name=standardized_name, verified=False)
        return Response({"id": tag.id}, status=status.HTTP_201_CREATED)

    @action(
        detail=False,
        methods=["get"],
        permission_classes=[AllowAny],
        url_path="tags/verify",  # URL path for the action
        url_name="verify-tag"
    )
    def is_tag_verified(self, request):
        """
        Checks if a tag is verified.
        - Tags in the Organization table are automatically verified.
        """
        tag_id = request.query_params.get("tag", "").strip()  # Get the tag from query parameters
        if not tag_id:
            return Response({"error": "Tag id is required."}, status=status.HTTP_400_BAD_REQUEST)

        # Check if the tag exists in the OrganizationTag table
        tag = OrganizationTag.objects.filter(id=tag_id).first()
        if tag and tag.verified:
            return Response({"id": tag.id, "name": tag.name}, status=status.HTTP_200_OK)
        else:
            return Response({"error": "Tag not found."}, status=status.HTTP_404_NOT_FOUND)

    @action(
    detail=False,
    methods=["get"],
    permission_classes=[IsAuthenticated],  # Ensure only authenticated users can access this
    url_path="tags/user",
    url_name="user-organization-tags"
)
    def get_user_tags(self, request):
        """
        Retrieve the organization tags associated with the authenticated user's profile.
        """
        user = request.user

        # Ensure the user has a profile with organization tags
        if not hasattr(user, 'college_student_profile'):
            return Response({"error": "User does not have a college student profile."}, status=status.HTTP_400_BAD_REQUEST)

        # Get the tags associated with the user's profile
        tags = user.college_student_profile.organization_tags.all()

        # Serialize the tags as a list of dictionaries with id and name
        tags_data = [{"id": tag.id, "name": tag.name} for tag in tags]

        return Response({"tags": tags_data}, status=status.HTTP_200_OK)

    @action(
        detail=False,
        methods=["post"],
        permission_classes=[IsAuthenticated],
        url_path="tags/batch-verify",
        url_name="batch-verify-tags"
    )
    def batch_verify_tags(self, request):
        tag_ids = request.data.get("tag_ids", [])
        if not isinstance(tag_ids, list):
            return Response({"error": "tag_ids must be a list of integers."}, status=status.HTTP_400_BAD_REQUEST)

        tags = OrganizationTag.objects.filter(id__in=tag_ids, verified=True)
        tags_data = [{"id": tag.id, "name": tag.name} for tag in tags]
        return Response({"tags": tags_data}, status=status.HTTP_200_OK)


