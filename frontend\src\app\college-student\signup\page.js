"use client"

import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'
import { <PERSON>ton } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { Eye, EyeOff } from 'lucide-react'
import { Header } from "@/components/ui/header"
import { useAuth } from "@/context/AuthProvider"
import { IntroBanner } from "@/components/ui/introBanner"
import ErrorMessage from '@/components/ui/errorMessage'
import { Loader2 } from 'lucide-react'

// Define the form schema with Zod
const formSchema = z.object({
  email: z.string().email("Invalid email address").refine(email => email.endsWith('.edu'), {
    message: "Email must be a .edu address",
  }),
  password: z.string().min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?])[A-Za-z\d!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]{8,}$/, 
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character")
})

// Main SignUpPage component
export default function SignUpPage() {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex flex-col md:flex-row" id="main-content">
        <IntroBanner />
        <SignUpForm />
      </main>
    </div>
  )
}

// SignUpForm component
function SignUpForm() {
  const router = useRouter()
  const [error, setError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const [loading, setLoading] = useState(false)
  const { user, loginWithToken } = useAuth()

  const form = useForm({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      password: ""
    }
  })

  const onSubmit = async (data) => {
    setLoading(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/signup/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      const result = await response.json()
      if (response.ok) {
        loginWithToken(result.auth_token) // Log in the user with the received token

        await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/email-verification/send/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${result.auth_token}`,
          },
          body: JSON.stringify({ email: data.email }),
        })
        router.push('/verify-email?email=' + encodeURIComponent(data.email))
      } else {
        if (result && result.email) {
          setError(result.email[0])
          setLoading(false)
          return
        }
        setError(result.error || 'An unexpected error occurred')
        setLoading(false)
        return
      }
    } catch (error) {
      setError(error.message || 'An unexpected error occurred')
      setLoading(false)
    }
  }

  return (
    <div className="p-8 md:w-1/2 lg:w-6/12 flex flex-col justify-center items-center">
      <div className="w-full max-w-md">

        <h1 className="text-3xl font-bold mb-8">Sign Up</h1>
        {error && <ErrorMessage message={error} />}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-md font-medium text-gray-700 mb-1">Email</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter your .edu email"
                      className="p-6 text-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-md font-medium text-gray-700 mb-1">Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input 
                        type={showPassword ? "text" : "password"} 
                        placeholder="Enter your password"
                        className="p-6 text-md"
                        {...field} 
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4 text-gray-500" /> : <Eye className="h-4 w-4 text-gray-500" />}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
              <Button
                type="submit"
                className="w-full p-6 hover:bg-green-500 text-md"
                disabled={loading}
              >
                {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Sign Up
              </Button>
          </form>
        </Form>
        <p className="mt-4 text-center text-sm text-gray-600">
          Already have an account?
          <Link href="/login" className="font-medium text-green-600 hover:text-green-500 ml-1">
            Log In
          </Link>
        </p>
      </div>
    </div>
  )
}
