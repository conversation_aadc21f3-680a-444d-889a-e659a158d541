import React from 'react';

const OutreachModal = ({ onClose, user }) => {
    const handleEmailClick = () => {
        const email = '<EMAIL>';
        const subject = '[Trailblazer] Interested in Community Partnership';
        const body = `Hello Trailblazer Team,\n\nMy name is ${user.first_name} from [INSERT ORGANIZATION]. I would like to complete my onboarding process to gain full access to the platform to begin scheduling near-peer college advising sessions with Trailblazers from my community.\n\nThank you.`;
        window.location.href = `mailto:${email}?subject=${encodeURIComponent(subject)}&body=${encodeURIComponent(body)}`;
    };

    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>
            <div className="relative bg-white p-6 rounded-md shadow-md z-10 w-1/2">
                <button className="absolute top-2 right-2 text-2xl" onClick={onClose}>
                    &times;
                </button>
                <h2 className="text-xl font-bold mb-2">Want to schedule sessions?</h2>
                <p className="text-base md:text-lg text-gray-600 pb-12">
                    Please reach out to our team using your organization email to complete your onboarding process and gain full access to the site. 
                    If you have already reached out, please disregard this message, and we will get back to you within 24 hours.
                </p>
                <button
                    onClick={handleEmailClick}
                    className="px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-500"
                >
                    Contact Us!
                </button>
            </div>
        </div>
    );
};

export default OutreachModal;