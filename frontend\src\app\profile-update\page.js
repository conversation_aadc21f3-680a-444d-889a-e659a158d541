"use client"

import * as React from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { HelpCircle, X, RefreshCw, ArrowRight, Check, ChevronsUpDown } from "lucide-react"
import { useRouter } from "next/navigation"

import { cn } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Checkbox } from "@/components/ui/checkbox"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Badge } from "@/components/ui/badge"
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { useAuth } from "@/context/AuthProvider"

// --- MOCK DATA ---
const graduationYears = ["2025", "2026", "2027", "2028", "2029", "2030"].map(year => ({ value: year, label: year }))

const majors = [
  { value: "accounting", label: "Accounting" },
  { value: "actuarial_sciences", label: "Actuarial Sciences" },
  { value: "advertising_public_relations", label: "Advertising & Public Relations" },
  { value: "aerospace_aeronautical_engineering", label: "Aerospace & Aeronautical Engineering" },
  { value: "african_african_american_studies", label: "African & African-American Studies" },
  { value: "agricultural_economics", label: "Agricultural Economics" },
  { value: "agriculture", label: "Agriculture" },
  { value: "agriculture_production_management", label: "Agriculture Production & Management" },
  { value: "animal_sciences", label: "Animal Sciences" },
  { value: "anthropology", label: "Anthropology" },
  { value: "applied_mathematics", label: "Applied Mathematics" },
  { value: "arabic_language_literature", label: "Arabic Language & Literature" },
  { value: "archaeology", label: "Archaeology" },
  { value: "architectural_engineering", label: "Architectural Engineering" },
  { value: "architecture", label: "Architecture" },
  { value: "art_art_history", label: "Art & Art History" },
  { value: "astronomy_astrophysics", label: "Astronomy & Astrophysics" },
  { value: "atmospheric_sciences_meteorology", label: "Atmospheric Sciences & Meteorology" },
  { value: "biochemistry", label: "Biochemistry" },
  { value: "biological_engineering", label: "Biological Engineering" },
  { value: "biology", label: "Biology" },
  { value: "biomedical_engineering", label: "Biomedical Engineering" },
  { value: "botany", label: "Botany" },
  { value: "business_economics", label: "Business Economics" },
  { value: "business_management_administration", label: "Business Management & Administration" },
  { value: "chemical_engineering", label: "Chemical Engineering" },
  { value: "chemistry", label: "Chemistry" },
  { value: "chinese_language_literature", label: "Chinese Language & Literature" },
  { value: "civil_engineering", label: "Civil Engineering" },
  { value: "clinical_psychology", label: "Clinical Psychology" },
  { value: "cognitive_science_biopsychology", label: "Cognitive Science & Biopsychology" },
  { value: "commercial_art_graphic_design", label: "Commercial Art & Graphic Design" },
  { value: "communication_technologies", label: "Communication Technologies" },
  { value: "communications", label: "Communications" },
  { value: "community_public_health", label: "Community & Public Health" },
  { value: "comparative_language_literature", label: "Comparative Language & Literature" },
  { value: "computer_information_systems", label: "Computer & Information Systems" },
  { value: "computer_engineering", label: "Computer Engineering" },
  { value: "computer_networking_telecommunications", label: "Computer Networking & Telecommunications" },
  { value: "computer_programming_data_processing", label: "Computer Programming & Data Processing" },
  { value: "computer_science", label: "Computer Science" },
  { value: "construction_services", label: "Construction Services" },
  { value: "cosmetology_services", label: "Cosmetology Services" },
  { value: "counseling_psychology", label: "Counseling Psychology" },
  { value: "criminal_justice", label: "Criminal Justice" },
  { value: "criminology", label: "Criminology" },
  { value: "culinary_arts", label: "Culinary Arts" },
  { value: "cybersecurity_network_security", label: "Cybersecurity & Network Security" },
  { value: "drama_theater_arts", label: "Drama & Theater Arts" },
  { value: "early_childhood_education", label: "Early Childhood Education" },
  { value: "east_asian_studies", label: "East Asian Studies" },
  { value: "ecology", label: "Ecology" },
  { value: "economics", label: "Economics" },
  { value: "education", label: "Education" },
  { value: "educational_administration_supervision", label: "Educational Administration & Supervision" },
  { value: "educational_psychology", label: "Educational Psychology" },
  { value: "electrical_engineering", label: "Electrical Engineering" },
  { value: "elementary_education", label: "Elementary Education" },
  { value: "engineering", label: "Engineering" },
  { value: "engineering_technologies", label: "Engineering Technologies" },
  { value: "english_language_literature", label: "English Language & Literature" },
  { value: "environmental_engineering", label: "Environmental Engineering" },
  { value: "environmental_sciences", label: "Environmental Sciences" },
  { value: "ethnic_civilization_studies", label: "Ethnic & Civilization Studies" },
  { value: "european_history", label: "European History" },
  { value: "film_video_photographic_arts", label: "Film, Video, & Photographic Arts" },
  { value: "finance", label: "Finance" },
  { value: "fine_arts", label: "Fine Arts" },
  { value: "folklore_mythology", label: "Folklore & Mythology" },
  { value: "forestry", label: "Forestry" },
  { value: "french_language_literature", label: "French Language & Literature" },
  { value: "general_medical_health_services", label: "General Medical & Health Services" },
  { value: "genetics", label: "Genetics" },
  { value: "geography", label: "Geography" },
  { value: "geological_geophysical_engineering", label: "Geological & Geophysical Engineering" },
  { value: "geology_earth_sciences", label: "Geology & Earth Sciences" },
  { value: "geosciences", label: "Geosciences" },
  { value: "german_language_literature", label: "German Language & Literature" },
  { value: "government_political_sciences", label: "Government & Political Sciences" },
  { value: "health_medical_administration", label: "Health & Medical Administration" },
  { value: "hispanic_latin_american_studies", label: "Hispanic & Latin American Studies" },
  { value: "history", label: "History" },
  { value: "hospitality_management", label: "Hospitality Management" },
  { value: "human_development_family_studies", label: "Human Development & Family Studies" },
  { value: "human_resources_personnel_management", label: "Human Resources & Personnel Management" },
  { value: "human_services_community_organization", label: "Human Services & Community Organization" },
  { value: "humanities", label: "Humanities" },
  { value: "industrial_manufacturing_engineering", label: "Industrial & Manufacturing Engineering" },
  { value: "industrial_organizational_psychology", label: "Industrial & Organizational Psychology" },
  { value: "intercultural_international_studies", label: "Intercultural & International Studies" },
  { value: "interior_design", label: "Interior Design" },
  { value: "international_business", label: "International Business" },
  { value: "international_relations", label: "International Relations" },
  { value: "italian_language_literature", label: "Italian Language & Literature" },
  { value: "japanese_language_literature", label: "Japanese Language & Literature" },
  { value: "journalism", label: "Journalism" },
  { value: "kinesiology_exercise_science", label: "Kinesiology & Exercise Science" },
  { value: "korean_language_literature", label: "Korean Language & Literature" },
  { value: "latin_language_literature", label: "Latin Language & Literature" },
  { value: "liberal_arts", label: "Liberal Arts" },
  { value: "linguistics", label: "Linguistics" },
  { value: "marketing_marketing_management", label: "Marketing & Marketing Management" },
  { value: "materials_engineering", label: "Materials Engineering" },
  { value: "materials_science", label: "Materials Science" },
  { value: "mathematics", label: "Mathematics" },
  { value: "mathematics_teacher_education", label: "Mathematics Teacher Education" },
  { value: "mechanical_engineering", label: "Mechanical Engineering" },
  { value: "medical_assisting_services", label: "Medical Assisting Services" },
  { value: "medical_technologies", label: "Medical Technologies" },
  { value: "metallurgical_engineering", label: "Metallurgical Engineering" },
  { value: "microbiology", label: "Microbiology" },
  { value: "military_technologies", label: "Military Technologies" },
  { value: "mining_mineral_engineering", label: "Mining & Mineral Engineering" },
  { value: "molecular_cellular_biology", label: "Molecular & Cellular Biology" },
  { value: "music", label: "Music" },
  { value: "native_american_languages_linguistics", label: "Native American Languages & Linguistics" },
  { value: "native_american_studies", label: "Native American Studies" },
  { value: "natural_resources_management", label: "Natural Resources Management" },
  { value: "naval_architecture_marine_engineering", label: "Naval Architecture & Marine Engineering" },
  { value: "near_middle_eastern_studies", label: "Near & Middle Eastern Studies" },
  { value: "neuroscience", label: "Neuroscience" },
  { value: "nuclear_engineering", label: "Nuclear Engineering" },
  { value: "nursing", label: "Nursing" },
  { value: "nutrition_food_sciences", label: "Nutrition & Food Sciences" },
  { value: "oceanography", label: "Oceanography" },
  { value: "operations_logistics_e_commerce", label: "Operations, Logistics, & E-Commerce" },
  { value: "petroleum_engineering", label: "Petroleum Engineering" },
  { value: "pharmacology", label: "Pharmacology" },
  { value: "pharmacy_pharmaceutical_sciences_administration", label: "Pharmacy, Pharmaceutical Sciences, & Administration" },
  { value: "philosophy", label: "Philosophy" },
  { value: "physical_health_education_teaching", label: "Physical & Health Education Teaching" },
  { value: "physical_sciences", label: "Physical Sciences" },
  { value: "physics", label: "Physics" },
  { value: "physiology", label: "Physiology" },
  { value: "plant_science_agronomy", label: "Plant Science & Agronomy" },
  { value: "portuguese_language_literature", label: "Portuguese Language & Literature" },
  { value: "pre_law_legal_studies", label: "Pre-Law & Legal Studies" },
  { value: "psychology", label: "Psychology" },
  { value: "public_administration", label: "Public Administration" },
  { value: "public_policy", label: "Public Policy" },
  { value: "russian_language_literature", label: "Russian Language & Literature" },
  { value: "school_student_counseling", label: "School Student Counseling" },
  { value: "science_computer_teacher_education", label: "Science & Computer Teacher Education" },
  { value: "secondary_teacher_education", label: "Secondary Teacher Education" },
  { value: "slavic_language_literature", label: "Slavic Language & Literature" },
  { value: "social_psychology", label: "Social Psychology" },
  { value: "social_science_history_teacher_education", label: "Social Science Or History Teacher Education" },
  { value: "social_sciences", label: "Social Sciences" },
  { value: "social_work_services", label: "Social Work & Services" },
  { value: "sociology", label: "Sociology" },
  { value: "south_asian_studies", label: "South Asian Studies" },
  { value: "spanish_language_literature", label: "Spanish Language & Literature" },
  { value: "special_needs_education", label: "Special Needs Education" },
  { value: "statistics", label: "Statistics" },
  { value: "studio_arts", label: "Studio Arts" },
  { value: "theology_religious_studies", label: "Theology & Religious Studies" },
  { value: "transportation_sciences_technologies", label: "Transportation Sciences & Technologies" },
  { value: "treatment_therapy_professions", label: "Treatment Therapy Professions" },
  { value: "united_states_history", label: "United States History" },
  { value: "urban_studies", label: "Urban Studies" },
  { value: "visual_performing_arts", label: "Visual & Performing Arts" },
  { value: "womens_gender_sexuality_studies", label: "Women's, Gender, & Sexuality Studies" },
  { value: "writing_composition_rhetoric", label: "Writing, Composition, & Rhetoric" },
  { value: "zoology", label: "Zoology" }
]

const collegeTypes = [
  { value: "public", label: "Public College" },
  { value: "private", label: "Private College" },
  { value: "liberal_arts", label: "Liberal Arts College" },
  { value: "hbcu", label: "Historically Black College and University (HBCU)" },
  { value: "hsi", label: "Hispanic-Serving Institution (HSI)" },
  { value: "aanapisi", label: "Asian American and Native American Pacific Islander-Serving Institution (AANAPISI)" },
  { value: "tcu", label: "Tribal College or University (TCU)" },
  { value: "ivy_league", label: "Ivy League" },
  { value: "single_gender", label: "Single-Gender" },
  { value: "religious_affiliation", label: "Religious Affiliation" },
  { value: "community_college", label: "Community College" },
  { value: "military_academy", label: "Military Academy" },
  { value: "technical_college", label: "Technical/Trade College" },
  { value: "no_preference", label: "No Preference" }
]

const states = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AS", label: "American Samoa" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "DC", label: "District of Columbia" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  { value: "GU", label: "Guam" },
  { value: "HI", label: "Hawaii" },
  { value: "ID", label: "Idaho" },
  { value: "IL", label: "Illinois" },
  { value: "IN", label: "Indiana" },
  { value: "IA", label: "Iowa" },
  { value: "KS", label: "Kansas" },
  { value: "KY", label: "Kentucky" },
  { value: "LA", label: "Louisiana" },
  { value: "ME", label: "Maine" },
  { value: "MD", label: "Maryland" },
  { value: "MA", label: "Massachusetts" },
  { value: "MI", label: "Michigan" },
  { value: "MN", label: "Minnesota" },
  { value: "MS", label: "Mississippi" },
  { value: "MO", label: "Missouri" },
  { value: "MT", label: "Montana" },
  { value: "NE", label: "Nebraska" },
  { value: "NV", label: "Nevada" },
  { value: "NH", label: "New Hampshire" },
  { value: "NJ", label: "New Jersey" },
  { value: "NM", label: "New Mexico" },
  { value: "NY", label: "New York" },
  { value: "NC", label: "North Carolina" },
  { value: "ND", label: "North Dakota" },
  { value: "MP", label: "Northern Mariana Islands" },
  { value: "OH", label: "Ohio" },
  { value: "OK", label: "Oklahoma" },
  { value: "OR", label: "Oregon" },
  { value: "PA", label: "Pennsylvania" },
  { value: "PR", label: "Puerto Rico" },
  { value: "RI", label: "Rhode Island" },
  { value: "SC", label: "South Carolina" },
  { value: "SD", label: "South Dakota" },
  { value: "TN", label: "Tennessee" },
  { value: "TX", label: "Texas" },
  { value: "UT", label: "Utah" },
  { value: "VT", label: "Vermont" },
  { value: "VA", label: "Virginia" },
  { value: "VI", label: "U.S. Virgin Islands" },
  { value: "WA", label: "Washington" },
  { value: "WV", label: "West Virginia" },
  { value: "WI", label: "Wisconsin" },
  { value: "WY", label: "Wyoming" },
]

const locationTypes = [
    { id: "city", label: "City" },
    { id: "suburban", label: "Suburban" },
    { id: "small_town", label: "Small Town" },
    { id: "rural", label: "Rural" },
    { id: "no_preference", label: "No Preference" },
]

const zipCodeRegex = /^\d{5}(-\d{4})?$/

// --- ENHANCED FORM SCHEMA WITH PROPER VALIDATION ---
const profileUpdateSchema = z.object({
  // Academic Information with proper validation ranges
  unweighted_gpa: z.string().min(1, "Unweighted GPA is required").regex(/^(4(\.0{1,2})?|[0-3](\.\d{1,2})?)$/, "Invalid GPA format"),
  weighted_gpa: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseFloat(val)
      return !isNaN(num) && num >= 0 && num <= 5.0
    }, "Weighted GPA must be between 0.0 and 5.0"),
  
  sat_math_score: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseInt(val)
      return !isNaN(num) && num >= 200 && num <= 800
    }, "SAT Math score must be between 200 and 800"),
  
  sat_reading_score: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseInt(val)
      return !isNaN(num) && num >= 200 && num <= 800
    }, "SAT Reading/Writing score must be between 200 and 800"),
  
  act_score: z.string()
    .optional()
    .refine((val) => {
      if (!val || val === "") return true
      const num = parseInt(val)
      return !isNaN(num) && num >= 1 && num <= 36
    }, "ACT score must be between 1 and 36"),
  
  // Required fields
  high_school_name: z.string().min(1, "High school name is required"),
  graduation_year: z.string().min(1, "Graduation year is required"),
  
  // Multi-select fields
  intended_majors: z.array(z.string()).optional(),
  extracurriculars: z.string().optional(),
  interests: z.string().optional(),
  college_type_preferences: z.array(z.string()).optional(),
  location_type_preferences: z.array(z.string()).optional(),
  
  // Geographic preferences
  geographic_preference_type: z.enum(["states", "zip", "none"]).default("states"),
  preferred_states: z.array(z.string()).optional(),
  preferred_zip: z.string().optional(),
  preferred_radius: z.string().optional(),
  
  // Demographics
  country: z.string().default("usa"),
  current_zip_code: z.string().optional(),
  gender: z.string().optional(),
  ethnicity: z.string().optional(),
  household_income: z.string().optional(),
  financial_aid_need: z.string().optional(),
}).superRefine((data, ctx) => {
  if (data.geographic_preference_type === "zip") {
    if (!data.preferred_zip || data.preferred_zip.trim() === "") {
      ctx.addIssue({
        path: ["preferred_zip"],
        code: z.ZodIssueCode.custom,
        message: "Preferred ZIP code is required."
      })
    } else if (!zipCodeRegex.test(data.preferred_zip)) {
      ctx.addIssue({
        path: ["preferred_zip"],
        code: z.ZodIssueCode.custom,
        message: "ZIP code must be in 5-digit (12345) or 5+4 (12345-6789) format."
      })
    }
  }

  if (data.country === "usa") {
    if (!data.current_zip_code || data.current_zip_code.trim() === "") {
      ctx.addIssue({
        path: ["current_zip_code"],
        code: z.ZodIssueCode.custom,
        message: "ZIP code is required for USA addresses.",
      });
    } else if (!zipCodeRegex.test(data.current_zip_code)) {
      ctx.addIssue({
        path: ["current_zip_code"],
        code: z.ZodIssueCode.custom,
        message: "ZIP code must be in 5-digit (12345) or 5+4 (12345-6789) format.",
      });
    }
  }
})

// --- REUSABLE & CUSTOM COMPONENTS ---

const PageHeader = () => (
  <header className="mb-8">
    <div className="flex items-center justify-between mb-6">
      <div className="flex items-center">
        <img alt="Trailblazer Logo" className="h-6" src="/logo.svg" />
      </div>
      <div className="text-sm text-gray-500 hidden sm:inline">Keep your profile current for the best recommendations</div>
    </div>
    <div className="mb-4">
      <h2 className="text-xl font-semibold mb-2 text-gray-800">Update Your Profile</h2>
      <p className="text-gray-600">Modify your information below to refine your college recommendations.</p>
    </div>
  </header>
)

const FormSectionCard = ({ title, children }) => (
  <Card className="bg-white p-6 rounded-lg shadow-sm mb-6">
    <CardHeader className="p-0 mb-4">
      <CardTitle className="text-lg font-semibold text-gray-800">{title}</CardTitle>
    </CardHeader>
    <CardContent className="p-0">
      {children}
    </CardContent>
  </Card>
)

// Enhanced MultiSelectCombobox with better error handling
const MultiSelectCombobox = ({ field, options, placeholder }) => {
  const [open, setOpen] = React.useState(false)
  const selectedValues = field.value || []

  const handleSelect = (currentValue) => {
    const newSelectedValues = selectedValues.includes(currentValue)
      ? selectedValues.filter((v) => v !== currentValue)
      : [...selectedValues, currentValue]
    field.onChange(newSelectedValues)
  }

  const handleRemove = (valueToRemove) => {
    field.onChange(selectedValues.filter((v) => v !== valueToRemove))
  }

  const clearAll = () => {
    field.onChange([])
  }

  return (
    <Popover open={open} onOpenChange={setOpen}>
      <PopoverTrigger asChild>
        <Button
          variant="outline"
          role="combobox"
          aria-expanded={open}
          className="w-full justify-between h-auto min-h-[2.5rem] py-2 px-3"
        >
          <div className="flex flex-wrap gap-1 items-center flex-1">
            {selectedValues.length > 0
              ? selectedValues.map(val => {
                  const option = options.find(opt => opt.value === val)
                  return (
                    <Badge key={val} variant="secondary" className="mr-1">
                      {option ? option.label : val}
                      <span
                        role="button"
                        tabIndex={0}
                        aria-label={`Remove ${option?.label || val}`}
                        onClick={(e) => {
                          e.stopPropagation()
                          handleRemove(val)
                        }}
                        onKeyDown={(e) => {
                          if (e.key === 'Enter' || e.key === ' ') {
                            e.preventDefault()
                            handleRemove(val)
                          }
                        }}
                        className="ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2 cursor-pointer"
                      >
                        <X className="h-3 w-3 text-muted-foreground hover:text-foreground" />
                      </span>
                    </Badge>
                  )
                })
              : <span className="text-muted-foreground">{placeholder}</span>
            }
          </div>
          <div className="flex items-center gap-1">
            {selectedValues.length > 0 && (
              <span
                role="button"
                tabIndex={0}
                aria-label="Clear all selections"
                onClick={(e) => {
                  e.stopPropagation()
                  clearAll()
                }}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    e.preventDefault()
                    clearAll()
                  }
                }}
                className="text-muted-foreground hover:text-foreground cursor-pointer"
              >
                <X className="h-4 w-4" />
              </span>
            )}
            <ChevronsUpDown className="h-4 w-4 shrink-0 opacity-50" />
          </div>
        </Button>
      </PopoverTrigger>
      <PopoverContent className="w-[--radix-popover-trigger-width] p-0">
        <Command>
          <CommandInput placeholder={`Search ${placeholder.toLowerCase()}...`} />
          <CommandList>
            <CommandEmpty>No results found.</CommandEmpty>
            <CommandGroup>
              {options.map((option) => (
                <CommandItem
                  key={option.value}
                  value={option.label}
                  onSelect={() => handleSelect(option.value)}
                >
                  <Check
                    className={cn(
                      "mr-2 h-4 w-4",
                      selectedValues.includes(option.value) ? "opacity-100" : "opacity-0"
                    )}
                  />
                  {option.label}
                </CommandItem>
              ))}
            </CommandGroup>
          </CommandList>
        </Command>
      </PopoverContent>
    </Popover>
  )
}

const FormFooter = ({ isLoading, onCancel, remainingRequests }) => (
  <footer className="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 shadow-[0_-2px_10px_rgba(0,0,0,0.05)] z-10">
    <div className="flex justify-between items-center max-w-3xl mx-auto">
      <Button variant="secondary" type="button" onClick={onCancel} disabled={isLoading}>
        <X className="w-4 h-4 mr-2" />
        Cancel
      </Button>
      <Button type="submit" disabled={isLoading || remainingRequests === 0} className="bg-[#35CE5C] hover:bg-[#31b954] text-white">
        {isLoading ? (
          <>
            <RefreshCw className="w-4 h-4 mr-2 animate-spin" />
            <span className="hidden sm:inline">Updating Profile...</span>
            <span className="inline sm:hidden">Updating...</span>
          </>
        ) : (
          <>
            <span className="hidden sm:inline">
              Update Profile & Get New Recommendations (Remaining: {remainingRequests})
            </span>
            <span className="inline sm:hidden">
              Update
            </span>
            <ArrowRight className="w-4 h-4 ml-2" />
          </>
        )}
      </Button>
    </div>
  </footer>
)

const LoadingSkeleton = () => {
  return (
    <div role="status" aria-label="Loading form" className="space-y-6 animate-pulse">
      {[...Array(6)].map((_, i) => (
        <div key={i} className="space-y-2">
          {/* Label */}
          <Skeleton className="h-4 w-32 rounded bg-gray-300 dark:bg-gray-700" />
          {/* Input */}
          <Skeleton className="h-10 w-full rounded bg-gray-200 dark:bg-gray-800" />
        </div>
      ))}

      {/* Buttons */}
      <div className="flex justify-end space-x-4 pt-4">
        <Skeleton className="h-10 w-24 rounded bg-gray-300 dark:bg-gray-700" />
        <Skeleton className="h-10 w-48 rounded bg-gray-400 dark:bg-gray-600" />
      </div>
    </div>
  )
}

// --- MAIN FORM COMPONENT ---

function transformProfilePayload(values) {
  return {
    unweighted_gpa: values.unweighted_gpa ? parseFloat(values.unweighted_gpa) : null,
    weighted_gpa: values.weighted_gpa ? parseFloat(values.weighted_gpa) : null,
    sat_math_score: values.sat_math_score ? parseInt(values.sat_math_score, 10) : null,
    sat_reading_score: values.sat_reading_score ? parseInt(values.sat_reading_score, 10) : null,
    act_score: values.act_score ? parseInt(values.act_score, 10) : null,
    high_school_name: values.high_school_name || "",
    graduation_year: values.graduation_year ? parseInt(values.graduation_year, 10) : null,
    intended_majors: values.intended_majors || [],
    extracurriculars: values.extracurriculars || "",
    interests: values.interests || "",
    college_type_preferences: values.college_type_preferences || [],
    location_type_preferences: values.location_type_preferences || [],
    geographic_preference_type: values.geographic_preference_type || "states",
    preferred_states: values.preferred_states || [],
    preferred_zip: values.preferred_zip || "",
    preferred_radius: values.preferred_radius || "",
    country: values.country || "usa",
    current_zip_code: values.current_zip_code || "",
    gender: values.gender || "",
    ethnicity: values.ethnicity || "",
    household_income: values.household_income || "",
    financial_aid_need: values.financial_aid_need || "",
  }
}

function ProfileUpdateForm() {
  const [isLoading, setIsLoading] = React.useState(false)
  const [isFetching, setIsFetching] = React.useState(true)
  const [remainingRequests, setRemainingRequests] = React.useState(null)
  const router = useRouter()
  const { toast } = useToast()

  // Initialize form with pre-filled default values (simulating existing user data)
  const form = useForm({
    resolver: zodResolver(profileUpdateSchema),
    defaultValues: {
      unweighted_gpa: "",
      weighted_gpa: "",
      sat_math_score: "",
      sat_reading_score: "",
      act_score: "",
      high_school_name: "",
      graduation_year: "",
      intended_majors: [],
      extracurriculars: "",
      interests: "",
      college_type_preferences: [],
      location_type_preferences: [],
      geographic_preference_type: "states",
      preferred_states: [],
      preferred_zip: "",
      preferred_radius: "",
      country: "usa",
      current_zip_code: "",
      gender: "",
      ethnicity: "",
      household_income: "",
      financial_aid_need: "",
    },
  })
  
  // Watch for changes in location preference type to show/hide relevant fields
  const locationPreferenceType = form.watch("geographic_preference_type")

  // Enhanced form submission with proper error handling and user feedback
  async function onSubmit(values) {
    if (remainingRequests === 0) {
      toast({ title: "Too Many Requests", description: "You have exceeded your rate limit for generating recommendations.", variant: "destructive" })
      return
    }
    try {
      setIsLoading(true)
      toast({ title: "Updating Profile", description: "Please wait while we update your profile..." })
      const token = getToken() || localStorage.getItem('trailblazers.auth.token')
      const transformedValues = transformProfilePayload(values)
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/profile/update/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Authorization: "Token " + token
        },
        body: JSON.stringify(transformedValues)
      })
      if (!response.ok) {
        if (response.status === 429) {
          toast({ title: "Rate Limit Exceeded", description: "You have reached the maximum number of allowed requests.", variant: "destructive" })
        } else {
          const errorData = await response.json()
          toast({ title: "Update Failed", description: errorData.detail || "There was an error updating your profile.", variant: "destructive" })
        }
        return
      }
      const result = await response.json()
      toast({ title: "Profile Updated Successfully!", description: "Your profile has been updated. Recommendations are being generated..." })
      router.push("/recommendations")
    } catch (error) {
      toast({ title: "Update Failed", description: "There was an error updating your profile. Please try again.", variant: "destructive" })
    } finally {
      setIsLoading(false)
    }
  }

  // Handle cancel functionality - return to previous page without saving
  function handleCancel() {
    // Show confirmation if form has been modified
    const isDirty = form.formState.isDirty
    
    if (isDirty) {
      const confirmCancel = window.confirm("Are you sure you want to cancel? Your changes will be lost.")
      if (!confirmCancel) return
    }
    
    // Navigate back to previous page or dashboard
    router.push("/recommendations")
  }

  const locationTypes = form.watch("location_type_preferences") || []

  // Handle location type changes with mutual exclusivity for "No Preference"
  const handleLocationTypeChange = (value, checked) => {
    const currentValues = locationTypes
    
    if (value === "no_preference") {
      // If selecting "No Preference", clear all other selections
      if (checked) {
        form.setValue("location_type_preferences", ["no_preference"])
      } else {
        form.setValue("location_type_preferences", [])
      }
    } else {
      // If selecting any other option, remove "No Preference" if it exists
      let newValues = checked
        ? [...currentValues.filter(v => v !== "no_preference"), value]
        : currentValues.filter(v => v !== value)
      
      form.setValue("location_type_preferences", newValues)
    }
  }

  const handleCollegeTypeChange = (newValues) => {
    const lastSelected = newValues[newValues.length - 1]
    const hasNoPreference = newValues.includes("no_preference")
  
    if (lastSelected === "no_preference") {
      // User just selected "no_preference" → reset to only that
      form.setValue("college_type_preferences", ["no_preference"])
    } else if (hasNoPreference) {
      // User selected something else while "no_preference" was selected → remove it
      form.setValue("college_type_preferences", newValues.filter((v) => v !== "no_preference"))
    } else {
      // Normal behavior
      form.setValue("college_type_preferences", newValues)
    }
  }

  const { getToken } = useAuth()
  React.useEffect(() => {
    async function fetchProfile() {
      const token = getToken() || localStorage.getItem('trailblazers.auth.token')
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/highschoolstudentprofile/me/`, {
          headers: { Authorization: "Token " + token }
        })
        if (!response.ok) {
          throw new Error("Failed to fetch profile data")
        }
        const data = await response.json()
        // Map fetched data to form fields ensuring fallback values for null fields
        form.reset({
          unweighted_gpa: data.unweighted_gpa != null ? String(data.unweighted_gpa).trim() : "",
          weighted_gpa: data.weighted_gpa != null ? String(data.weighted_gpa).trim() : "",
          sat_math_score: data.sat_math_score != null ? String(data.sat_math_score).trim() : "",
          sat_reading_score: data.sat_reading_score != null ? String(data.sat_reading_score).trim() : "",
          act_score: data.act_score != null ? String(data.act_score).trim() : "",
          high_school_name: data.high_school_name != null ? data.high_school_name : "",
          graduation_year: data.graduation_year != null ? String(data.graduation_year).trim() : "",
          intended_majors: data.intended_majors ?? [],
          extracurriculars: data.extracurriculars ?? "",
          interests: data.interests ?? "",
          college_type_preferences: data.college_type_preferences ?? [],
          location_type_preferences: data.location_type_preferences ?? [],
          geographic_preference_type: data.geographic_preference_type ?? "states",
          preferred_states: data.preferred_states ?? [],
          preferred_zip: data.preferred_zip ?? "",
          preferred_radius: data.preferred_radius ?? "",
          country: data.country ?? "usa",
          current_zip_code: data.current_zip_code ?? "",
          gender: data.gender ?? "",
          ethnicity: data.ethnicity ?? "",
          household_income: data.household_income ?? "",
          financial_aid_need: data.financial_aid_need ?? "",
        })
      } catch (error) {
        toast({ title: "Profile Load Error", description: "Unable to fetch your profile data.", variant: "destructive" })
      } finally {
        setIsFetching(false)
      }
    }
    fetchProfile()
  }, [])

  React.useEffect(() => {
    async function fetchRateLimit() {
      const token = getToken() || localStorage.getItem('trailblazers.auth.token')
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/highschoolstudentprofile/recommendation-throttle-status/`, {
          headers: { Authorization: "Token " + token }
        })
        if (!response.ok) {
          throw new Error("Failed to fetch rate limit status")
        }
        const data = await response.json()
        setRemainingRequests(data.remaining_requests)
      } catch (error) {
        toast({ title: "Rate Limit Error", description: "Unable to fetch rate limit status.", variant: "destructive" })
      }
    }
    fetchRateLimit()
  }, [])

  if (isFetching) {
    return (
      <div className="max-w-3xl mx-auto p-6">
        <LoadingSkeleton />
      </div>
    )
  }

  return (
    <TooltipProvider>
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6 mb-24">
          
          {/* Academic Information Section */}
          <FormSectionCard title="Your Academic Journey">
          <Alert className="bg-primary/5 border-l-4 border-t-0 border-r-0 border-b-0 border-primary p-3 mb-4 rounded-md text-sm">
            <AlertDescription>
              Note: If both GPAs are provided, we convert your Weighted GPA to a 4.0 scale and use the higher of the Unweighted and converted Weighted GPA to match how colleges report GPA. We always use the most favorable GPA for your college recommendations.
            </AlertDescription>
          </Alert>
            
            {/* GPA Fields with enhanced validation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField control={form.control} name="unweighted_gpa" render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Unweighted GPA (4.0 Scale)
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="w-4 h-4 ml-1 text-gray-400 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your GPA on a 4.0 scale, without extra points for AP/IB or honors classes.</p>
                      </TooltipContent>
                    </Tooltip>
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g. 3.7" 
                      {...field} 
                      className={cn(form.formState.errors.unweighted_gpa && "border-red-500")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              
              <FormField control={form.control} name="weighted_gpa" render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center">
                    Weighted GPA (e.g., 5.0 Scale)
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <HelpCircle className="w-4 h-4 ml-1 text-gray-400 cursor-pointer" />
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Enter your GPA if your school uses a weighted scale (e.g., 5.0), including points for AP/IB/honors.</p>
                      </TooltipContent>
                    </Tooltip>
                  </FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g. 4.2" 
                      {...field} 
                      className={cn(form.formState.errors.weighted_gpa && "border-red-500")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            {/* Test Scores with enhanced validation */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormField control={form.control} name="sat_math_score" render={({ field }) => (
                <FormItem>
                  <FormLabel>SAT Math Score (optional)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="200-800" 
                      {...field} 
                      className={cn(form.formState.errors.sat_math_score && "border-red-500")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
              
              <FormField control={form.control} name="sat_reading_score" render={({ field }) => (
                <FormItem>
                  <FormLabel>SAT Reading/Writing Score (optional)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="200-800" 
                      {...field} 
                      className={cn(form.formState.errors.sat_reading_score && "border-red-500")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            <div className="mt-4">
              <FormField control={form.control} name="act_score" render={({ field }) => (
                <FormItem>
                  <FormLabel>ACT Composite Score (optional)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="1-36" 
                      {...field} 
                      className={cn(form.formState.errors.act_score && "border-red-500")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            {/* Required School Information */}
            <div className="mt-4">
              <FormField control={form.control} name="high_school_name" render={({ field }) => (
                <FormItem>
                  <FormLabel>Current High School Name *</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter your high school name" 
                      {...field} 
                      className={cn(form.formState.errors.high_school_name && "border-red-500")}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            <div className="mt-4">
              <FormField control={form.control} name="graduation_year" render={({ field }) => (
                <FormItem>
                  <FormLabel>High School Graduation Year *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className={cn(form.formState.errors.graduation_year && "border-red-500")}>
                        <SelectValue placeholder="Select year" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      {graduationYears.map(opt => (
                        <SelectItem key={opt.value} value={opt.value}>
                          {opt.label}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
          </FormSectionCard>
          
          {/* College Interests & Preferences Section */}
          <FormSectionCard title="College Interests & Preferences">
            {/* Multi-select for Intended Majors */}
            <FormField control={form.control} name="intended_majors" render={({ field }) => (
              <FormItem>
                <FormLabel>Intended Major(s)</FormLabel>
                <MultiSelectCombobox 
                  field={field} 
                  placeholder="Search and select majors..." 
                  options={majors}
                />
                <FormDescription>You can search and select multiple majors. Type to search.</FormDescription>
                <FormMessage />
              </FormItem>
            )} />

            <div className="mt-4">
              <FormField control={form.control} name="extracurriculars" render={({ field }) => (
                <FormItem>
                  <FormLabel>Extracurricular Activities</FormLabel>
                  <FormControl>
                    <Textarea 
                      rows={3} 
                      placeholder="List your key extracurricular activities..." 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            <div className="mt-4">
              <FormField control={form.control} name="interests" render={({ field }) => (
                <FormItem>
                  <FormLabel>Interests & Hobbies</FormLabel>
                  <FormControl>
                    <Textarea 
                      rows={3} 
                      placeholder="e.g., coding, hiking, painting..." 
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            {/* Multi-select for College Types */}
            <div className="mt-4">
              <FormField control={form.control} name="college_type_preferences" render={({ field }) => (
                <FormItem>
                  <FormLabel>College Type Preferences</FormLabel>
                  <MultiSelectCombobox 
                    field={{
                      ...field,
                      onChange: (newValues) => {
                        handleCollegeTypeChange(newValues)
                      }
                    }}
                    placeholder="Search and select college types..." 
                    options={collegeTypes}
                  />
                  <FormDescription>You can select multiple options.</FormDescription>
                  <FormMessage />
                </FormItem>
              )} />
            </div>

            {/* Location Type Preferences with Checkboxes */}
            <div className="mt-4">
              <FormField control={form.control} name="location_type_preferences" render={({ field }) => (
                <FormItem>
                  <FormLabel>Location Type Preferences</FormLabel>
                  <div className="grid grid-cols-2 sm:grid-cols-3 gap-x-4 gap-y-2 mt-2">
                    {[
                      { id: "city", label: "City" },
                      { id: "suburban", label: "Suburban" },
                      { id: "small_town", label: "Small Town" },
                      { id: "rural", label: "Rural" },
                      { id: "no_preference", label: "No Preference" }
                    ].map((item) => (
                      <FormField key={item.id} control={form.control} name="location_type_preferences" render={({ field }) => (
                        <FormItem key={item.id} className="flex flex-row items-start space-x-3 space-y-0">
                          <FormControl>
                            <Checkbox
                              checked={locationTypes.includes(item.id)}
                              onCheckedChange={(checked) => handleLocationTypeChange(item.id, checked)}
                            />
                          </FormControl>
                          <FormLabel className="font-normal">{item.label}</FormLabel>
                        </FormItem>
                      )} />
                    ))}
                  </div>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            {/* Geographic Preferences Toggle */}
            <div className="mt-6 pt-4 border-t border-gray-200">
              <FormField control={form.control} name="geographic_preference_type" render={({ field }) => (
                <FormItem>
                  <FormLabel className="font-medium text-gray-700">Geographic Preferences (Optional)</FormLabel>
                  <FormDescription className="text-sm">How would you like to specify your geographic preference?</FormDescription>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex flex-wrap gap-x-6 gap-y-2 mt-2">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="states" /></FormControl>
                        <FormLabel className="font-normal">By U.S. States/Territories</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="zip" /></FormControl>
                        <FormLabel className="font-normal">By ZIP Code & Radius</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="none" /></FormControl>
                        <FormLabel className="font-normal">No Geographic Preference</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />

              {/* Conditional rendering based on geographic preference selection */}
              {locationPreferenceType === "states" && (
                <div className="mt-4">
                  <FormField control={form.control} name="preferred_states" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred U.S. States/Territories</FormLabel>
                      <MultiSelectCombobox 
                        field={field} 
                        placeholder="Search and select states..." 
                        options={states}
                      />
                      <FormDescription>You can search and select multiple states and territories.</FormDescription>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
              )}
              
              {locationPreferenceType === "zip" && (
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
                  <FormField control={form.control} name="preferred_zip" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred ZIP Code</FormLabel>
                      <FormControl>
                        <Input 
                          placeholder="Enter ZIP code" 
                          {...field} 
                          className={cn(form.formState.errors.preferred_zip && "border-red-500")}
                        />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )} />
                  
                  <FormField control={form.control} name="preferred_radius" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Preferred Mileage Radius</FormLabel>
                      <Select onValueChange={field.onChange} value={field.value}>
                        <FormControl>
                          <SelectTrigger className={cn(form.formState.errors.preferred_radius && "border-red-500")}>
                            <SelectValue placeholder="Select radius" />
                          </SelectTrigger>
                        </FormControl>
                        <SelectContent>
                          <SelectItem value="0-20">0-20 miles (short drive)</SelectItem>
                          <SelectItem value="21-50">21-50 miles (medium drive)</SelectItem>
                          <SelectItem value="51-200">51-200 miles (long drive)</SelectItem>
                          <SelectItem value="201-500">201-500 miles (short flight)</SelectItem>
                          <SelectItem value="501+">501+ miles (long flight)</SelectItem>
                        </SelectContent>
                      </Select>
                      <FormMessage />
                    </FormItem>
                  )} />
                </div>
              )}
            </div>
          </FormSectionCard>

          {/* Demographics Section */}
          <FormSectionCard title="Tell Us About Yourself">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <FormField control={form.control} name="country" render={({ field }) => (
                <FormItem>
                  <FormLabel>Country *</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger className={cn(form.formState.errors.country && "border-red-500")}>
                        <SelectValue />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="usa">USA</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />
              
              <FormField control={form.control} name="current_zip_code" render={({ field }) => (
                <FormItem>
                  <FormLabel>ZIP Code (if in USA)</FormLabel>
                  <FormControl>
                    <Input placeholder="e.g. 90210" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mt-4">
              <FormField control={form.control} name="gender" render={({ field }) => (
                <FormItem>
                  <FormLabel>Gender (optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select gender" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="female">Female</SelectItem>
                      <SelectItem value="male">Male</SelectItem>
                      <SelectItem value="non_binary">Non-binary</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />
              
              <FormField control={form.control} name="ethnicity" render={({ field }) => (
                <FormItem>
                  <FormLabel>Ethnicity (optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select ethnicity" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="black">Black or African American</SelectItem>
                      <SelectItem value="east_asian">East Asian</SelectItem>
                      <SelectItem value="hispanic">Hispanic or Latine</SelectItem>
                      <SelectItem value="middle_eastern">Middle Eastern or North African</SelectItem>
                      <SelectItem value="native_american">Native American or Alaska Native</SelectItem>
                      <SelectItem value="native_hawaiian">Native Hawaiian or Other Pacific Islander</SelectItem>
                      <SelectItem value="south_asian">South Asian</SelectItem>
                      <SelectItem value="white">White or Caucasian</SelectItem>
                      <SelectItem value="multiracial">Multiracial</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            <div className="mt-4">
              <FormField control={form.control} name="household_income" render={({ field }) => (
                <FormItem>
                  <FormLabel>Household Income (optional)</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Select income range" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="under_30k">Under $30,000</SelectItem>
                      <SelectItem value="30k_48k">$30,001 - $48,000</SelectItem>
                      <SelectItem value="48k_75k">$48,001 - $75,000</SelectItem>
                      <SelectItem value="75k_100k">$75,001 - $100,000</SelectItem>
                      <SelectItem value="100k_150k">$110,001 - $150,000</SelectItem>
                      <SelectItem value="150k_plus">$150,001+</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
            
            <div className="mt-4">
              <FormField control={form.control} name="financial_aid_need" render={({ field }) => (
                <FormItem>
                  <FormLabel>Financial Aid Need *</FormLabel>
                  <FormControl>
                    <RadioGroup onValueChange={field.onChange} value={field.value} className="flex flex-wrap gap-x-4 gap-y-2 mt-2">
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="high" /></FormControl>
                        <FormLabel className="font-normal">High Need</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="medium" /></FormControl>
                        <FormLabel className="font-normal">Medium Need</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="low" /></FormControl>
                        <FormLabel className="font-normal">Low Need</FormLabel>
                      </FormItem>
                      <FormItem className="flex items-center space-x-2">
                        <FormControl><RadioGroupItem value="no_need" /></FormControl>
                        <FormLabel className="font-normal">No Need</FormLabel>
                      </FormItem>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )} />
            </div>
          </FormSectionCard>

          {/* Fixed Footer with Cancel and Submit buttons */}
          <FormFooter isLoading={isLoading} onCancel={handleCancel} remainingRequests={remainingRequests} />
        </form>
      </Form>
    </TooltipProvider>
  )
}

// --- MAIN PAGE COMPONENT ---

export default function UpdateProfilePage() {
  return (
    <div className="bg-[#F9F9F9] min-h-screen">
      <div className="max-w-3xl mx-auto p-4 md:p-6">
        <PageHeader />
        <main>
          <ProfileUpdateForm />
        </main>
      </div>
    </div>
  )
}