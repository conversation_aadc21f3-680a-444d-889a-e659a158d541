# Trailblazer

A web application that connects college students (Trailblazers) with high school administrators and students for mentorship.

*Project built with Turbo*

## About the Code

There are three main components in this project:

- **Backend API server**: Django REST framework
- **Frontend**: Next.js
- **Database**: PostgreSQL

## Environment Variables

To run the application, you need to configure both backend and frontend environment variables. These variables are essential for defining the behavior and connectivity of the application in different environments (development and production).

### Backend Environment Variables

The backend environment variables are organized under the `.env` folder. Inside this folder, you will find a `.local` directory, which contains the following two files:

1. **django**: This file contains environment variables specific to the Django application.
2. **posgress**: This file contains environment variables required for configuring the PostgreSQL database.

Each file includes variables for development purposes and should be updated as needed for your environment. As you prepare for production, you will also have to add certain variables to the `Django` environment file (see How to Run Production Mode)

### Frontend Environment Variables

The frontend environment variables are stored in a file named `env.local` located in the root of the project directory. This file defines variables necessary for the Next.js frontend application.

### Notes on Security

- **Do not push production environment files containing sensitive information (e.g., `env.production`) to Git.**  
- Always ensure these files are included in `.gitignore` to prevent accidental exposure of credentials.
- Use secure methods to share environment variables, such as encrypted channels or secret management tools.

## How to Run Development Mode

### Build and Run

```bash
docker-compose build
docker-compose up
```

## How to Run Production Mode

### Create Backend Environment variables

Create a new folder named `.production` under the `.envs` directory. Replicate the structure of the `.local` folder as a `.production` folder.

Use this template for setting up the variables:

**backend/.envs/.production/django**
```env
# General
# ------------------------------------------------------------------------------
USE_DOCKER=yes
IPYTHONDIR=/app/.ipython

# Production Settings
# ------------------------------------------------------------------------------
DJANGO_SECRET_KEY=secret-key                    # The secret key for Django, used for cryptographic signing
DJANGO_SETTINGS_MODULE=api.settings.production
DJANGO_AWS_ACCESS_KEY_ID=access-key-id          # AWS access key for using S3 storage
DJANGO_AWS_SECRET_ACCESS_KEY=secret-access-key  # AWS secret access key for using S3 storage
DJANGO_AWS_STORAGE_BUCKET_NAME=bucket-name      # The name of the S3 bucket for file storage
DJANGO_ADMIN_URL=admin/                         # The relative path for accessing the Django admin panel
DJANGO_ALLOWED_HOSTS=allowed-hosts              # A list of allowed hosts that can access the application, separated by commas

# Frontend
# ------------------------------------------------------------------------------
FRONTEND_BASE_URL=frontend-url # The base URL for the production frontend application

# Google Calendar
# ------------------------------------------------------------------------------
GOOGLE_CLIENT_ID=client-id         # The client ID for Google API authentication
GOOGLE_CLIENT_SECRET=client-secret # The client secret for Google API authentication
GOOGLE_REFRESH_TOKEN=refresh-token # The refresh token to maintain authentication with Google services
GOOGLE_CALENDAR_ID=calendar-id     # The calendar ID used to identify a specific Google Calendar

# Email
# ------------------------------------------------------------------------------
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.sendgrid.net
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=your-sendgrid-api-key # The API key for the SMTP server
EMAIL_PORT=587
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=<EMAIL>
```

**backend/.envs/.production/django**
```env
# PostgreSQL
# ------------------------------------------------------------------------------
POSTGRES_HOST=postgres
POSTGRES_PORT=5432
POSTGRES_DB=db-name           # The name of the PostgreSQL database
POSTGRES_USER=db-user         # The username for accessing the PostgreSQL database
POSTGRES_PASSWORD=db-password # The password for accessing the PostgreSQL database
```

### Create Frontend Environment variables

Create a new file called `env.production` for the frontend and update the variables accordingly.

Use this template for setting up the variables:

**frontend/.env.production**
```env
# Api base url
# ------------------------------------------------------------------------------
NEXT_PUBLIC_API_BASE_URL=api-url    # The base URL for the production backend application
NEXT_PUBLIC_MEDIA_BASE_URL=api-url  # The base URL for the production backend application
```

### Build and Run

Build and run using the production docker compose file

```bash
docker-compose -f docker-compose.production.yml build
docker-compose -f docker-compose.production.yml up
```
