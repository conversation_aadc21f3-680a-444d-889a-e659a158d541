from rest_framework.test import APITestCase
from django.urls import reverse
from api.users.models import User, OnboardingProgress, CollegeStudentProfile, HighSchoolStudentProfile, CounselorAdministratorProfile
from api.organizations.models import Organization
from rest_framework import status
from django.core.files.uploadedfile import SimpleUploadedFile
from io import BytesIO
from PIL import Image
import json


def generate_image_file(size=10):
    # Create an in-memory image file
    img = Image.new('RGB', (10, 10), color = (255, 0, 0))
    img_io = BytesIO()
    img.save(img_io, format='JPEG')
    img_io.seek(0)
    return SimpleUploadedFile("avatar.jpg", img_io.read(), content_type="image/jpeg")


class CollegeStudentOnboardingAPITestCase(APITestCase):
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.client.force_authenticate(user=self.user)
        self.url = reverse('onboarding-college-student')
    
    def test_get_onboarding_progress(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['steps_completed'], [])

    def test_complete_every_onboarding_step_in_sequence(self):
        payload = {
            "step": "high_school",
            "high_school_name": "Test High School",
            "high_school_zip_code": "12345",
            "high_school_city": "Test City",
            "high_school_state": "Test State"
        }
        response = self.client.put(self.url, payload)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['high_school'])

        payload = {
            "step": "university",
            "university": "Test University",
            "university_tags": ["Test Tag"],
        }
        response = self.client.put(self.url, payload)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['high_school', 'university'])

        payload = {
            "step": "interests",
            "college_major": "Test Major",
            "interests": ["Test Interest"],
        }
        response = self.client.put(self.url, payload)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['high_school', 'university', 'interests'])

        payload = {
            "step": "basic_info",
            "first_name": "Test",
            "last_name": "User",
            "bio": "This is a test bio.",
            "avatar": generate_image_file()
        }
        response = self.client.put(self.url, payload, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['high_school', 'university', 'interests', 'basic_info'])

        payload = {
            "step": "availability",
            "availability": {
                "time_zone": "UTC",
                "monday_available": True,
                "tuesday_available": False,
                "wednesday_available": True,
                "thursday_available": False,
                "friday_available": True,
                "saturday_available": False,
                "sunday_available": False,
                "monday_time_ranges": [{"start_time": "09:00", "end_time": "17:00"}],
                "wednesday_time_ranges": [{"start_time": "09:00", "end_time": "17:00"}],
                "friday_time_ranges": [{"start_time": "09:00", "end_time": "17:00"}]
            }
        }
        response = self.client.put(self.url, payload, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['high_school', 'university', 'interests', 'basic_info', 'availability'])
        

class HighSchoolStudentOnboardingAPITestCase(APITestCase):
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.client.force_authenticate(user=self.user)
        self.url = reverse('onboarding-high-school-student')
    
    def test_get_onboarding_progress(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['steps_completed'], [])

    def test_complete_every_onboarding_step_in_sequence(self):
        payload = {
            "step": "basic_info",
            "first_name": "Test",
            "last_name": "User",
            "avatar": generate_image_file(),
            "grade_level": "12",
        }
        response = self.client.put(self.url, payload, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['basic_info'])


class CounselorAdministratorOnboardingAPITestCase(APITestCase):
    
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='securepassword123',
            user_type=User.COUNSELOR_ADMINISTRATOR_TYPE
        )
        self.organization = Organization.objects.create(
            name='Test Organization',
            zip_code='12345',
            city='Test City',
            state='Test State'
        )
        self.client.force_authenticate(user=self.user)
        self.url = reverse('onboarding-counselor-administrator')
    
    def test_get_onboarding_progress(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['steps_completed'], [])

    def test_complete_every_onboarding_step_in_sequence(self):
        payload = {
            "step": "organization",
            "organization": self.organization.id,
        }
        response = self.client.put(self.url, payload)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['organization'])

        payload = {
            "step": "basic_info",
            "first_name": "Test",
            "last_name": "User",
            "avatar": generate_image_file(),
            "position": "Test Position",
        }
        response = self.client.put(self.url, payload, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user.refresh_from_db()
        self.assertEqual(self.user.onboarding_progress.steps_completed, ['organization', 'basic_info'])