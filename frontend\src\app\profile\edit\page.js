"use client"

import { useState } from "react"
import { useToast } from "@/hooks/use-toast"
import { Button } from "@/components/ui/button"
import { Header } from "@/components/ui/header"
import { Sidebar } from "@/components/ui/sidebar"
import { UserType } from "@/lib/utils"
import { CollegeStudentProfileForm } from './collegeStudentProfileForm'
import { HighSchoolStudentProfileForm } from './highschoolStudentProfileForm'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { CounselorProfileForm } from './counselorProfileForm'
import { avatarToFile } from '@/components/ui/avatarSelection'
import { Loader2 } from 'lucide-react'
import { MainLayout } from "@/components/ui/mainLayout"

// ProfileLayout component
const ProfileLayout = ({ user, children, loading }) => (
    <MainLayout user={user} displaySidebarMenu={true}>
        <div className="flex items-center justify-between mb-8">
            <h1 className="text-2xl md:text-3xl font-bold text-left">Your Profile</h1>
            <Button
                type="submit"
                form="profile-form"
                variant="outline"
                className="border border-gray-800 text-gray-800"
                disabled={loading}
            >
                {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Save Changes
            </Button>
        </div>
        {children}
    </MainLayout>
)

// Main ProfileEditForm component
const ProfileEditForm = () => {
    const { toast } = useToast()
    const { user, loadingUser: loading, error: error } = useAuth()
    const { getToken, updateAuthenticatedUser } = useAuth()
    const [updateLoading, setUpdateLoading] = useState(false)
    const [pendingCustomTags, setPendingCustomTags] = useState([])

    const onSubmit = async (data) => {
        setUpdateLoading(true);
        try {
            const authToken = getToken();

            // Create custom tags in the backend and collect their IDs
            const customTagIds = [];
            for (const tag of pendingCustomTags) {

                data.organizationTags = data.organizationTags.filter((orgTag) => orgTag.value !== tag.value);

                const customTagResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/tags/create/`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${authToken}`,
                    },
                    body: JSON.stringify({ label: tag.label }),
                });

                if (customTagResponse.ok) {
                    const createdTag = await customTagResponse.json();
                    customTagIds.push(createdTag.id); // Add the new tag's ID to the list
                } else {
                    console.error(`Failed to create custom tag: ${tag}`);
                }
            }

            // Ensure only allowed fields are sent to the backend
            const avatarFile = await avatarToFile(data.avatar);
            const formData = new FormData();
            formData.append('avatar', avatarFile);

            if (user.user_type === UserType.COLLEGE_STUDENT_TYPE) {
                formData.append('bio', data.bio);
                formData.append('university', data.university);
                formData.append('graduation_year', data.graduationYear);
                formData.append('college_major', data.major);
                formData.append('university_tags', JSON.stringify(data.collegeTags));
                formData.append('interests', JSON.stringify(data.interests));

                // Combine existing organization tags with custom tag IDs
                const organizationTagIds = [
                    ...data.organizationTags.map((tag) => tag.value), // Extract IDs from existing tags
                    ...customTagIds, // Add IDs of newly created custom tags
                ];

                // Append organization_tags as individual values
                organizationTagIds.forEach((id) => formData.append('organization_tags', id));
            }


            if (user.user_type === UserType.HIGH_SCHOOL_STUDENT_TYPE) {
                formData.append('grade_level', parseInt(data.schoolYear));
            }

            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/me/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Token ${authToken}`,
                },
                body: formData,
            });

            if (response.ok) {
                await updateAuthenticatedUser();
                toast({
                    title: "Profile updated",
                    description: "Your profile has been successfully updated.",
                });
            } else {
                const errorData = await response.json();
                console.error(errorData);
                Object.keys(errorData).forEach((key) => {
                    toast({
                        variant: 'destructive',
                        description: errorData[key][0],
                        status: "error",
                        duration: 5000,
                        isClosable: true,
                    });
                });
            }
        } catch (error) {
            console.error('An unexpected error occurred:', error);
            toast({
                variant: 'destructive',
                description: "An unexpected error occurred.",
                status: "error",
                duration: 5000,
                isClosable: true,
            });
        }
        setUpdateLoading(false);
    };

    return (
        <ProfileLayout user={user} loading={updateLoading}>
            { loading && <p>Loading...</p> }
            {(user && user.user_type === UserType.HIGH_SCHOOL_STUDENT_TYPE) && (
                <HighSchoolStudentProfileForm userData={user} onSubmit={onSubmit} />
            )}
            {(user && user.user_type === UserType.COLLEGE_STUDENT_TYPE) && (
                <CollegeStudentProfileForm userData={user} onSubmit={onSubmit} setPendingCustomTags={setPendingCustomTags} />
            )}
            {(user && user.user_type === UserType.COUNSELOR_ADMINISTRATOR_TYPE) && (
                <CounselorProfileForm userData={user} onSubmit={onSubmit} />
            )}
        </ProfileLayout>
    )
}

export default withAuth(ProfileEditForm)
