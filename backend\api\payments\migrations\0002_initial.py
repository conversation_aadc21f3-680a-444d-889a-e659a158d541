# Generated by Django 4.2.13 on 2025-01-30 07:04

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TrailblazerHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_hours', models.FloatField(default=0)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('trailblazer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='trailblazer_hours', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Trailblazer Hours',
                'verbose_name_plural': 'Trailblazer Hours',
                'managed': True,
            },
        ),
        migrations.CreateModel(
            name='PaymentLog',
            fields=[
                ('id', models.BigAutoField(primary_key=True, serialize=False)),
                ('paypal_email', models.EmailField(max_length=254)),
                ('hours', models.FloatField()),
                ('amount', models.DecimalField(decimal_places=2, max_digits=10, null=True)),
                ('payment_status', models.CharField(max_length=1000)),
                ('payment_date', models.DateTimeField()),
                ('error_message', models.TextField(blank=True, null=True)),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='payment_logs', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'payment_log',
                'ordering': ['-payment_date'],
                'managed': True,
            },
        ),
    ]
