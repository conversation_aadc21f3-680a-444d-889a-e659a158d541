from rest_framework import serializers
from api.universities.models import University, ShortlistItem
from api.users.models import CollegeStudentProfile

class UniversitySerializer(serializers.ModelSerializer):
    """
    Serializer for University model used in recommendations.
    """
    
    class Meta:
        model = University
        fields = []
        read_only_fields = ['unitid']

    def get_acceptance_rate(self, obj):
        if obj.acceptance_rate:
            return f"{int(obj.acceptance_rate * 100)}%"
        return None
        
    def get_bookmarked(self, obj):
        """
        Check if the university is bookmarked (shortlisted) by the current user.
        """
        request = self.context.get('request')
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            return ShortlistItem.objects.filter(user=request.user, university=obj).exists()
        return False

    def get_match(self, obj):
        """
        Returns 'reach', 'target', or 'safety' if this university is in the respective list
        passed via context['recommendations'] (mapping of categories to university IDs).
        """
        recommendations = self.context.get('recommendations', {})
        if obj.unitid in recommendations.get('reach_universities', []):
            return 'reach'
        elif obj.unitid in recommendations.get('target_universities', []):
            return 'target'
        elif obj.unitid in recommendations.get('safety_universities', []):
            return 'safety'
        return None

    def to_representation(self, instance):
        representation = {
            'unitid': instance.unitid,
            'college_name': instance.institution,
            'location': f"{instance.city}, {instance.state_territory}",
            'type': instance.public_private,
            'acceptance_rate': self.get_acceptance_rate(instance),
            'annual_cost': instance.avg_annual_cost,
            'bookmarked': self.get_bookmarked(instance),  # Add bookmarked attribute
            'match': self.get_match(instance),
        }
        return representation


class UniversityStatsSerializer(serializers.Serializer):
    """Serializer for university statistics section"""
    undergrad_students = serializers.SerializerMethodField()
    student_faculty_ratio = serializers.SerializerMethodField()
    acceptance_rate = serializers.SerializerMethodField()
    sat_reading_writing = serializers.SerializerMethodField()
    sat_math = serializers.SerializerMethodField()
    act_composite = serializers.SerializerMethodField()
    retention_rate = serializers.SerializerMethodField()
    graduation_rate = serializers.SerializerMethodField()
    open_admissions = serializers.SerializerMethodField()
    
    def get_undergrad_students(self, obj):
        if obj.undergrad_count:
            return f"{obj.undergrad_count:,}"
        return None
        
    def get_student_faculty_ratio(self, obj):
        if obj.student_faculty_ratio is not None:
            return f"{int(obj.student_faculty_ratio*100)}:1"
        return None
        
    def get_acceptance_rate(self, obj):
        if obj.acceptance_rate:
            return f"{int(obj.acceptance_rate * 100)}%"
        return None
        
    def get_sat_reading_writing(self, obj):
        if obj.sat_reading_25th and obj.sat_reading_75th:
            return f"{obj.sat_reading_25th}-{obj.sat_reading_75th}"
        return None
        
    def get_sat_math(self, obj):
        if obj.sat_math_25th and obj.sat_math_75th:
            return f"{obj.sat_math_25th}-{obj.sat_math_75th}"
        return None
        
    def get_act_composite(self, obj):
        if obj.act_25th and obj.act_75th:
            return f"{obj.act_25th}-{obj.act_75th}"
        return None
        
    def get_retention_rate(self, obj):
        if obj.retention_rate:
            return f"{obj.retention_rate * 100:.0f}%"
        return None
        
    def get_graduation_rate(self, obj):
        if obj.graduation_rate:
            return f"{obj.graduation_rate * 100:.0f}%"
        return None
        
    def get_open_admissions(self, obj):
        return "Yes" if obj.has_open_admissions else "No"


class UniversityFactsSerializer(serializers.Serializer):
    """Serializer for university facts section"""
    facts = serializers.SerializerMethodField()
    
    def get_facts(self, obj):
        facts = []
        
        # Add public/private status
        if obj.public_private:
            facts.append(obj.public_private)
        
        # Add locale
        if obj.locale:
            facts.append(obj.locale)
        
        # Add institution type indicators
        if obj.is_hbcu:
            facts.append("Historically Black College & University")
        if obj.is_tribal:
            facts.append("Tribal College & University or Native-serving Institution")
        if obj.is_hsi:
            facts.append("Hispanic-serving Institution")
        if obj.is_aanapisi:
            facts.append("Asian American & Native American Pacific Islander-serving Institution")
        if obj.is_single_gender:
            facts.append("Single-gender college")
        if obj.religious_affiliation == "Yes":
            facts.append("Religious Affiliation")
        
        return facts


class UniversityProgramsSerializer(serializers.Serializer):
    """Serializer for university programs section"""
    programs = serializers.SerializerMethodField()
    
    def get_programs(self, obj):
        # Map degree percentage fields to their display names
        degree_fields = {
            'pct_degrees_agriculture': 'Agriculture & Related Sciences',
            'pct_degrees_resources': 'Environmental & Conservation Studies',
            'pct_degrees_architecture': 'Architecture & Design',
            'pct_degrees_ethnic_cultural_gender': 'Cultural, Ethnic, & Gender Studies',
            'pct_degrees_communication': 'Communication & Journalism',
            'pct_degrees_communications_tech': 'Media & Tech Support',
            'pct_degrees_computer_science': 'Computer Science & Related Fields',
            'pct_degrees_culinary': 'Culinary & Personal Services',
            'pct_degrees_education': 'Education',
            'pct_degrees_engineering': 'Engineering',
            'pct_degrees_engineering_tech': 'Engineering Technologies',
            'pct_degrees_language': 'Foreign Languages & Linguistics',
            'pct_degrees_family_consumer_science': 'Human & Family Sciences',
            'pct_degrees_legal': 'Law & Legal Studies',
            'pct_degrees_english': 'English Language & Literature',
            'pct_degrees_humanities': 'Liberal Arts & Humanities',
            'pct_degrees_library': 'Library Science',
            'pct_degrees_biological': 'Biology & Biomedical Sciences',
            'pct_degrees_mathematics': 'Math & Statistics',
            'pct_degrees_military': 'Military & Defense Technologies',
            'pct_degrees_multidiscipline': 'Interdisciplinary Studies',
            'pct_degrees_parks_recreation_fitness': 'Sports, Recreation, & Fitness Studies',
            'pct_degrees_philosophy': 'Philosophy & Religion',
            'pct_degrees_theology': 'Theology & Ministry',
            'pct_degrees_physical_science': 'Physical Sciences',
            'pct_degrees_science_tech': 'Science & Lab Technologies',
            'pct_degrees_psychology': 'Psychology',
            'pct_degrees_security_law_enforcement': 'Criminal Justice & Public Safety',
            'pct_degrees_public_admin': 'Public Administration & Social Work',
            'pct_degrees_social_science': 'Social Sciences',
            'pct_degrees_construction': 'Construction & Related Fields',
            'pct_degrees_mechanic_repair_tech': 'Mechanics & Repair Technologies',
            'pct_degrees_precision_production': 'Manufacturing & Related Fields',
            'pct_degrees_transportation': 'Transportation & Logistics',
            'pct_degrees_visual_performing': 'Arts, Music, & Performance Studies',
            'pct_degrees_health': 'Health & Medical Studies',
            'pct_degrees_business': 'Business & Marketing',
            'pct_degrees_history': 'History'
        }
        
        # Create a list of (field_name, percentage, display_name) tuples for fields with values
        programs_data = []
        for field, display_name in degree_fields.items():
            # Convert the value to float; default to 0 if missing
            try:
                value = float(getattr(obj, field, 0) or 0)
            except (TypeError, ValueError):
                value = 0
            if value > 0:
                programs_data.append((field, value, display_name))
        
        # Sort by percentage in descending order and take top 5
        programs_data.sort(key=lambda x: x[1], reverse=True)
        top_programs = [program[2] for program in programs_data[:5]]
        
        return top_programs


class UniversityFinancialsSerializer(serializers.Serializer):
    """Serializer for university financials section"""
    cost_after_aid = serializers.SerializerMethodField()
    cost_by_income = serializers.SerializerMethodField()
    median_annual_earnings = serializers.SerializerMethodField()
    earnings_range = serializers.SerializerMethodField()
    scorecard_url = serializers.SerializerMethodField()
    
    def get_cost_after_aid(self, obj):
        if obj.avg_annual_cost:
            return f"${obj.avg_annual_cost:,.0f}"
        return None
        
    def get_cost_by_income(self, obj):
        income_brackets = [
            {"income": "$0-$30,000", "cost": obj.avg_net_price_0_30k},
            {"income": "$30,001-$48,000", "cost": obj.avg_net_price_30k_48k},
            {"income": "$48,001-$75,000", "cost": obj.avg_net_price_48k_75k},
            {"income": "$75,001-$110,000", "cost": obj.avg_net_price_75k_110k},
            {"income": "$110,001+", "cost": obj.avg_net_price_110k_plus}
        ]
        
        # Format costs with dollar signs and commas
        for bracket in income_brackets:
            if bracket["cost"]:
                bracket["cost"] = f"${bracket['cost']:,.0f}"
            else:
                bracket["cost"] = None
        
        return income_brackets
        
    def get_median_annual_earnings(self, obj):
        if obj.median_earnings_10yrs:
            return f"${obj.median_earnings_10yrs:,.0f}"
        return None
        
    def get_earnings_range(self, obj):
        if obj.earnings_25th_pctl_10yrs and obj.median_earnings_10yrs and obj.earnings_75th_pctl_10yrs:
            return {
                "min": f"${obj.earnings_25th_pctl_10yrs:,.0f}",
                "median": f"${obj.median_earnings_10yrs:,.0f}",
                "max": f"${obj.earnings_75th_pctl_10yrs:,.0f}"
            }
        return None
        
    def get_scorecard_url(self, obj):
        return f"https://collegescorecard.ed.gov/school/?{obj.unitid}"


class UniversityPellSerializer(serializers.Serializer):
    """Serializer for university Pell grant information"""
    students_awarded = serializers.SerializerMethodField()
    median_debt = serializers.SerializerMethodField()
    
    def get_students_awarded(self, obj):
        if obj.pct_students_pell:
            return f"{obj.pct_students_pell * 100:.0f}%"
        return None
        
    def get_median_debt(self, obj):
        if obj.median_debt_pell:
            return f"${obj.median_debt_pell:,.0f}"
        return None


class UniversityDemographicsSerializer(serializers.Serializer):
    """Serializer for university demographics section"""
    gender = serializers.SerializerMethodField()
    ethnicity = serializers.SerializerMethodField()
    
    def get_gender(self, obj):
        gender_data = []
        if obj.pct_men_students is not None:
            gender_data.append({"label": "Male", "value": round(obj.pct_men_students * 100)})
        if obj.pct_women_students is not None:
            gender_data.append({"label": "Female", "value": round(obj.pct_women_students * 100)})
        return gender_data
        
    def get_ethnicity(self, obj):
        ethnicity_data = []
        ethnicity_mapping = [
            ("pct_white_students", "White or Caucasian"),
            ("pct_asian_students", "Asian"),
            ("pct_hispanic_students", "Hispanic or Latine"),
            ("pct_multiracial_students", "Two or More Races"),
            ("pct_black_students", "Black or African American"),
            ("pct_aian_students", "American Indian or Alaska Native"),
            ("pct_nhpi_students", "Native Hawaiian or Pacific Islander"),
            ("pct_unknown_race_students", "Race Unknown")
        ]
        
        for field, label in ethnicity_mapping:
            value = getattr(obj, field, None)
            if value is not None and value > 0:
                ethnicity_data.append({"label": label, "value": round(value * 100)})
        
        # Sort by percentage in descending order
        ethnicity_data.sort(key=lambda x: x["value"], reverse=True)
        
        return ethnicity_data


class UniversityDetailSerializer(serializers.ModelSerializer):
    """
    Serializer for detailed university information
    """
    name = serializers.CharField(source='institution')
    location = serializers.SerializerMethodField()
    type = serializers.SerializerMethodField()
    website = serializers.CharField(source='institution_website')
    stats = UniversityStatsSerializer(source='*')
    facts = serializers.SerializerMethodField()
    programs = serializers.SerializerMethodField()
    financials = UniversityFinancialsSerializer(source='*')
    pell = UniversityPellSerializer(source='*')
    demographics = UniversityDemographicsSerializer(source='*')
    connect_with_trailblazers = serializers.SerializerMethodField()
    is_bookmarked = serializers.SerializerMethodField()
    
    class Meta:
        model = University
        fields = [
            'unitid', 'name', 'location', 'type', 'website',
            'stats', 'facts', 'programs', 'financials', 'pell',
            'demographics', 'connect_with_trailblazers',
            "is_bookmarked"
        ]
    
    def get_is_bookmarked(self, obj):
        """
        Check if the university is bookmarked (shortlisted) by the current user.
        """
        request = self.context.get('request')
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            return ShortlistItem.objects.filter(user=request.user, university=obj).exists()
        return False

    def get_location(self, obj):
        return f"{obj.city}, {obj.state_territory}"
    
    def get_type(self, obj):
        if obj.public_private:
            return f"{obj.public_private} University"
        return None
    
    def get_facts(self, obj):
        # Use the nested serializer's get_facts method
        return UniversityFactsSerializer().get_facts(obj)
    
    def get_programs(self, obj):
        return UniversityProgramsSerializer().get_programs(obj)
    
    def get_connect_with_trailblazers(self, obj):
        # Count students at this university
        return CollegeStudentProfile.objects.filter(university__icontains=obj.institution).count()


class ShortlistItemSerializer(serializers.ModelSerializer):
    university_id = serializers.CharField(write_only=True)
    university_name = serializers.SerializerMethodField()
    
    class Meta:
        model = ShortlistItem
        fields = ['id', 'university_id', 'university_name', 'created_at']
        read_only_fields = ['id', 'university_name', 'created_at']
    
    def get_university_name(self, obj):
        return obj.university.institution

    def validate_university_id(self, value):
        try:
            University.objects.get(unitid=value)
        except University.DoesNotExist:
            raise serializers.ValidationError("Invalid university ID")
        return value
    
    def create(self, validated_data):
        university_id = validated_data.pop('university_id')
        user = self.context['request'].user
        university = University.objects.get(unitid=university_id)
        
        # Check if the university is already in the shortlist
        if ShortlistItem.objects.filter(user=user, university=university).exists():
            raise serializers.ValidationError({"university_id": "University already in shortlist"})
        
        validated_data.pop('user', None)
        return ShortlistItem.objects.create(
            user=user,
            university=university,
            **validated_data
        )

class UniversitySearchSerializer(serializers.ModelSerializer):
    """
    Serializer for university search results.
    """
    name = serializers.CharField(source='institution')
    location = serializers.SerializerMethodField()
    type = serializers.CharField(source='public_private')
    is_bookmarked = serializers.SerializerMethodField()
    
    class Meta:
        model = University
        fields = [
            'unitid', 
            'name', 
            'location', 
            'type', 
            'undergrad_count', 
            'is_bookmarked'
        ]
    
    def get_location(self, obj):
        """
        Format location as 'city, state'
        """
        return f"{obj.city}, {obj.state_territory}"
    
    def get_is_bookmarked(self, obj):
        """
        Check if the university is bookmarked by the current user
        """
        request = self.context.get('request')
        if request and hasattr(request, 'user') and request.user.is_authenticated:
            return ShortlistItem.objects.filter(user=request.user, university=obj).exists()
        return False