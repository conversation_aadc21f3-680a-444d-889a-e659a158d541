from rest_framework import permissions
from django.conf import settings
from django.contrib.auth import get_user_model


class IsUserOrReadOnly(permissions.BasePermission):
    """
    Object-level permission to only allow owners of an object to edit it.
    """
    def has_object_permission(self, request, view, obj):

        if request.method in permissions.SAFE_METHODS:
            return True

        return obj == request.user


class IsOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to access it.
    """
    def has_object_permission(self, request, view, obj):
        return obj.user == request.user  # Ensure the user owns the Availability


class IsAuthenticatedAndOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of the profile to edit it.
    """
    def has_object_permission(self, request, view, obj):
        return obj == request.user


User = get_user_model()


class IsHighSchoolStudent(permissions.BasePermission):
    """
    Custom permission to only allow high school students to access the view.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        return request.user.user_type == User.HIGH_SCHOOL_STUDENT_TYPE