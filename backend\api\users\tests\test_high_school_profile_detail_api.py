from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from api.users.models import User
from api.users.tests.factories import UserFactory, HighSchoolStudentProfileFactory
from api.organizations.tests.factories import OrganizationFactory


class HighSchoolStudentProfileDetailTests(APITestCase):
    """
    Test cases for the high school student profile detail API (/me/ endpoint).
    """
    
    def setUp(self):
        self.organization = OrganizationFactory()
        
        # High school student user
        self.user = UserFactory(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
        self.profile = self.user.high_school_student_profile
        self.profile.organization = self.organization
        self.profile.unweighted_gpa = 3.8
        self.profile.weighted_gpa = 4.2
        self.profile.sat_math_score = 750
        self.profile.sat_reading_score = 720
        self.profile.act_score = 32
        self.profile.high_school_name = 'Lincoln High School'
        self.profile.graduation_year = 2025
        self.profile.intended_majors = ['Computer Science', 'Mathematics']
        self.profile.extracurriculars = "Debate and Chess Club"
        self.profile.interests = "Programming and reading"
        self.profile.college_type_preferences = ['Public', 'Private']
        self.profile.location_type_preferences = ['Urban', 'Suburban']
        self.profile.geographic_preference_type = 'states'
        self.profile.preferred_states = ['CA', 'NY', 'MA']
        self.profile.preferred_radius = '0-20'
        self.profile.gender = 'female'
        self.profile.ethnicity = 'black'
        self.profile.household_income = '48k_75k'
        self.profile.financial_aid_need = 'medium'
        self.profile.save()
        
        # Other users
        self.other_user = UserFactory(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
        self.college_student = UserFactory(user_type=User.COLLEGE_STUDENT_TYPE)

        # New /me/ endpoint URL
        self.url = reverse('high-school-student-profile-detail')
    
    def test_retrieve_own_profile_success(self):
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unweighted_gpa'], 3.8)
        self.assertEqual(response.data['weighted_gpa'], 4.2)
        self.assertEqual(response.data['sat_math_score'], 750)
        self.assertEqual(response.data['sat_reading_score'], 720)
        self.assertEqual(response.data['act_score'], 32)
        self.assertEqual(response.data['high_school_name'], 'Lincoln High School')
        self.assertEqual(response.data['graduation_year'], 2025)
        self.assertEqual(response.data['intended_majors'], ['Computer Science', 'Mathematics'])
        self.assertEqual(response.data['extracurriculars'], "Debate and Chess Club")
        self.assertEqual(response.data['interests'], "Programming and reading")
        self.assertEqual(response.data['college_type_preferences'], ['Public', 'Private'])
        self.assertEqual(response.data['location_type_preferences'], ['Urban', 'Suburban'])
        self.assertEqual(response.data['geographic_preference_type'], 'states')
        self.assertEqual(response.data['preferred_states'], ['CA', 'NY', 'MA'])
        self.assertEqual(response.data['preferred_radius'], '0-20')
        self.assertEqual(response.data['gender'], 'female')
        self.assertEqual(response.data['ethnicity'], 'black')
        self.assertEqual(response.data['household_income'], '48k_75k')
        self.assertEqual(response.data['financial_aid_need'], 'medium')

    def test_retrieve_profile_unauthorized(self):
        self.client.force_authenticate(user=None)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)

    def test_retrieve_profile_wrong_user_type(self):
        self.client.force_authenticate(user=self.college_student)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_profile_does_not_exist(self):
        # User without a profile
        user_without_profile = UserFactory(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
        user_without_profile.high_school_student_profile.delete()

        self.client.force_authenticate(user=user_without_profile)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
