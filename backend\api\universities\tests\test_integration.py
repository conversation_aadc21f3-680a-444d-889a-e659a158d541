"""
Integration tests for university services.
"""
from unittest import mock

from django.test import TestCase
from django.contrib.auth import get_user_model

from api.universities.services import generate_ideal_university_description

User = get_user_model()

class IdealUniversityIntegrationTests(TestCase):
    """Integration tests for the ideal university generation service."""
    
    def setUp(self):
        """Set up test data."""
        # Create a user with a high school student profile
        self.user = User.objects.create_user(
            email="<EMAIL>",
            password="testpassword",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.profile = self.user.high_school_student_profile
        
        # Set profile data
        self.profile.intended_majors = ["Computer Science", "Mathematics"]
        self.profile.interests = ["Programming", "Reading"]
        self.profile.save()
        
        # Mock embedding vector for testing
        self.mock_embedding = [0.1] * 1536
    
    @mock.patch('api.universities.services.get_embedding')
    def test_update_profile_with_ideal_university(self, mock_get_embedding):
        """Test updating a profile with ideal university description and embedding."""
        # Configure the mock to return a sample embedding
        mock_get_embedding.return_value = self.mock_embedding
        
        # Generate the description and embedding
        description, embedding = generate_ideal_university_description(self.profile)
        
        # Update the profile
        self.profile.ideal_university_description = description
        self.profile.ideal_university_embedding = embedding
        self.profile.save()
        
        # Refresh from database
        self.profile.refresh_from_db()
        
        # Verify the results
        self.assertIsNotNone(self.profile.ideal_university_description)
        self.assertIsNotNone(self.profile.ideal_university_embedding)
        self.assertEqual(self.profile.ideal_university_description, description)
        # Cast to list before comparison to avoid ambiguous truth value error
        self.assertEqual(list(self.profile.ideal_university_embedding), embedding)
        
        # Verify the description content
        self.assertIn("Computer Science", self.profile.ideal_university_description)
        self.assertIn("Mathematics", self.profile.ideal_university_description)
        
        # Verify the embedding dimensions
        self.assertEqual(len(self.profile.ideal_university_embedding), 1536)