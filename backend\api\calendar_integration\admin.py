from django.contrib import admin
from .models import MasterGoogleCredentials, CalendarEvent


@admin.register(MasterGoogleCredentials)
class MasterGoogleCredentialsAdmin(admin.ModelAdmin):
    list_display = ('id', 'created_at', 'updated_at', 'service_account_enabled')
    list_filter = ('service_account_enabled',)
    search_fields = ('id', 'created_at', 'updated_at', 'service_account_enabled')
    readonly_fields = ('created_at', 'updated_at')

@admin.register(CalendarEvent)
class CalendarEventAdmin(admin.ModelAdmin):
    list_display = ('id', 'calendar_event_id', 'booking', 'user', 'created_at')
    list_filter = ('created_at',)
    readonly_fields = ('created_at', 'updated_at')
