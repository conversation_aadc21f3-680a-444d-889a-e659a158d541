from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase, APIClient
from django.contrib.auth import get_user_model
from api.universities.models import University, ShortlistItem

User = get_user_model()

class ShortlistCreateViewTests(APITestCase):
    def setUp(self):
        # Create a high school student user
        self.high_school_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        # Create a college student user (for testing user type restriction)
        self.college_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        # Create test universities
        self.university1 = University.objects.create(
            unitid="12345",
            institution="Test University 1",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        self.university2 = University.objects.create(
            unitid="67890",
            institution="Test University 2",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        
        # Setup the client and URL
        self.client = APIClient()
        self.url = reverse('shortlist')
    
    def test_create_shortlist_item_success(self):
        """Test creating a shortlist item with valid data."""
        self.client.force_authenticate(user=self.high_school_user)
        response = self.client.post(self.url, {"university_id": "12345"})
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(ShortlistItem.objects.count(), 1)
        self.assertEqual(ShortlistItem.objects.first().university.unitid, "12345")
        self.assertEqual(ShortlistItem.objects.first().user, self.high_school_user)
    
    def test_create_shortlist_item_duplicate(self):
        """Test attempting to create a duplicate shortlist item."""
        self.client.force_authenticate(user=self.high_school_user)
        # Create the initial shortlist item
        ShortlistItem.objects.create(
            user=self.high_school_user,
            university=self.university1
        )
        
        # Attempt to create a duplicate
        response = self.client.post(self.url, {"university_id": "12345"})
        
        self.assertEqual(response.status_code, status.HTTP_409_CONFLICT)
        self.assertEqual(ShortlistItem.objects.count(), 1)  # No new item should be created
        self.assertEqual(response.data, {"error": "University already in shortlist"})
    
    def test_create_shortlist_item_invalid_university(self):
        """Test creating a shortlist item with an invalid university ID."""
        self.client.force_authenticate(user=self.high_school_user)
        response = self.client.post(self.url, {"university_id": "invalid"})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(ShortlistItem.objects.count(), 0)
        self.assertEqual(response.data, {"error": "Invalid university ID"})
    
    def test_create_shortlist_item_unauthenticated(self):
        """Test that unauthenticated users cannot create shortlist items."""
        response = self.client.post(self.url, {"university_id": "12345"})
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(ShortlistItem.objects.count(), 0)
    
    def test_create_shortlist_item_wrong_user_type(self):
        """Test that non-high school students cannot create shortlist items."""
        self.client.force_authenticate(user=self.college_user)
        response = self.client.post(self.url, {"university_id": "12345"})
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        self.assertEqual(ShortlistItem.objects.count(), 0)
    
    def test_create_shortlist_item_malformed_request(self):
        """Test that malformed requests return a 400 error."""
        self.client.force_authenticate(user=self.high_school_user)
        response = self.client.post(self.url, {"invalid_field": "12345"})
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertEqual(ShortlistItem.objects.count(), 0)
        self.assertEqual(response.data, {"error": "Invalid request format"})

class ShortlistDeleteViewTests(APITestCase):
    def setUp(self):
        # Create a high school student user
        self.high_school_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        # Create another high school student user for testing ownership
        self.other_high_school_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        # Create a college student user (for testing user type restriction)
        self.college_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        # Create test universities
        self.university1 = University.objects.create(
            unitid="12345",
            institution="Test University 1",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        self.university2 = University.objects.create(
            unitid="67890",
            institution="Test University 2",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        
        # Setup the client
        self.client = APIClient()
    
    def test_delete_shortlist_item_success(self):
        """Test successful deletion of a ShortlistItem."""
        # Authenticate as high school student
        self.client.force_authenticate(user=self.high_school_user)
        
        # Create a ShortlistItem for the user
        shortlist_item = ShortlistItem.objects.create(
            user=self.high_school_user,
            university=self.university1
        )
        
        # Get the URL for deleting the ShortlistItem
        url = reverse('shortlist-delete', kwargs={'college_id': '12345'})
        
        # Send DELETE request
        response = self.client.delete(url)
        
        # Verify response status code is 204 No Content
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        
        # Verify the ShortlistItem is deleted
        self.assertEqual(ShortlistItem.objects.count(), 0)
    
    def test_delete_nonexistent_shortlist_item(self):
        """Test deletion of a non-existent ShortlistItem."""
        # Authenticate as high school student
        self.client.force_authenticate(user=self.high_school_user)
        
        # Get the URL for deleting a non-existent ShortlistItem
        url = reverse('shortlist-delete', kwargs={'college_id': '99999'})
        
        # Send DELETE request
        response = self.client.delete(url)
        
        # Verify response status code is 404 Not Found
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Verify response body contains expected error message
        self.assertEqual(response.data, {"error": "Shortlist item not found"})
    
    def test_delete_unauthenticated(self):
        """Test that unauthenticated users cannot delete ShortlistItems."""
        # Create a ShortlistItem
        shortlist_item = ShortlistItem.objects.create(
            user=self.high_school_user,
            university=self.university1
        )
        
        # Get the URL for deleting the ShortlistItem
        url = reverse('shortlist-delete', kwargs={'college_id': '12345'})
        
        # Send DELETE request without authentication
        response = self.client.delete(url)
        
        # Verify response status code is 401 Unauthorized
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Verify the ShortlistItem is not deleted
        self.assertEqual(ShortlistItem.objects.count(), 1)
    
    def test_delete_non_high_school_student(self):
        """Test that non-high school students cannot delete ShortlistItems."""
        # Create a ShortlistItem
        shortlist_item = ShortlistItem.objects.create(
            user=self.high_school_user,
            university=self.university1
        )
        
        # Authenticate as college student
        self.client.force_authenticate(user=self.college_user)
        
        # Get the URL for deleting the ShortlistItem
        url = reverse('shortlist-delete', kwargs={'college_id': '12345'})
        
        # Send DELETE request
        response = self.client.delete(url)
        
        # Verify response status code is 403 Forbidden
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
        
        # Verify the ShortlistItem is not deleted
        self.assertEqual(ShortlistItem.objects.count(), 1)
    
    def test_delete_other_user_shortlist_item(self):
        """Test deletion of a ShortlistItem owned by another user."""
        # Create a ShortlistItem for the other high school user
        shortlist_item = ShortlistItem.objects.create(
            user=self.other_high_school_user,
            university=self.university1
        )
        
        # Authenticate as high school student
        self.client.force_authenticate(user=self.high_school_user)
        
        # Get the URL for deleting the ShortlistItem
        url = reverse('shortlist-delete', kwargs={'college_id': '12345'})
        
        # Send DELETE request
        response = self.client.delete(url)
        
        # Verify response status code is 404 Not Found (item not found for THIS user)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
        # Verify the ShortlistItem is not deleted
        self.assertEqual(ShortlistItem.objects.count(), 1)

class ShortlistViewTests(APITestCase):
    def setUp(self):
        # Create a high school student user
        self.high_school_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        # Create a college student user (for testing user type restriction)
        self.college_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        # Create test universities
        self.university1 = University.objects.create(
            unitid="12345",
            institution="Test University 1",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        self.university2 = University.objects.create(
            unitid="67890",
            institution="Test University 2",
            city="Test City",
            state_territory="Test State",
            zip_code="12345"
        )
        
        # Create a shortlist item for the high school user
        self.shortlist_item = ShortlistItem.objects.create(
            user=self.high_school_user,
            university=self.university1
        )
        
        # Setup the client and URL
        self.client = APIClient()
        self.url = reverse('shortlist')
    
    def test_get_shortlist_success(self):
        """Test retrieving the shortlist for an authenticated high school student."""
        self.client.force_authenticate(user=self.high_school_user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['shortlist_data']), 1)
        self.assertEqual(response.data['shortlist_data'][0]['unitid'], "12345")
        self.assertEqual(response.data['shortlist_data'][0]['college_name'], "Test University 1")
        self.assertTrue(response.data['shortlist_data'][0]['bookmarked'])
    
    def test_get_shortlist_empty(self):
        """Test retrieving an empty shortlist."""
        # Create a new high school user with no shortlisted items
        empty_user = User.objects.create_user(
            email="<EMAIL>",
            password="testpass123",
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        
        self.client.force_authenticate(user=empty_user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['shortlist_data']), 0)
    
    def test_get_shortlist_unauthenticated(self):
        """Test that unauthenticated users cannot retrieve the shortlist."""
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_get_shortlist_wrong_user_type(self):
        """Test that non-high school students cannot retrieve the shortlist."""
        self.client.force_authenticate(user=self.college_user)
        response = self.client.get(self.url)
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)