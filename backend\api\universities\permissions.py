from rest_framework import permissions
from django.contrib.auth import get_user_model

User = get_user_model()

class IsHighSchoolStudent(permissions.BasePermission):
    """
    Custom permission to only allow high school students to access the view.
    """
    def has_permission(self, request, view):
        if not request.user.is_authenticated:
            return False
        return request.user.user_type == User.HIGH_SCHOOL_STUDENT_TYPE