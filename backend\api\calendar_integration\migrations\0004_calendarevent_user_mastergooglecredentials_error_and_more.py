# Generated by Django 4.2.13 on 2024-11-05 21:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('bookings', '0004_alter_booking_creator_status_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('calendar_integration', '0003_mastergooglecredentials'),
    ]

    operations = [
        migrations.AddField(
            model_name='calendarevent',
            name='user',
            field=models.ForeignKey(default=None, on_delete=django.db.models.deletion.CASCADE, related_name='calendar_events', to=settings.AUTH_USER_MODEL),
            preserve_default=False,
        ),
        migrations.AddField(
            model_name='mastergooglecredentials',
            name='error',
            field=models.TextField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='calendarevent',
            name='booking',
            field=models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to='bookings.booking'),
        ),
        migrations.DeleteModel(
            name='GoogleAuthCredentials',
        ),
    ]
