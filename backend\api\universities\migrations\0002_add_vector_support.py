from django.db import migrations
from pgvector.django import VectorField

class Migration(migrations.Migration):

    dependencies = [
        ('universities', '0001_initial'),
    ]

    operations = [
        migrations.RunSQL("CREATE EXTENSION IF NOT EXISTS vector;"),
        migrations.AlterField(
            model_name='university',
            name='embedding',
            field=VectorField(null=True, blank=True, dimensions=1536),
        ),
    ]
