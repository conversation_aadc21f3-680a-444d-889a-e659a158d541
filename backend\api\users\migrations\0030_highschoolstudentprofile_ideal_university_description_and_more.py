# Generated by Django 4.2.13 on 2025-06-09 20:32

from django.db import migrations, models
import pgvector.django.vector


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0029_alter_highschoolstudentprofile_geographic_preference_type'),
    ]

    operations = [
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='ideal_university_description',
            field=models.TextField(blank=True, help_text='Generated description of the ideal university.', null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='ideal_university_embedding',
            field=pgvector.django.vector.VectorField(blank=True, dimensions=1536, help_text='Embedding vector for the ideal university description.', null=True),
        ),
    ]
