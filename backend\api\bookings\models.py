from datetime import timedelta

from django.db import models
from django.conf import settings
from django.utils import timezone
from django.core.exceptions import ValidationError
from django.contrib.auth import get_user_model
from django.db.models import Q

User = get_user_model()


class TrailblazerBookingStatus(models.Model):
    TRAILBLAZER_STATUS_CHOICES = [
        ('pending', 'Pending'),
        ('confirmed', 'Confirmed'),
        ('declined', 'Declined'),
    ]
    booking = models.ForeignKey('Booking', on_delete=models.CASCADE, related_name='trailblazer_statuses')
    trailblazer = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='booking_statuses')
    status = models.CharField(max_length=20, choices=TRAILBLAZER_STATUS_CHOICES, default='pending')
    updated_at = models.DateTimeField(auto_now=True)
    decline_reason = models.TextField(null=True, blank=True)
    
    class Meta:
        unique_together = ('booking', 'trailblazer')
        
    def __str__(self):
        return f"{self.trailblazer} - {self.booking} - {self.status}"


class Booking(models.Model):
    CREATOR_STATUS_CHOICES = [
        ('confirmed', 'Confirmed'),
        ('cancelled', 'Cancelled'),
    ]
    booked_by = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='user_bookings')
    start_time = models.DateTimeField()
    end_time = models.DateTimeField()
    message = models.CharField(max_length=280)
    number_of_students = models.IntegerField(default=1)
    created_at = models.DateTimeField(auto_now_add=True)
    duration = models.DurationField(default=timedelta(hours=1))
    is_available = models.BooleanField(default=True)
    creator_status = models.CharField(max_length=20, choices=CREATOR_STATUS_CHOICES, default='confirmed')
    creator_status_updated_at = models.DateTimeField(auto_now=True)
    creator_cancel_reason = models.TextField(null=True, blank=True)
    meet_link = models.URLField(max_length=1024, blank=True, null=True)  # New field for the Meet link
    creator_timezone = models.CharField(max_length=64, blank=True, null=True)
    proposed_times = models.JSONField(default=list, null=True, blank=True)
    proposed_time_confirmed = models.BooleanField(default=False)
    @property
    def trailblazers(self):
        # Returns a queryset of User objects associated with this booking
        return User.objects.filter(booking_statuses__booking=self)

    @property
    def status(self):
        if self.creator_status == 'cancelled':
            return 'cancelled'
        
        # creator status is confirmed
        
        trailblazer_statuses = self.trailblazer_statuses.values_list('status', flat=True)

        # if there are no trailblazers, then the booking is pending or waiting for trailblazers
        if not trailblazer_statuses:
            return 'pending'

        # If all trailbalzers have declined, then booking is declined
        if all(status == 'declined' for status in trailblazer_statuses):
            return 'declined'

        # If any trailblazer has confirmed, then booking is confirmed, else is pending
        if any(status == 'confirmed' for status in trailblazer_statuses):
            preliminary_status = 'confirmed'
        elif any(status == 'pending' for status in trailblazer_statuses):
            preliminary_status = 'pending'
        else:
            preliminary_status = 'pending'  # Default fallback if no statuses meet above criteria
        
        # Final check for completion based on end time
        if preliminary_status in ['pending', 'confirmed'] and self.end_time < timezone.now():
            return 'completed'
        
        return preliminary_status

    def get_current_trailblazer_status(self, trailblazer):
        # if trailbalzer is None, return None
        if trailblazer is None:
            return None

        # if trailblazer is not  associated with this booking, return None
        if not self.trailblazers.filter(id=trailblazer.id).exists():
            return None

        # if creator status is cancelled, return cancelled
        if self.creator_status == 'cancelled':
            return 'cancelled'

        # creator status is confirmed

        # get the status of the trailblazer
        trailblazer_status = self.trailblazer_statuses.get(trailblazer=trailblazer).status

        # if it is confirmed and end time is in the past, return completed
        if trailblazer_status == 'confirmed' and self.end_time < timezone.now():
            return 'completed'

        return trailblazer_status

    def save(self, *args, **kwargs):
        # Set end_time if it is not already set
        if not self.end_time:
            self.end_time = self.start_time + self.duration

        # Ensure both start_time and end_time are timezone-aware
        if timezone.is_naive(self.start_time):
            self.start_time = timezone.make_aware(self.start_time, timezone=timezone.utc)
        if timezone.is_naive(self.end_time):
            self.end_time = timezone.make_aware(self.end_time, timezone=timezone.utc)

        # Save the instance first to assign an id if it doesn't have one
        initial_save = False
        if self.pk is None:
            super().save(*args, **kwargs)
            initial_save = True

        # Now perform overlapping bookings check if trailblazers are set
        if not self.is_available and self.trailblazers.exists():
            for trailblazer in self.trailblazers.all():
                overlapping_bookings = Booking.objects.filter(
                    trailblazer_statuses__trailblazer=trailblazer,
                    start_time__lt=self.end_time,
                    end_time__gt=self.start_time
                ).exclude(id=self.id)
                if overlapping_bookings.exists():
                    raise ValidationError(f"Time slot overlaps for Trailblazer {trailblazer.email}.")

        # Save again only if this is not the initial save
        if not initial_save:
            super().save(*args, **kwargs)

    def clean(self):
        super().clean()
        if self.trailblazers.count() > 3:
            raise ValidationError("A booking can have up to three Trailblazers.")
        # if self.start_time < timezone.now() + timedelta(hours=72):
        #     raise ValidationError("Session must be booked at least 72 hours in advance.")
        if self.number_of_students < 0:
            raise ValidationError("Number of students cannot be negative.")

    def set_trailblazers(self, trailblazers_with_status):
        """
        Replaces all trailblazers associated with this booking.
        Accepts a list of tuples: (trailblazer, status).
        Validates that there are no more than three trailblazers and that statuses are valid.
        """
        if len(trailblazers_with_status) > 3:
            raise ValidationError("A booking can have up to three Trailblazers.")

        # Clear existing trailblazers
        self.trailblazer_statuses.all().delete()

        for trailblazer, status in trailblazers_with_status:
            # Check for valid status
            if status not in dict(TrailblazerBookingStatus.TRAILBLAZER_STATUS_CHOICES):
                raise ValidationError(f"Invalid status '{status}' for trailblazer {trailblazer}.")
            
            # Add new trailblazer with specified status
            TrailblazerBookingStatus.objects.create(
                booking=self,
                trailblazer=trailblazer,
                status=status
            )

    def add_trailblazer(self, trailblazer, status='pending'):
        """
        Adds a single trailblazer to this booking with a specified status.
        Validates the total number of trailblazers and ensures no duplicate trailblazer is added.
        """
        # Check if adding another trailblazer would exceed the limit
        if self.trailblazer_statuses.count() >= 3:
            raise ValidationError("A booking can have up to three Trailblazers.")

        # Check if the trailblazer is already associated with this booking
        if self.trailblazer_statuses.filter(trailblazer=trailblazer).exists():
            raise ValidationError(f"Trailblazer {trailblazer} is already assigned to this booking.")

        # Check for valid status
        if status not in dict(TrailblazerBookingStatus.TRAILBLAZER_STATUS_CHOICES):
            raise ValidationError(f"Invalid status '{status}' for trailblazer {trailblazer}.")

        # Add trailblazer with specified status
        TrailblazerBookingStatus.objects.create(
            booking=self,
            trailblazer=trailblazer,
            status=status
        )

    


