import { useState, useEffect } from 'react';
import { AutoComplete } from '@/components/ui/autocomplete'; // Assuming AutoComplete is already implemented
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useAuth } from '@/context/AuthProvider'; // Assuming useAuth is a custom hook to get the auth token
import { cn } from "@/lib/utils"; // Assuming you have a utility for className merging
import ErrorMessage from '@/components/ui/errorMessage'
import { set } from 'date-fns';

export const OrganizationTagPicker = ({ field, setPendingCustomTags }) => {
    const [tags, setTags] = useState([]); // All available tags
    const [selectedTags, setSelectedTags] = useState(field.value || []); // Use field.value as the initial value
    const [customTag, setCustomTag] = useState(''); // Custom tag input
    const [isLoading, setIsLoading] = useState(false);
    const { getToken } = useAuth(); // Assuming useAuth is a custom hook to get the auth token
    const [error, setError] = useState('')

    // Fetch tags from the backend
    useEffect(() => {
        const fetchTags = async () => {
            setIsLoading(true);
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/tags/list/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${getToken()}`,
                    },
                });
                if (!response.ok) throw new Error('Failed to fetch tags');
                const data = await response.json();
                setTags(data.tags.map((tag) => ({ value: tag.id, label: tag.name }))); // Map tags to value/label format
            } catch (error) {
                console.error('Error fetching tags:', error);
            } finally {
                setIsLoading(false);
            }
        };
        fetchTags();
    }, [getToken]);

    const handleSetSelectedTags = (selectedTag) => {
        // Prevent duplicate tags
        if (selectedTags.some((tag) => tag.value === selectedTag.value)) {
            setError('Tag already selected.')
            return;
        }

        // Limit to 3 tags
        if (selectedTags.length >= 3) {
            setError('You can only have up to 3 tags in total.')
            return;
        }

        setError('')

        const updatedTags = [...selectedTags, selectedTag];
        setSelectedTags(updatedTags);
        field.onChange(updatedTags); // Update the form state with full objects (value and label)
    };

    const handleRemoveTag = (tagId) => {
        const updatedTags = selectedTags.filter((tag) => tag.value !== tagId);
        setSelectedTags(updatedTags);
        field.onChange(updatedTags); // Update the form state with full objects (value and label)

        // Remove the tag from pendingCustomTags if it exists there
        setPendingCustomTags((prev) => prev.filter((tag) => tag.value !== tagId));
    };

    const handleAddCustomTag = () => {
        if (!customTag.trim()) {
            setError('Custom tag cannot be empty.');
            return;
        }

        const tempId = Date.now();

        // Add the custom tag as a new object
        const newTag = { value: tempId, label: customTag }; // Use a temporary ID for custom tags

        // Prevent duplicate tags
        if (selectedTags.some((tag) => tag.label.toLowerCase() === customTag.toLowerCase())) {
            setError('Tag already selected.');
            return;
        }

        // Limit to 3 tags
        if (selectedTags.length >= 3) {
            setError('You can only have up to 3 tags in total.');
            return;
        }

        const updatedTags = [...selectedTags, newTag];
        setSelectedTags(updatedTags);
        field.onChange(updatedTags); // Update the form state with full objects (value and label)
        setPendingCustomTags((prev) => [...prev, newTag]); // Add to pending custom tags
        setCustomTag(''); // Clear the custom tag input
    };

    return (
        <div>
            <h5 className="text-sm mb-4">Choose up to 3 or add your own</h5>
            {error && <ErrorMessage message={error} />}
            <AutoComplete
                options={tags} // Pass tags directly as value/label objects
                emptyMessage="No tags found"
                placeholder="Search or select organizations"
                isLoading={isLoading}
                value={null} // Reset the input value after selection
                onValueChange={handleSetSelectedTags} // Handle tag selection
                showAllOptionsByDefault={true} // Show all options by default
                clearInputOnSelect={true}
                className="flex h-9 w-full rounded-md border border-input bg-white py-6 px-4 text-sm shadow-sm transition-colors file:border-0 file:bg-white file:text-sm file:font-medium file:text-foreground placeholder:text-muted-foreground focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-ring disabled:cursor-not-allowed disabled:opacity-50"
            />
            <div className="mt-4 flex flex-wrap gap-2">
                {selectedTags.map((tag) => (
                    <div
                        key={tag.value}
                        className={cn(
                            "px-3 py-2 rounded-lg text-sm",
                            "bg-[#ADFFC2] text-black border-2 border-green-500", 
                            "hover:bg-green-400 cursor-pointer"
                        )}

                        onClick={() => handleRemoveTag(tag.value)}
                    >
                        {tag.label}
                    </div>
                ))}
            </div>
            <div className="mt-4">
                <Input
                    type="text"
                    placeholder="Add a different organization"
                    value={customTag}
                    onChange={(e) => setCustomTag(e.target.value)}
                    className="mb-2"
                />
                <Button type="button" variant="secondary" onClick={handleAddCustomTag}>
                    Add Organization
                </Button>
            </div>
        </div>
    );
};