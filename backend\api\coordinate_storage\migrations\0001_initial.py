# Generated by Django 4.2.13 on 2024-12-25 01:48

from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='SharedAPIResult',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('query', models.Char<PERSON>ield(max_length=255, unique=True)),
                ('result', models.J<PERSON>NField()),
                ('timestamp', models.DateTimeField(auto_now=True)),
            ],
        ),
    ]
