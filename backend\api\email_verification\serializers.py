from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import EmailVerificationCode
import random

User = get_user_model()

class SendVerificationEmailSerializer(serializers.Serializer):


    def validate(self, attrs):
        user = self.context['request'].user
        if not user:
            raise serializers.ValidationError("Authentication required.")
        return attrs

class VerifyEmailSerializer(serializers.Serializer):
    code = serializers.CharField(max_length=6)

    def validate_code(self, value):
        if not value.isdigit() or len(value) != 6:
            raise serializers.ValidationError("Invalid code format.")
        return value

    def validate(self, attrs):
        user = self.context['request'].user
        if not user:
            raise serializers.ValidationError("Authentication required.")
        code = attrs.get('code')

        try:
            verification = EmailVerificationCode.objects.get(user=user, code=code)
        except EmailVerificationCode.DoesNotExist:
            raise serializers.ValidationError("Invalid code.")

        if verification.is_expired():
            raise serializers.ValidationError("Verification code has expired.")

        attrs['user'] = user
        attrs['verification'] = verification
        return attrs

class EmailVerificationCodeSerializer(serializers.ModelSerializer):
    class Meta:
        model = EmailVerificationCode
        fields = ['code']
