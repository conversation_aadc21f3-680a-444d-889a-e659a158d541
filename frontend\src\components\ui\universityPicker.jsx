import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { AutoComplete } from "@/components/ui/autocomplete";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Input } from "@/components/ui/input"; // Assuming you have an Input component
import { FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"; // Assuming you have form components

export const universityTags = [
    { label: "Public College", value: "public" },
    { label: "Private College", value: "private" },
    { label: "Liberal Arts", value: "liberal-arts" },
    { label: "Ivy League", value: "ivy-league" },
    { label: "Hispanic-Serving Institution (HSI)", value: "hsi" },
    { label: "Tribal College and University (TCU)", value: "tcu" },
    { label: "Historically Black College and University (HBCU)", value: "hbcu" },
    { label: "Single-Gender", value: "single-gender" },
    { label: "Faith Based", value: "faith-based" },
    { label: "Community College", value: "community" },
    { label: "Military Academy", value: "military" },
    { label: "Technical and Vocational College", value: "technical" },
];

// UniversityCombobox component
export const UniversityCombobox = ({ field, form }) => {
    const [universities, setUniversities] = useState([]);
    const [isLoading, setIsLoading] = useState(false);
    const [city, setCity] = useState("");

    useEffect(() => {
        const fetchUniversities = async () => {
            if (!city) return;
            setIsLoading(true);
            try {
                const response = await fetch(`https://api.data.gov/ed/collegescorecard/v1/schools?api_key=${process.env.NEXT_PUBLIC_COLLEGE_SCORECARD_API_KEY}&school.city=${city}&fields=id,school.name`);
                const data = await response.json();
                const filtered = data.results.map(university => ({ value: university["school.name"] }));
                setUniversities(filtered);
            } catch (error) {
                console.error("Error fetching universities:", error);
            } finally {
                setIsLoading(false);
            }
        };

        fetchUniversities();
    }, [city]);

    const setValue = (value) => {
        field.onChange(value ? value : "");
    };

    return (
        <FormItem>
            <FormLabel>City*</FormLabel>
            <FormControl>
                <Input
                    type="text"
                    placeholder="Enter city"
                    value={city}
                    onChange={(e) => setCity(e.target.value)}
                    className="w-full rounded-lg border border-slate-300 bg-white px-4 py-2 pr-10 text-sm shadow-sm outline-none transition focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                />
            </FormControl>
            <div className="mb-4"></div>
            <FormMessage />
            <FormLabel>University*</FormLabel>
            <FormControl>
                <AutoComplete
                    options={universities}
                    emptyMessage="No universities found"
                    placeholder="Find a university"
                    isLoading={isLoading}
                    onValueChange={setValue}
                    value={field.value}
                    disabled={!city}
                />
            </FormControl>
            <FormMessage />
        </FormItem>
    );
};

// SchoolTypeToggle component
export const SchoolTypeToggle = (field) => {
    const { value: selectedTypes, onChange: setSelectedTypes } = field;

    return (
        <ToggleGroup
            type="multiple"
            value={selectedTypes}
            onValueChange={setSelectedTypes}
            className="flex flex-wrap gap-3 justify-start"
        >
            {universityTags.map((type) => (
                <ToggleGroupItem
                    key={type.value}
                    value={type.value}
                    aria-label={type.label}
                    className={cn(
                        "px-3 py-2 rounded-md text-sm",
                        selectedTypes.includes(type.value)
                            ? "data-[state=on]:bg-[#ADFFC2] text-primary-foreground border-green-500 border-2"
                            : "bg-white border border-gray-200 text-gray-700"
                    )}
                >
                    {type.label}
                </ToggleGroupItem>
            ))}
        </ToggleGroup>
    );
};