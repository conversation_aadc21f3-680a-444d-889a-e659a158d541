import os
import csv
import logging
import time
from pathlib import Path
from django.db import migrations
from django.conf import settings
from langchain_openai import OpenAIEmbeddings

logger = logging.getLogger(__name__)

def generate_embedding(texts, retry_count=0):
    """Generate bulk embedding vectors for the given list of texts using OpenAI's API."""
    try:
        
        # Initialize the OpenAI embeddings client with the API key from settings
        embeddings = OpenAIEmbeddings(
            model="text-embedding-3-small",
            openai_api_key=os.environ.get('OPENAI_API_KEY')
        )
        
        # Generate bulk embedding vectors
        embedding_vectors = embeddings.embed_documents(texts)
        return embedding_vectors
    except Exception as e:
        # Implement retry logic with exponential backoff
        if retry_count < 3:
            wait_time = 2 ** retry_count
            logger.warning(f"Error generating bulk embedding: {e}. Retrying in {wait_time} seconds...")
            time.sleep(wait_time)
            return generate_embedding(texts, retry_count + 1)
        else:
            logger.error(f"Failed to generate bulk embedding after {retry_count} retries: {e}")
            # Return a list of None values corresponding to each text
            return [None] * len(texts)

def format_university_text(data):
    """Format university data into a descriptive text for embedding generation."""
    text_parts = [
        f"University: {data.get('Institution', '')}",
        f"Location: {data.get('City', '')}, {data.get('State/Territory', '')}",
        f"Type: {data.get('Public/private?', '')}",
        f"Predominant degree: {data.get('Predominant degree awarded', '')}",
        f"Highest degree: {data.get('Highest degree awarded', '')}",
    ]
    
    # Add more descriptive elements if available
    if data.get('Special focus'):
        text_parts.append(f"Special focus: {data.get('Special focus')}")
        
    if data.get('Religious affiliation'):
        text_parts.append(f"Religious affiliation: {data.get('Religious affiliation')}")
    
    # Add information about student demographics
    demographics = []
    if data.get('% Students White'):
        demographics.append(f"{data.get('% Students White')}% White")
    if data.get('% Students Black'):
        demographics.append(f"{data.get('% Students Black')}% Black")
    if data.get('% Students Hispanic'):
        demographics.append(f"{data.get('% Students Hispanic')}% Hispanic")
    if data.get('% Students Asian'):
        demographics.append(f"{data.get('% Students Asian')}% Asian")
    
    if demographics:
        text_parts.append(f"Student demographics: {', '.join(demographics)}")
    
    # Add information about popular majors
    majors = []
    if data.get('% Degrees Business, Management, Marketing, & Related Support Services'):
        majors.append(f"Business: {data.get('% Degrees Business, Management, Marketing, & Related Support Services')}%")
    if data.get('% Degrees Computer & Information Sciences & Support Services'):
        majors.append(f"Computer Science: {data.get('% Degrees Computer & Information Sciences & Support Services')}%")
    if data.get('% Degrees Engineering'):
        majors.append(f"Engineering: {data.get('% Degrees Engineering')}%")
        
    if majors:
        text_parts.append(f"Popular majors: {', '.join(majors)}")
        
    # Add financial information
    if data.get('Average net price: $0 - $30,000 family income'):
        text_parts.append(f"Average net price (low income): ${data.get('Average net price: $0 - $30,000 family income')}")
        
    if data.get('Median earnings (10 years after entry)'):
        text_parts.append(f"Median earnings (10 years after entry): ${data.get('Median earnings (10 years after entry)')}")
        
    return " ".join(text_parts)

def parse_percentage(s):
    try:
        # Remove whitespace and percent sign
        s = s.strip().replace('%', '')
        # Check for empty or invalid strings
        if not s or s == '-':
            return None
        return float(s) / 100
    except ValueError:
        return None

def load_university_data(apps, schema_editor):
    University = apps.get_model('universities', 'University')
    
    # Path to the CSV file
    csv_filename = 'test_universities.csv' if settings.TESTING else 'universities.csv'
    csv_file_path = Path(settings.APPS_DIR) / 'external_data' / csv_filename
    
    if not csv_file_path.exists():
        logger.error(f"Universities CSV file not found at {csv_file_path}")
        return
        
    # Keep track of processed universities for idempotency
    processed_count = 0
    skipped_count = 0
    error_count = 0
    batch_size = 500
    batch_items = []  # list of tuples (university_data, university_text)
    
    with open(csv_file_path, 'r', encoding='utf-8-sig') as csv_file:
        reader = csv.DictReader(csv_file)
        
        for row in reader:
            try:
                # Extract and validate UNITID
                raw_unitid = row.get('UNITID')
                institution_name = row.get('Institution', 'Unknown')
                
                # Check if UNITID exists
                if raw_unitid is None or raw_unitid == '':
                    logger.warning(f"Skipping university {institution_name} - missing UNITID")
                    skipped_count += 1
                    continue
                
                # Validate UNITID is a valid numeric value and convert to string
                try:
                    # Handle potential numeric or string input
                    unitid = str(int(raw_unitid)).strip()
                    
                    # Additional validation to ensure non-empty string
                    if not unitid:
                        raise ValueError("UNITID converted to empty string")
                        
                    logger.debug(f"Processing university {institution_name} with UNITID: {unitid}")
                except (ValueError, TypeError) as e:
                    logger.warning(f"Skipping university {institution_name} - invalid UNITID format: {raw_unitid}, Error: {e}")
                    skipped_count += 1
                    continue
                
                # Check if university already exists (idempotency check)
                if University.objects.filter(unitid=unitid).exists():
                    logger.debug(f"Skipping existing university with UNITID: {unitid}")
                    skipped_count += 1
                    continue
                    
                # Prepare university data
                university_data = {
                    'unitid': unitid,
                    'institution': row.get('Institution'),
                    'city': row.get('City'),
                    'state_territory': row.get('State/Territory'),
                    'zip_code': row.get('Zip code'),
                    'institution_website': row.get('Institution website'),
                    'predominant_degree_awarded': row.get('Predominant degree awarded'),
                    'highest_degree_awarded': row.get('Highest degree awarded'),
                    'public_private': row.get('Public/private?'),
                    'region': row.get('Region'),
                    'locale': row.get('Locale'),
                    'special_focus': row.get('Special focus'),
                    'is_hbcu': True if row.get('Historically Black College & University') == 'Yes' else False if row.get('Historically Black College & University') == 'No' else None,
                    'is_tribal': True if row.get('Tribal College & University or Native-serving Institution') == 'Yes' else False if row.get('Tribal College & University or Native-serving Institution') == 'No' else None,
                    'is_aanapisi': True if row.get('Asian American & Native American Pacific Islander-serving Institution') == 'Yes' else False if row.get('Asian American & Native American Pacific Islander-serving Institution') == 'No' else None,
                    'is_hsi': True if row.get('Hispanic-serving Institution') == 'Yes' else False if row.get('Hispanic-serving Institution') == 'No' else None,
                    'is_single_gender': True if row.get('Single-gender college') == 'Yes' else False if row.get('Single-gender college') == 'No' else None,
                    'religious_affiliation': row.get('Religious affiliation'),
                }
                
                # Add test scores
                for field, csv_field in {
                    'sat_reading_25th': 'SAT Reading/Writing (25th percentile)',
                    'sat_reading_75th': 'SAT Reading/Writing (75th percentile)',
                    'sat_math_25th': 'SAT Math (25th percentile)',
                    'sat_math_75th': 'SAT Math (75th percentile)',
                    'act_25th': 'ACT (25th percentile)',
                    'act_75th': 'ACT (75th percentile)',
                }.items():
                    value = row.get(csv_field)
                    if value and value.strip():
                        try:
                            university_data[field] = int(float(value))
                        except (ValueError, TypeError):
                            pass
                
                # Add boolean fields
                university_data['has_open_admissions'] = True if row.get('Open admissions policy') == 'Yes' else False if row.get('Open admissions policy') == 'No' else None
                
                # Add numeric fields with proper type conversion, excluding undergrad_count
                numeric_fields = {
                    'acceptance_rate': 'Acceptance rate',
                    'pct_white_students': '% Students White',
                    'pct_black_students': '% Students Black',
                    'pct_hispanic_students': '% Students Hispanic',
                    'pct_asian_students': '% Students Asian',
                    'pct_aian_students': '% Students American Indian or Alaska Native',
                    'pct_nhpi_students': '% Students Native Hawaiian or Pacific Islander',
                    'pct_multiracial_students': '% Students 2 or More Races',
                    'pct_unknown_race_students': '% Students Race Unknown',
                    'pct_men_students': '% Students Men',
                    'pct_women_students': '% Students Women',
                    'student_faculty_ratio': 'Student-to-Faculty ratio',
                    'pct_white_faculty': '% Faculty White',
                    'pct_black_faculty': '% Faculty Black',
                    'pct_hispanic_faculty': '% Faculty Hispanic',
                    'pct_asian_faculty': '% Faculty Asian',
                    'pct_aian_faculty': '% Faculty American Indian or Alaska Native',
                    'pct_nhpi_faculty': '% Faculty Native Hawaiian or Pacific Islander',
                    'pct_multiracial_faculty': '% Faculty 2 or More Races',
                    'pct_unknown_race_faculty': '% Faculty Race Unknown',
                    'pct_men_faculty': '% Faculty Men',
                    'pct_women_faculty': '% Faculty Women',
                }

                for field, csv_field in numeric_fields.items():
                    value = row.get(csv_field)
                    if value and value.strip():
                        try:
                            university_data[field] = parse_percentage(value)
                        except (ValueError, TypeError):
                            pass

                # Process undergrad_count separately as an integer
                undergrad_value = row.get('# Undergraduates')
                if undergrad_value and undergrad_value.strip():
                    try:
                        university_data['undergrad_count'] = int(undergrad_value)
                    except ValueError:
                        pass
                
                # Add degree fields (too many to list individually)
                degree_field_mapping = {
                    "Associate/Bachelor's: Agriculture, Agriculture Operations, & Related Sciences": "degrees_agriculture",
                    "Associate/Bachelor's: Natural Resources & Conservation": "degrees_resources",
                    "Associate/Bachelor's: Architecture & Related Services": "degrees_architecture",
                    "Associate/Bachelor's: Area, Ethnic, Cultural, Gender, & Group Studies": "degrees_ethnic_cultural_gender",
                    "Associate/Bachelor's: Communication, Journalism, & Related Programs": "degrees_communication",
                    "Associate/Bachelor's: Communications Technologies/Technicians & Support Services": "degrees_communications_tech",
                    "Associate/Bachelor's: Computer & Information Sciences & Support Services": "degrees_computer_science",
                    "Associate/Bachelor's: Personal & Culinary Services": "degrees_culinary",
                    "Associate/Bachelor's: Education": "degrees_education",
                    "Associate/Bachelor's: Engineering": "degrees_engineering",
                    "Associate/Bachelor's: Engineering Technologies & Engineering-Related Fields": "degrees_engineering_tech",
                    "Associate/Bachelor's: Foreign Languages, Literatures, & Linguistics": "degrees_language",
                    "Associate/Bachelor's: Family & Consumer Sciences/Human Sciences": "degrees_family_consumer_science",
                    "Associate/Bachelor's: Legal Professions & Studies": "degrees_legal",
                    "Associate/Bachelor's: English Language & Literature/Letters": "degrees_english",
                    "Associate/Bachelor's: Liberal Arts & Sciences, General Studies & Humanities": "degrees_humanities",
                    "Associate/Bachelor's: Library Science": "degrees_library",
                    "Associate/Bachelor's: Biological & Biomedical Sciences": "degrees_biological",
                    "Associate/Bachelor's: Mathematics & Statistics": "degrees_mathematics",
                    "Associate/Bachelor's: Military Technologies & Applied Sciences": "degrees_military",
                    "Associate/Bachelor's: Multi/Interdisciplinary Studies": "degrees_multidiscipline",
                    "Associate/Bachelor's: Parks, Recreation, Leisure, & Fitness Studies": "degrees_parks_recreation_fitness",
                    "Associate/Bachelor's: Philosophy & Religious Studies": "degrees_philosophy",
                    "Associate/Bachelor's: Theology & Religious Vocations": "degrees_theology",
                    "Associate/Bachelor's: Physical Sciences": "degrees_physical_science",
                    "Associate/Bachelor's: Science Technologies/Technicians": "degrees_science_tech",
                    "Associate/Bachelor's: Psychology": "degrees_psychology",
                    "Associate/Bachelor's: Homeland Security, Law Enforcement, Firefighting & Related Protective Services": "degrees_security_law_enforcement",
                    "Associate/Bachelor's: Public Administration & Social Service Professions": "degrees_public_admin",
                    "Associate/Bachelor's: Social Sciences": "degrees_social_science",
                    "Associate/Bachelor's: Construction Trades": "degrees_construction",
                    "Associate/Bachelor's: Mechanic & Repair Technologies/Technicians": "degrees_mechanic_repair_tech",
                    "Associate/Bachelor's: Precision Production": "degrees_precision_production",
                    "Associate/Bachelor's: Transportation & Materials Moving": "degrees_transportation",
                    "Associate/Bachelor's: Visual & Performing Arts": "degrees_visual_performing",
                    "Associate/Bachelor's: Health Professions & Related Programs": "degrees_health",
                    "Associate/Bachelor's: Business, Management, Marketing, & Related Support Services": "degrees_business",
                    "Associate/Bachelor's: History": "degrees_history",
                }
                degree_fields = [k for k in row.keys() if k.startswith("Associate/Bachelor's:")]
                for csv_field in degree_fields:
                    model_field = degree_field_mapping.get(csv_field)
                    if not model_field:
                        continue
                    value = row.get(csv_field)
                    if value and value.strip():
                        try:
                            university_data[model_field] = value.strip()
                        except (ValueError, TypeError):
                            pass
                
                # Add degree percentage fields
                pct_degree_field_mapping = {
                    "% Degrees Agriculture, Agriculture Operations, & Related Sciences": "pct_degrees_agriculture",
                    "% Degrees Natural Resources & Conservation": "pct_degrees_resources",
                    "% Degrees Architecture & Related Services": "pct_degrees_architecture",
                    "% Degrees Area, Ethnic, Cultural, Gender, & Group Studies": "pct_degrees_ethnic_cultural_gender",
                    "% Degrees Communication, Journalism, & Related Programs": "pct_degrees_communication",
                    "% Degrees Communications Technologies/Technicians & Support Services": "pct_degrees_communications_tech",
                    "% Degrees Computer & Information Sciences & Support Services": "pct_degrees_computer_science",
                    "% Degrees Personal & Culinary Services": "pct_degrees_culinary",
                    "% Degrees Education": "pct_degrees_education",
                    "% Degrees Engineering": "pct_degrees_engineering",
                    "% Degrees Engineering Technologies & Engineering-Related Fields": "pct_degrees_engineering_tech",
                    "% Degrees Foreign Languages, Literatures, & Linguistics": "pct_degrees_language",
                    "% Degrees Family & Consumer Sciences/Human Sciences": "pct_degrees_family_consumer_science",
                    "% Degrees Legal Professions & Studies": "pct_degrees_legal",
                    "% Degrees English Language & Literature/Letters": "pct_degrees_english",
                    "% Degrees Liberal Arts & Sciences, General Studies & Humanities": "pct_degrees_humanities",
                    "% Degrees Library Science": "pct_degrees_library",
                    "% Degrees Biological & Biomedical Sciences": "pct_degrees_biological",
                    "% Degrees Mathematics & Statistics": "pct_degrees_mathematics",
                    "% Degrees Military Technologies & Applied Sciences": "pct_degrees_military",
                    "% Degrees Multi/Interdisciplinary Studies": "pct_degrees_multidiscipline",
                    "% Degrees Parks, Recreation, Leisure, & Fitness Studies": "pct_degrees_parks_recreation_fitness",
                    "% Degrees Philosophy & Religious Studies": "pct_degrees_philosophy",
                    "% Degrees Theology & Religious Vocations": "pct_degrees_theology",
                    "% Degrees Physical Sciences": "pct_degrees_physical_science",
                    "% Degrees Science Technologies/Technicians": "pct_degrees_science_tech",
                    "% Degrees Psychology": "pct_degrees_psychology",
                    "% Degrees Homel& Security, Law Enforcement, Firefighting & Related Protective Services": "pct_degrees_security_law_enforcement",
                    "% Degrees Public Administration & Social Service Professions": "pct_degrees_public_admin",
                    "% Degrees Social Sciences": "pct_degrees_social_science",
                    "% Degrees Construction Trades": "pct_degrees_construction",
                    "% Degrees Mechanic & Repair Technologies/Technicians": "pct_degrees_mechanic_repair_tech",
                    "% Degrees Precision Production": "pct_degrees_precision_production",
                    "% Degrees Transportation & Materials Moving": "pct_degrees_transportation",
                    "% Degrees Visual & Performing Arts": "pct_degrees_visual_performing",
                    "% Degrees Health Professions & Related Programs": "pct_degrees_health",
                    "% Degrees Business, Management, Marketing, & Related Support Services": "pct_degrees_business",
                    "% Degrees History": "pct_degrees_history",
                }
                pct_degree_fields = [k for k in row.keys() if k.startswith('% Degrees')]
                for csv_field in pct_degree_fields:
                    model_field = pct_degree_field_mapping.get(csv_field)
                    if not model_field:
                        continue
                    
                    value = row.get(csv_field)
                    if value and value.strip():
                        try:
                            university_data[model_field] = parse_percentage(value)
                        except (ValueError, TypeError):
                            pass
                
                # Add retention and completion rate fields
                completion_fields = {
                    'retention_rate': 'Retention rate',
                    'graduation_rate': 'Graduation rate',
                    'completion_rate_150_pooled': 'Completion rate (150% expected time, pooled 2-year avg, supp small N)',
                    'completion_rate_150': 'Completion rate (150% expected time)',
                    'completion_rate_150_white': 'Completion rate White (150% expected time)',
                    'completion_rate_150_black': 'Completion rate Black  (150% expected time)',
                    'completion_rate_150_hispanic': 'Completion rate Hispanic  (150% expected time)',
                    'completion_rate_150_asian': 'Completion rate Asian  (150% expected time)',
                    'completion_rate_150_aian': 'Completion rate American Indian or Alaska Native (150% expected time)',
                    'completion_rate_150_nhpi': 'Completion rate Native Hawaiian or Pacific Islander  (150% expected time)',
                    'completion_rate_150_multiracial': 'Completion rate 2 or More Races  (150% expected time)',
                    'completion_rate_150_unknown_race': 'Completion rate Race Unknown  (150% expected time)',
                }
                
                for field, csv_field in completion_fields.items():
                    value = row.get(csv_field)
                    if value and value.strip():
                        try:
                            university_data[field] = parse_percentage(value)
                        except (ValueError, TypeError):
                            pass
                
                # Add financial fields with proper type conversion
                financial_fields = {
                    'avg_annual_cost': 'Average annual cost',
                    'avg_net_price_0_30k': 'Average net price:  $0 - $30,000 family income',
                    'avg_net_price_30k_48k': 'Average net price: $30,001 - $48,000 family income',
                    'avg_net_price_48k_75k': 'Average net price: $48,001 - $75,000 family income',
                    'avg_net_price_75k_110k': 'Average net price: $75,001 - $110,000 family income',
                    'avg_net_price_110k_plus': 'Average net price: $110,000+ family income',
                    'pct_students_pell': '% Students awarded Pell grant',
                    'pct_students_federal_loan': '% Students with federal loan',
                    'median_debt_pell': 'Median debt Pell recipients',
                    'median_debt_firstgen': 'Median debt first-gen students',
                    'median_earnings_10yrs': 'Median earnings (10 years after entry)',
                    'earnings_25th_pctl_10yrs': '25th percentile earnings (10 years after entry)',
                    'earnings_75th_pctl_10yrs': '75th percentile earnings (10 years after entry)',
                }

                for field, csv_field in financial_fields.items():
                    value = row.get(csv_field)
                    if value and value.strip():
                        try:
                            if field.startswith('pct_'):
                                university_data[field] = parse_percentage(value)
                            else:
                                university_data[field] = float(value)
                        except (ValueError, TypeError):
                            pass
                
                # Generate formatted text for embedding
                university_text = format_university_text(row)
                batch_items.append((university_data, university_text))
                
                if len(batch_items) >= batch_size:
                    texts = [item[1] for item in batch_items]
                    embeddings = generate_embedding(texts)
                    for (uni_data, _), embedding in zip(batch_items, embeddings):
                        if embedding:
                            uni_data['embedding'] = embedding
                        University.objects.create(**uni_data)
                        processed_count += 1
                    batch_items = []

                if processed_count % 10 == 0 and processed_count > 0:
                    logger.info(f"Processed {processed_count} universities")
                    
            except Exception as e:
                error_count += 1
                institution_name = row.get('Institution', 'Unknown')
                raw_unitid = row.get('UNITID', 'None')
                logger.error(f"Error processing university {institution_name} with UNITID: {raw_unitid}: {e}")
    
    if batch_items:
        texts = [item[1] for item in batch_items]
        embeddings = generate_embedding(texts)
        for (uni_data, _), embedding in zip(batch_items, embeddings):
            if embedding:
                uni_data['embedding'] = embedding
            University.objects.create(**uni_data)
            processed_count += 1

    logger.info(f"University data migration completed. Processed: {processed_count}, Skipped: {skipped_count}, Errors: {error_count}")

class Migration(migrations.Migration):
    dependencies = [
        ('universities', '0001_initial'),
        ('universities', '0002_add_vector_support'),
    ]

    operations = [
        migrations.RunPython(load_university_data, migrations.RunPython.noop),
    ]