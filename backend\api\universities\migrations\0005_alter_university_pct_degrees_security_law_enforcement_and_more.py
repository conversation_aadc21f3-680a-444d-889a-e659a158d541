# Generated by Django 4.2.13 on 2025-06-13 15:53

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('universities', '0004_alter_university_embedding'),
    ]

    operations = [
        migrations.AlterField(
            model_name='university',
            name='pct_degrees_security_law_enforcement',
            field=models.FloatField(blank=True, help_text='Percentage of degrees awarded in Homeland Security, Law Enforcement, Firefighting & Related Protective Services', null=True),
        ),
        migrations.CreateModel(
            name='ShortlistItem',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('university', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shortlisted_by', to='universities.university')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='shortlist_items', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Shortlist Item',
                'verbose_name_plural': 'Shortlist Items',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'university')},
            },
        ),
    ]
