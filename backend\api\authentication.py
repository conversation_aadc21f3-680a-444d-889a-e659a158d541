from django.conf import settings
from rest_framework.authentication import BaseAuthentication
from rest_framework.exceptions import AuthenticationFailed

# Print the value of settings.API_KEY for debugging purposes

class APIKeyAuthentication(BaseAuthentication):
    def authenticate(self, request):
        api_key = request.headers.get('Authorization')
        if api_key == settings.API_KEY:
            return (None, None)  # No user associated with the API key
        raise AuthenticationFailed('Invalid API key')