"use client"

import { useState, useEffect } from "react"
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { getUseWaitlist } from '@/lib/utils'
import { useRouter } from 'next/navigation'


const WaitlistPage = () => {
    const router = useRouter();
    const [isLoading, setIsLoading] = useState(true); // Add a loading state
    const { user, getToken, updateAuthenticatedUser } = useAuth();

    useEffect(() => {
        const checkWaitlistStatus = async () => {
            try {
                const useWaitlist = await getUseWaitlist(getToken());
               
                if (!useWaitlist || !user?.profile?.is_waitlisted) {
                    router.push('/college-student/onboarding/background-check');
                } else {
                    setIsLoading(false); // Allow the page to render if not waitlisted
                }
            } catch (error) {
                console.error("Error checking waitlist status:", error);
                setIsLoading(false); // Allow the page to render even if there's an error
            }
        };

        if (user) {
            checkWaitlistStatus();
        }
    }, [user])

    if (isLoading) {
        return <div>Loading...</div>
    }


    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="py-10">
                    <StepProgressDisplay currentStep={6} totalSteps={6} />
                </div>
                <div className="py-10">
                    <h1 className="text-3xl font-semibold mb-8 mt-2">
                        Congratulations! You are now on our Trailblazer waitlist
                    </h1>
                    <p className="text-base md:text-lg text-gray-600 pb-12">
                        You'll receive an email from our team soon, welcoming you to the waitlist and sharing important next steps.
                        It will include details on paid opportunities to onboard schools and college access organizations from your community
                        so that you can start making an impact on your hometown even sooner.
                        <br />
                        <br />
                        We can't wait to welcome you to Trailblazer - we'll notify you as soon as we launch in your community!
                    </p>
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(WaitlistPage)