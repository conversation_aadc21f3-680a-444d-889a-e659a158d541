'use client';

import React from 'react';

export default function RecommendationsLoading() {
  return (
    <div className="relative flex min-h-screen items-center justify-center bg-[#f8faf8] px-4 py-8 font-sans text-gray-800">
      {/* Brand Accent */}
      <div className="absolute left-5 top-5 h-10 w-1 rounded bg-gradient-to-b from-green-400 to-green-500" />

      {/* Container */}
      <div className="max-w-xl rounded-2xl border border-green-100 bg-white px-10 py-16 text-center shadow-lg sm:px-6 sm:py-12">
        {/* Progress Bar */}
        <div className="relative mx-auto mb-12 h-1.5 w-60 overflow-hidden rounded bg-green-100">
          <div
            className="absolute left-0 top-0 h-full w-full rounded bg-gradient-to-r from-green-400 to-green-500"
            style={{
              animation: 'progress 2s ease-in-out infinite',
            }}
          />
        </div>

        {/* Text */}
        <h2
          className="mb-3 text-2xl font-semibold text-gray-800 opacity-0"
          style={{
            animation: 'fadeIn 1.5s ease-out 0.5s forwards',
          }}
        >
          Creating your recommendations
        </h2>
        <p
          className="text-sm text-gray-500 opacity-0"
          style={{
            animation: 'fadeIn 1.5s ease-out 1s forwards',
          }}
        >
          We're analyzing your profile to find colleges that might be a great fit for you
        </p>
      </div>

      {/* Inline Animations */}
      <style jsx>{`
        @keyframes progress {
          0% {
            width: 0%;
            transform: translateX(-100%);
          }
          50% {
            width: 100%;
            transform: translateX(0%);
          }
          100% {
            width: 100%;
            transform: translateX(100%);
          }
        }

        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(20px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }
      `}</style>
    </div>
  );
}
