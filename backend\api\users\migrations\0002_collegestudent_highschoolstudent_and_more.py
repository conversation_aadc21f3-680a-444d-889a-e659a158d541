# Generated by Django 4.2.13 on 2024-10-07 22:38

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0001_initial'),
        ('users', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='CollegeStudent',
            fields=[
                ('user_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('university', models.CharField(max_length=255)),
                ('interests', models.TextField()),
                ('high_school_name', models.CharField(max_length=255)),
                ('high_school_zip_code', models.CharField(max_length=10)),
                ('high_school_city', models.Char<PERSON>ield(max_length=100)),
                ('high_school_state', models.Char<PERSON><PERSON>(max_length=100)),
                ('college_major', models.<PERSON>r<PERSON><PERSON>(max_length=255)),
                ('college_tags', models.J<PERSON><PERSON>ield()),
            ],
            options={
                'verbose_name': 'College Student',
                'verbose_name_plural': 'College Students',
            },
            bases=('users.user',),
        ),
        migrations.CreateModel(
            name='HighSchoolStudent',
            fields=[
                ('user_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('grade_level', models.IntegerField()),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='high_school_students', to='organizations.organization')),
            ],
            options={
                'verbose_name': 'High School Student',
                'verbose_name_plural': 'High School Students',
            },
            bases=('users.user',),
        ),
        migrations.CreateModel(
            name='CounselorAdministrator',
            fields=[
                ('user_ptr', models.OneToOneField(auto_created=True, on_delete=django.db.models.deletion.CASCADE, parent_link=True, primary_key=True, serialize=False, to=settings.AUTH_USER_MODEL)),
                ('position', models.CharField(max_length=255)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='counselors_administrators', to='organizations.organization')),
            ],
            options={
                'verbose_name': 'Counselor/Administrator',
                'verbose_name_plural': 'Counselors/Administrators',
            },
            bases=('users.user',),
        ),
    ]
