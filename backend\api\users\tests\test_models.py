from django.test import TestCase
from api.users.models import User, CollegeStudentProfile, HighSchoolStudentProfile, CounselorAdministratorProfile, Availability
from api.organizations.models import Organization
from django.core.exceptions import ValidationError
from django.utils import timezone


class HighSchoolStudentProfileModelTest(TestCase):
    def setUp(self):
        self.organization = Organization.objects.create(
            name='Test Organization',
            zip_code='12345',
            city='Test City',
            state='Test State'
        )
        self.user = User.objects.create_user(
            first_name='<PERSON>',
            last_name='<PERSON>',
            email='<EMAIL>',
            password='Password123!',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE,
        )
        self.profile = self.user.profile
        self.profile.grade_level = 10
        self.profile.organization = self.organization
        
        # Set values for new fields
        self.profile.unweighted_gpa = 3.8
        self.profile.weighted_gpa = 4.2
        self.profile.sat_math_score = 750
        self.profile.sat_reading_score = 720
        self.profile.act_score = 32
        self.profile.high_school_name = "Lincoln High School"
        self.profile.graduation_year = 2025
        self.profile.intended_majors = ["Computer Science", "Mathematics"]
        self.profile.extracurriculars = ["Debate", "Chess Club"]
        self.profile.interests = ["Programming", "Reading"]
        self.profile.college_type_preferences = ["Public", "Private"]
        self.profile.location_type_preferences = ["Urban", "Suburban"]
        self.profile.geographic_preference_type = "states"
        self.profile.preferred_states = ["CA", "NY", "MA"]
        self.profile.preferred_zip = "94105"
        self.profile.preferred_radius = "0-20"
        self.profile.gender = "female"
        self.profile.ethnicity = "black"
        self.profile.household_income = "48k_75k"
        self.profile.financial_aid_need = "medium"
        
        self.profile.save()

    def test_high_school_student_profile_creation(self):
        self.assertIsInstance(self.profile, HighSchoolStudentProfile)
        self.assertEqual(self.profile.grade_level, 10)
        self.assertEqual(self.profile.organization, self.organization)
        
        # Test the new academic fields
        self.assertEqual(self.profile.unweighted_gpa, 3.8)
        self.assertEqual(self.profile.weighted_gpa, 4.2)
        self.assertEqual(self.profile.sat_math_score, 750)
        self.assertEqual(self.profile.sat_reading_score, 720)
        self.assertEqual(self.profile.act_score, 32)
        self.assertEqual(self.profile.high_school_name, "Lincoln High School")
        self.assertEqual(self.profile.graduation_year, 2025)
        
        # Test JSON fields
        self.assertEqual(len(self.profile.intended_majors), 2)
        self.assertIn("Computer Science", self.profile.intended_majors)
        self.assertEqual(len(self.profile.extracurriculars), 2)
        self.assertIn("Debate", self.profile.extracurriculars)
        self.assertEqual(len(self.profile.interests), 2)
        self.assertIn("Programming", self.profile.interests)
        
        # Test preference fields
        self.assertEqual(len(self.profile.college_type_preferences), 2)
        self.assertIn("Public", self.profile.college_type_preferences)
        self.assertEqual(len(self.profile.location_type_preferences), 2)
        self.assertIn("Urban", self.profile.location_type_preferences)
        
        # Test geographic preference fields
        self.assertEqual(self.profile.geographic_preference_type, "states")
        self.assertEqual(len(self.profile.preferred_states), 3)
        self.assertIn("CA", self.profile.preferred_states)
        self.assertEqual(self.profile.preferred_zip, "94105")
        self.assertEqual(self.profile.preferred_radius, "0-20")
        
        # Test demographic fields
        self.assertEqual(self.profile.gender, "female")
        self.assertEqual(self.profile.ethnicity, "black")
        self.assertEqual(self.profile.household_income, "48k_75k")
        self.assertEqual(self.profile.financial_aid_need, "medium")
        
    def test_null_field_values(self):
        """Test that fields can have null values"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        profile = user.high_school_student_profile
        
        # All new fields should have null/default values
        self.assertIsNone(profile.unweighted_gpa)
        self.assertIsNone(profile.weighted_gpa)
        self.assertIsNone(profile.sat_math_score)
        self.assertIsNone(profile.sat_reading_score)
        self.assertIsNone(profile.act_score)
        self.assertIsNone(profile.high_school_name)
        self.assertIsNone(profile.graduation_year)
        self.assertEqual(profile.intended_majors, [])
        self.assertEqual(profile.extracurriculars, None)
        self.assertEqual(profile.interests, None)
        self.assertEqual(profile.college_type_preferences, [])
        self.assertEqual(profile.location_type_preferences, [])
        self.assertEqual(profile.geographic_preference_type, "states")
        self.assertEqual(profile.preferred_states, [])
        self.assertIsNone(profile.preferred_zip)
        self.assertIsNone(profile.preferred_radius)
        self.assertIsNone(profile.gender)
        self.assertIsNone(profile.ethnicity)
        self.assertIsNone(profile.household_income)
        self.assertIsNone(profile.financial_aid_need)
    
    def test_field_choices(self):
        """Test that choice fields only accept valid values"""
        # Test gender choices
        self.profile.gender = "Invalid Gender"
        with self.assertRaises(ValidationError):
            self.profile.full_clean()
        
        # Reset to valid value
        self.profile.gender = "female"
        
        # Test ethnicity choices
        self.profile.ethnicity = "Invalid Ethnicity"
        with self.assertRaises(ValidationError):
            self.profile.full_clean()
            
        # Reset to valid value
        self.profile.ethnicity = "black"
        
        # Test household_income choices
        self.profile.household_income = "Invalid Income"
        with self.assertRaises(ValidationError):
            self.profile.full_clean()
            
        # Reset to valid value
        self.profile.household_income = "48k_75k"
        
        # Test financial_aid_need choices
        self.profile.financial_aid_need = "Invalid Need"
        with self.assertRaises(ValidationError):
            self.profile.full_clean()
            
        # Reset to valid value
        self.profile.financial_aid_need = "medium"
        
        # Test geographic_preference_type choices
        self.profile.geographic_preference_type = "Invalid Type"
        with self.assertRaises(ValidationError):
            self.profile.full_clean()
        
        # Should pass validation with all valid values
        self.profile.geographic_preference_type = "states"
        self.profile.full_clean()

    def test_recommendations_status_field_default(self):
        """Test that the recommendations_status field has the correct default value."""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        profile = user.high_school_student_profile
        self.assertEqual(profile.recommendations_status, 'not_requested')

    def test_recommendations_status_field_choices(self):
        """Test that the recommendations_status field only accepts valid values."""
        self.profile.recommendations_status = 'invalid_status'
        with self.assertRaises(ValidationError):
            self.profile.full_clean()
        
        # Reset to valid values and verify no errors
        for status in ['not_requested', 'pending', 'ready']:
            self.profile.recommendations_status = status
            try:
                self.profile.full_clean()
            except ValidationError:
                self.fail(f"recommendations_status '{status}' should be valid")


class AvailabilityModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!'
        )
        self.availability = Availability.objects.create(
            user=self.user,
            time_zone='UTC',
            monday_available=True,
            tuesday_available=False,
            wednesday_available=True,
            monday_time_ranges=[
                {'start_time': '09:00', 'end_time': '12:00'},
                {'start_time': '13:00', 'end_time': '17:00'}
            ],
            wednesday_time_ranges=[
                {'start_time': '10:00', 'end_time': '15:00'}
            ]
        )

    def test_availability_creation(self):
        self.assertEqual(self.availability.user, self.user)
        self.assertEqual(self.availability.time_zone, 'UTC')
        self.assertTrue(self.availability.monday_available)
        self.assertFalse(self.availability.tuesday_available)
        self.assertEqual(len(self.availability.monday_time_ranges), 2)

    def test_overlapping_time_ranges(self):
        with self.assertRaises(ValidationError):
            availability = Availability(
                user=self.user,
                time_zone='UTC',
                monday_available=True,
                monday_time_ranges=[
                    {'start_time': '09:00', 'end_time': '12:00'},
                    {'start_time': '11:00', 'end_time': '13:00'}
                ]
            )
            availability.clean()

    def test_one_to_one_relationship(self):
        with self.assertRaises(Exception):
            # Attempt to create a second Availability instance for the same user should fail
            Availability.objects.create(user=self.user, time_zone='PST')


class CollegeStudentProfileModelTest(TestCase):
    def setUp(self):
        self.user = User.objects.create_user(
            first_name='John',
            last_name='Doe',
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.profile = self.user.profile
        self.profile.university = 'Test University'
        self.profile.interests = 'Computer Science'
        self.profile.high_school_name = 'Test High School'
        self.profile.high_school_zip_code = '12345'
        self.profile.high_school_city = 'Test City'
        self.profile.high_school_state = 'Test State'
        self.profile.college_major = 'Engineering'
        self.profile.college_tags = {'type': 'public'}
        self.profile.save()

    def test_college_student_profile_creation(self):
        self.assertIsInstance(self.profile, CollegeStudentProfile)
        self.assertEqual(self.profile.university, 'Test University')
        self.assertEqual(self.profile.college_tags['type'], 'public')

    def test_background_check_passed_at_default(self):
        self.assertIsNone(self.profile.background_check_passed_at)

    def test_background_check_passed_at_set(self):
        now = timezone.now()
        self.profile.background_check_passed_at = now
        self.profile.save()
        self.assertEqual(self.profile.background_check_passed_at, now)


class CounselorAdministratorProfileModelTest(TestCase):
    def setUp(self):
        self.organization = Organization.objects.create(
            name='Test Organization',
            zip_code='12345',
            city='Test City',
            state='Test State',
        )
        self.user = User.objects.create_user(
            first_name='Alice',
            last_name='Smith',
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COUNSELOR_ADMINISTRATOR_TYPE,
        )
        self.profile = self.user.profile
        self.profile.position = 'Counselor'
        self.profile.organization = self.organization
        self.profile.save()

    def test_counselor_administrator_profile_creation(self):
        self.assertIsInstance(self.profile, CounselorAdministratorProfile)
        self.assertEqual(self.profile.position, 'Counselor')
        self.assertEqual(self.profile.organization, self.organization)