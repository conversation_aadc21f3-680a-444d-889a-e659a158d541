import { useState, useEffect, useRef, useMemo } from 'react';
import { useAuth } from '@/context/AuthProvider';
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { BookingCard } from "@/components/ui/BookingCard";


const BookingsList = ({ user, userLoading, userError }) => {
    const [bookings, setBookings] = useState([]);
    const [loadingBookings, setLoadingBookings] = useState(true);
    const [error, setError] = useState(null);
    const { getToken } = useAuth()
    const bookingsFetched = useRef(false);

    const upcomingBookings = useMemo(() => bookings.filter(booking => !booking.is_historical), [bookings]);
    const historyBookings = useMemo(() => bookings.filter(booking => booking.is_historical), [bookings]);

    useEffect(() => {
        if (userLoading || userError) return;
        
        const fetchBookings = async () => {
            if (bookingsFetched.current) return;
            bookingsFetched.current = true;
            const fetchUrl = user.user_type === 'CollegeStudent'
                ? `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/trailblazer/`
                : `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/bookings/`;
            try {
                const authToken = getToken();
                    const response = await fetch(fetchUrl, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${authToken}`
                    }
                });
                if (!response.ok) {
                    throw new Error('Failed to fetch bookings');
                }
                const data = await response.json();
                setBookings(data.results);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoadingBookings(false);
            }
        };
        fetchBookings();
    }, [userLoading, userError]);

    const setBookingStatus = async (bookingId, newStatus) => {
        setBookings(bookings.map(booking => {
            if (booking.id === bookingId) {
                return {
                    ...booking,
                    status: newStatus
                };
            }
            return booking;
        }));
    };

    const setTrailblazerStatus = async (bookingId, newStatus, meetLink) => {
        setBookings(bookings.map(booking => {
            if (booking.id === bookingId) {
                return {
                    ...booking,
                    current_trailblazer_status: newStatus,
                    meet_link: meetLink
                };
            }
            return booking;
        }));
    };

    if (loadingBookings) {
        return <p>Loading...</p>;
    }

    if (error) {
        return <p className="text-red-500">Error: {error}</p>;
    }

    return (
        <div className="w-full">
            <Tabs defaultValue="upcoming" className="w-full">
                <TabsList className="mb-8 p-0 bg-transparent">
                    <TabsTrigger value="upcoming" className="mr-4 px-4 py-2 text-sm font-medium border text-foreground data-[state=active]:bg-[#ADFFC2] data-[state=active]:border-[#BFBFBF]">
                        Upcoming
                    </TabsTrigger>
                    <TabsTrigger value="history" className="px-4 py-2 text-sm font-medium border text-foreground data-[state=active]:bg-[#ADFFC2] data-[state=active]:border-[#BFBFBF]">
                        History
                    </TabsTrigger>
                </TabsList>
                <TabsContent value="upcoming">
                    {upcomingBookings.length > 0 ? (
                        upcomingBookings.map((booking, index) => (
                            <BookingCard
                                key={`${booking.id}${booking.status}`}
                                booking={booking}
                                user={user}
                                setBookingStatus={setBookingStatus}
                                setTrailblazerStatus={setTrailblazerStatus}
                            />
                        ))
                    ) : (
                        <p>No upcoming bookings available.</p>
                    )}
                </TabsContent>
                <TabsContent value="history">
                    {historyBookings.length > 0 ? (
                        historyBookings.map((booking, index) => (
                            <BookingCard
                                key={booking.id}
                                booking={booking}
                                user={user}
                                setBookingStatus={setBookingStatus}
                                setTrailblazerStatus={setTrailblazerStatus}
                            />
                        ))
                    ) : (
                        <p>No historical bookings available.</p>
                    )}
                </TabsContent>
            </Tabs>
        </div>
    );
};

export default BookingsList;
