"use client"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { AvailabilityForm } from "@/components/ui/availabilityForm"
import { useState, useEffect } from "react"
import { MainLayout } from "@/components/ui/mainLayout"
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { parseAvailabilityApiData, formatAvailabilityDataForApi } from "@/lib/availabiltyUtils"
import { Loader2 } from 'lucide-react'


// Main page component
const MainContent = ({ user, userLoading, userError }) => {
    const [availability, setAvailability] = useState(null)
    const [loading, setLoading] = useState(true)
    const [updateLoading, setUpdateLoading] = useState(false)
    const { toast } = useToast()
    const { getToken } = useAuth()

    useEffect(() => {
        const fetchAvailability = async () => {
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/availabilities/current/`, {
                    headers: {
                        'Authorization': `Token ${getToken()}`
                    }
                })
                if (!response.ok) {
                    throw new Error('Failed to fetch availability')
                }
                const data = await response.json()
                const formattedData = parseAvailabilityApiData(data)
                setAvailability(formattedData)
            } catch (error) {
                toast({ 
                    variant: 'destructive',
                    description: 'Error fetching availability'
                })
            } finally {
                setLoading(false)
            }
        }
        fetchAvailability()
    }, [])

    const onSubmit = async (formData) => {
        setUpdateLoading(true)
        // Format the data to match the API schema
        const formattedData = formatAvailabilityDataForApi(formData)
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/availabilities/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${getToken()}`
                },
                body: JSON.stringify(formattedData),
            })
            const result = await response.json()
            if (response.ok) {
                toast({ 
                    title: 'Success!', 
                    description: 'Availability set successfully' 
                })
                // Optionally navigate or update UI
            } else {
                toast({
                    variant: 'destructive',
                    description: result.detail || 'Failed to set availability' 
                })
            }
        } catch (error) {
            toast({
                variant: 'destructive',
                description: `An unexpected error occurred: ${error}` 
            })
        }
        setUpdateLoading(false)
    }

    return (
        <div className="flex-grow container">
            {   
                loading || userLoading ? (
                    <p>Loading...</p>
                ) : (
                    <AvailabilityForm 
                        initialData={availability}
                        onSubmit={onSubmit}
                        submitButton={(
                            <div className="flex justify-between items-center mb-8">
                                <h1 className="text-2xl md:text-3xl font-bold text-left">Availability</h1>
                                <Button
                                    type="submit"
                                    variant="outline"
                                    className="border border-gray-800 text-gray-800"
                                    disabled={updateLoading}
                                >
                                    {updateLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Save Changes
                                </Button>
                            </div>
                        )}
                        submitPosition="top"
                    />
                )
            }
        </div>
    )
}

// Page component
const AvailabilityPage = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()

    return (
        <MainLayout user={user} displaySidebarMenu={true}>
            <MainContent user={user} userLoading={userLoading} userError={userError} />
        </MainLayout>
    );
};

export default withAuth(AvailabilityPage)
