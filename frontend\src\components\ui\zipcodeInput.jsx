"use client"

import { useEffect } from 'react'
import { Input } from "@/components/ui/input"
import zipcodes from 'zipcodes'


const stateMapping = {
    AL: "Alabama",
    AK: "Alaska",
    AZ: "Arizona",
    AR: "Arkansas",
    CA: "California",
    CO: "Colorado",
    CT: "Connecticut",
    DE: "Delaware",
    FL: "Florida",
    GA: "Georgia",
    HI: "Hawaii",
    ID: "Idaho",
    IL: "Illinois",
    IN: "Indiana",
    IA: "Iowa",
    KS: "Kansas",
    KY: "Kentucky",
    LA: "Louisiana",
    ME: "Maine",
    MD: "Maryland",
    MA: "Massachusetts",
    MI: "Michigan",
    MN: "Minnesota",
    MS: "Mississippi",
    MO: "Missouri",
    MT: "Montana",
    NE: "Nebraska",
    NV: "Nevada",
    NH: "New Hampshire",
    NJ: "New Jersey",
    NM: "New Mexico",
    NY: "New York",
    NC: "North Carolina",
    ND: "North Dakota",
    OH: "Ohio",
    OK: "Oklahoma",
    OR: "Oregon",
    PA: "Pennsylvania",
    RI: "Rhode Island",
    SC: "South Carolina",
    SD: "South Dakota",
    TN: "Tennessee",
    TX: "Texas",
    UT: "Utah",
    VT: "Vermont",
    VA: "Virginia",
    WA: "Washington",
    WV: "West Virginia",
    WI: "Wisconsin",
    WY: "Wyoming",
  };

// Function to lookup zip code using the 'zipcodes' library
export const lookupZipCode = async (zipCode) => {
    const result = zipcodes.lookup(zipCode)
    if (result) {
        return {
            city: result.city,
            state: stateMapping[result.state] ? stateMapping[result.state] : result.state
        }
    }
    return null
}

// ZipCodeInput component
export const ZipCodeInput = (props) => {
    const { field, form, onZipMatch, onZipError } = props
    const fieldName = field.name

    useEffect(() => {
        const zipCode = form.watch(fieldName)
        if (!zipCode) {
            return
        }
        onZipError(null)
        if (zipCode.length === 5) {
            lookupZipCode(zipCode).then(result => {
                if (result) {
                    onZipMatch(result)
                } else {
                    onZipError("Invalid zip code")
                }
            }).catch(() => {
                onZipError("Invalid zip code")
            })
        }
    }, [form.watch(fieldName)])
    
    return (
        <Input placeholder="Enter zip code" {...field} {...props} />
    )
}