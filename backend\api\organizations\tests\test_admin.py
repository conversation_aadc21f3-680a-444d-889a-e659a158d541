from django.contrib.admin.sites import AdminSite
from django.test import TestCase
from api.organizations.admin import OrganizationAdmin
from api.organizations.models import Organization
from django.contrib.auth import get_user_model

User = get_user_model()

class MockRequest:
    pass

class OrganizationAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin = OrganizationAdmin(Organization, self.site)
        self.organization1 = Organization.objects.create(
            name='Alpha Org',
            zip_code='11111',
            city='Alpha City',
            state='Alpha State',
            registration_number='REG111',
            contact_email='<EMAIL>',
            contact_phone='************'
        )
        self.organization2 = Organization.objects.create(
            name='Beta Org',
            zip_code='22222',
            city='Beta City',
            state='Beta State',
            registration_number='REG222',
            contact_email='<EMAIL>',
            contact_phone='************'
        )

    def test_search_fields_include_added_fields(self):
        expected_fields = ('name', 'city', 'state', 'registration_number', 'zip_code', 'contact_email', 'contact_phone')
        self.assertEqual(self.admin.search_fields, expected_fields)

    def test_list_display_includes_added_fields(self):
        expected_display = ('name', 'city', 'state', 'zip_code', 'registration_number', 'created_at', 'updated_at')
        self.assertEqual(self.admin.list_display, expected_display)

    def test_ordering_includes_added_fields(self):
        expected_ordering = ('name', 'created_at', 'updated_at')
        self.assertEqual(self.admin.ordering, expected_ordering)

    def test_admin_search_functionality(self):
        request = MockRequest()
        queryset = self.admin.get_queryset(request)
        result = self.admin.get_search_results(request, queryset, 'Alpha')
        self.assertIn(self.organization1, result[0])
        self.assertNotIn(self.organization2, result[0])

    def test_admin_ordering_functionality(self):
        request = MockRequest()
        self.admin.ordering = ('-created_at',)
        queryset = self.admin.get_queryset(request).order_by(*self.admin.ordering)
        self.assertEqual(list(queryset), [self.organization2, self.organization1])
