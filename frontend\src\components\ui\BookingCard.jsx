"use client"

import { useState, useMemo, useEffect } from 'react'
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Check, X, Calendar, Mail, CircleCheck, CircleX, Loader2 } from 'lucide-react'
import CancelSessionDialog from "./CancelSessionDialog"
import { useToast } from '@/hooks/use-toast'
import { useAuth } from '@/context/AuthProvider';
import ConfirmBookingChangeDialog from './ConfirmBookingChangeDiaolog'
import { Trash } from 'lucide-react';

const ApproveButton = ({ onClick, loading }) => (
    <Button
        className="font-semibold px-2 md:px-4"
        onClick={onClick}
        disabled={loading}
    >
        {loading ? <Loader2 className="mr-2 w-4 h-4 md:w-6 md:h-6 animate-spin" /> : <Check className="mr-2 w-4 h-4 md:w-6 md:h-6" />}Approve
    </Button>
)

const DeclineButton = ({ onClick, loading }) => (
    <Button
        variant="outline"
        className="font-semibold px-2 md:px-4 border border-black"
        onClick={onClick}
        disabled={loading}
    >
        {loading ? <Loader2 className="mr-2 w-4 h-4 md:w-6 md:h-6 animate-spin" /> : <X className="mr-2 w-4 h-4 md:w-6 md:h-6" />}Decline
    </Button>
)

const VideoCallButton = ({ onClick }) => (
    <Button
        variant="outline"
        className="font-semibold px-2 md:px-4 border border-black"
        onClick={onClick}
    >
        <Calendar className="mr-2 w-4 h-4 md:w-6 md:h-6" />Video Call
    </Button>
)

const EmailButton = ({ onClick }) => (
    <Button
        variant="outline"
        className="font-semibold px-2 md:px-4 border border-black"
        onClick={onClick}
    >
        <Mail className="mr-2 w-4 h-4 md:w-6 md:h-6" />Email
    </Button>
)

const CancelButton = ({ onClick, loading }) => (
    <Button
        variant="outline"
        className="font-semibold px-2 md:px-4 border border-black"
        onClick={onClick}
        disabled={loading}
    >
        {loading ? <Loader2 className="mr-2 w-4 h-4 md:w-6 md:h-6 animate-spin" /> : <X className="mr-2 w-4 h-4 md:w-6 md:h-6" />}Cancel
    </Button>
)

export const BookingCard = ({ booking, user, setBookingStatus, setTrailblazerStatus }) => {
    const { toast } = useToast()
    const [isCancelDialogOpen, setIsCancelDialogOpen] = useState(false)
    const [isDeclineDialogOpen, setIsDeclineDialogOpen] = useState(false)
    const [loadingCancel, setLoadingCancel] = useState(false)
    const [loadingDecline, setLoadingDecline] = useState(false)
    const [loadingConfirm, setLoadingConfirm] = useState(false)
    const [loadingConfirmedProposedTime, setLoadingConfirmedProposedTime] = useState(false)
    const { getToken } = useAuth()

    const handleConfirm = () => handleUpdateStatus(booking.id, 'confirmed')
    const handleCancel = (formData) => handleUpdateStatus(booking.id, 'cancelled', formData)
    const handleDecline = (formData) => handleUpdateStatus(booking.id, 'declined', formData)

    const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
    const [selectedProposedTime, setSelectedProposedTime] = useState(null);
    const [proposedTimeConfirmed, setProposedTimeConfirmed] = useState(booking.proposed_time_confirmed);
    
    useEffect(() => {
        setProposedTimeConfirmed(booking.proposed_time_confirmed);
    },[booking.proposed_time_confirmed]);
    
    function convertLocalToUTC(dateString, timeString) {
        // Function to parse the time string (e.g., "21:04")

        function parseTimeString(timeString) {
            const [hours, minutes] = timeString.split(':').map(Number);
            return { hours, minutes };
        }

        // Parse the date string (e.g., "2025-03-07")
        const [year, month, day] = dateString.split('-').map(Number);

        // Parse the time string
        const { hours, minutes } = parseTimeString(timeString);

        // Combine date and time into a Date object in local time
        const dateWithTime = new Date(year, month - 1, day, hours, minutes);

        // Convert to UTC datetime string
        return dateWithTime.toISOString();
    }

    const convertUTCToLocal = (utcString) => {
        const date = new Date(utcString);
        const options = {
            weekday: 'short',
            month: 'short',
            day: 'numeric',
            hour: 'numeric',
            minute: 'numeric',
            hour12: true
        };

        // Format the date part
        const datePart = date.toLocaleDateString('en-US', {
            weekday: 'short',
            month: 'short',
            day: 'numeric'
        }).replace(',', ''); // Remove the comma after the day

        // Format the time part
        const timePart = date.toLocaleTimeString('en-US', {
            hour: 'numeric',
            minute: 'numeric',
            hour12: true
        });

        return `${datePart} ${timePart}`;
    };

    const handleUpdateStatus = (bookingId, status, formData = null) => {
        if (user.user_type === 'CollegeStudent') {
            updateTrailblazerStatus(bookingId, status, formData);
            if (formData && formData.proposedTimes) {
                const proposedTimes = formData.proposedTimes
                    .filter(({ date, time }) => date !== "" && time !== "")
                    .map(({ date, time }) => ({
                        start_time: convertLocalToUTC(date, time)
                    }));
                updateBookingWithProposedTimes(bookingId, proposedTimes);
            }
        } else {
            updateBookingStatus(bookingId, status, formData);
        }
    };


    const updateBookingWithProposedTimes = async (bookingId, proposedTimes) => {
        try {
            const authToken = getToken();
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/trailblazer/${bookingId}/propose_times/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`
                },
                body: JSON.stringify({ proposed_times: proposedTimes })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to update proposed times');
            }

            const updatedBooking = await response.json();
            setBookingStatus(bookingId, updatedBooking.status);
            toast({ title: 'Success!', description: 'Proposed times updated successfully' });
        } catch (error) {
            toast({ variant: 'destructive', description: error.message });
        }
    };

    const setLoading = (status, loading) => {
        if (status === 'confirmed') setLoadingConfirm(loading)
        if (status === 'cancelled') setLoadingCancel(loading)
        if (status === 'declined') setLoadingDecline(loading)
    }

    const closeDialog = (status) => {
        if (status === 'cancelled') setIsCancelDialogOpen(false)
        if (status === 'declined') setIsDeclineDialogOpen(false)
    }

    const handleJoinVideoCall = () => {
        window.open(booking.meet_link, '_blank')
    }

    const handleMailTo = () => {
        // Extract the emails from the trailblazers
        const recipients = booking.trailblazers.map(trailblazer => trailblazer.email);
        // Join emails into a single string separated by commas
        const recipientEmails = recipients.join(',');
        // Construct the mailto link with only recipients
        const mailtoLink = `mailto:${recipientEmails}`;
        // Open the mailto link in a new tab
        window.open(mailtoLink, '_blank');
    };

    const updateTrailblazerStatus = async (bookingId, status, formData = null) => {
        if (!booking.id || !['confirmed', 'cancelled', 'declined'].includes(status)) {
            toast({ variant: 'destructive', description: 'Invalid booking ID or status' })
            return
        }
        const body = {
            status: status
        }
        if (status == 'declined' && formData && formData.cancelReason) {
            body.decline_reason = formData.cancelReason
        }
        try {
            setLoading(status, true)
            const authToken = getToken()
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/trailblazer/${bookingId}/update_status/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`
                },
                body: JSON.stringify(body)
            })
            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.detail || 'Failed to update status')
            }
            const updatedBooking = await response.json()
            setTrailblazerStatus(booking.id, updatedBooking.status, updatedBooking.meet_link)
            toast({ title: 'Success!', description: 'Session status updated successfully' })
            setLoading(status, false)
            closeDialog(status)
        } catch (error) {
            toast({ variant: 'destructive', description: error.message })
            setLoading(status, false)
        }
    }

    const updateBookingStatus = async (bookingId, status, formData = null) => {
        if (!booking.id || !['confirmed', 'cancelled'].includes(status)) {
            toast({ variant: 'destructive', description: 'Invalid booking ID or status' })
            return
        }
        const body = {
            creator_status: status
        }
        if (status == 'cancelled' && formData && formData.cancelReason) {
            body.creator_cancel_reason = formData.cancelReason
        }
        try {
            setLoading(status, true)
            const authToken = getToken()
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/bookings/${bookingId}/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`
                },
                body: JSON.stringify(body)
            })
            if (!response.ok) {
                const errorData = await response.json()
                throw new Error(errorData.detail || 'Failed to update status')
            }
            const updatedBooking = await response.json()
            setBookingStatus(booking.id, updatedBooking.status)
            toast({ title: 'Success!', description: 'Session status updated successfully' })
            setLoading(status, false)
            closeDialog(status)
        } catch (error) {
            toast({ variant: 'destructive', description: error.message })
            setLoading(status, false)
        }
    }

    const bookingNames = useMemo(() => {
        if (!booking) return ''
        if (user.user_type === 'CollegeStudent') {
            return `${booking.booked_by.first_name} ${booking.booked_by.last_name}`
        }
        const trailblazers = booking.trailblazers.map(trailblazer => {
            return {
                ...trailblazer,
                name: `${trailblazer.first_name} ${trailblazer.last_name}`
            }
        })
        let trailblazerNames = ''
        if (trailblazers.length === 1) {
            trailblazerNames = trailblazers[0].name
        }
        if (trailblazers.length === 2) {
            trailblazerNames = `${trailblazers[0].name} and ${trailblazers[1].name}`
        }
        if (trailblazers.length > 2) {
            trailblazerNames = trailblazers.slice(0, -1).map(trailblazer => trailblazer.name).join(', ') + ` and ${trailblazers[trailblazers.length - 1].name}`
        }
        return trailblazerNames
    }, [booking, user])

    const bookingTitle = useMemo(() => {
        if (!booking || !bookingNames) return ''
        return `Meeting with ${bookingNames}`
    }, [booking, user, bookingNames])

    const BookingSubtitle = useMemo(() => {
        if (!booking) return ''
        if (user.user_type === 'CollegeStudent') {
            if (booking.booked_by.user_type === 'CounselorAdministrator') {
                return `Counselor or Administrator • ${booking.booked_by.profile.organization.name}`
            }
            if (booking.booked_by.user_type === 'HighSchoolStudent') {
                return `High School Student • ${booking.booked_by.profile.organization.name}`
            }
        }
        const trailblazerUniversities = booking.trailblazers.map(trailblazer => trailblazer.profile?.university).join(' / ')
        const trailblazerMajors = booking.trailblazers.map(trailblazer => trailblazer.profile?.college_major).join(' / ')
        const trailblazerHometowns = booking.trailblazers.map(trailblazer => `${trailblazer.profile?.high_school_city}, ${trailblazer.profile?.high_school_state}`).join(' / ')
        return `${trailblazerUniversities} • ${trailblazerMajors} • ${trailblazerHometowns}`
    }, [booking, user])

    const bookingDatetime = useMemo(() => {
        if (!booking) return {
            date: '',
            startTime: '',
            endTime: ''
        }
        const startDate = new Date(booking.start_time);
        const endDate = new Date(booking.end_time);
        return {
            date: startDate.toLocaleDateString('en-US', { weekday: 'short', month: 'short', day: 'numeric' }),
            startTime: startDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true }),
            endTime: endDate.toLocaleTimeString('en-US', { hour: 'numeric', minute: 'numeric', hour12: true })
        }
    }, [booking])

    const bookingStatus = useMemo(() => {
        if (!booking) return {
            value: '',
            color: '',
            borderColor: '',
            verbose: ''
        }
        const statuses = {
            declined: {
                value: 'declined',
                verbose: 'Declined',
                color: '#FFD1A9',
                borderColor: '#F57C00'
            },
            cancelled: {
                value: 'cancelled',
                verbose: 'Canceled',
                color: '#FFD1A9',
                borderColor: '#F57C00'
            },
            confirmed: {
                value: 'confirmed',
                verbose: 'Confirmed',
                color: '#ADFFC2',
                borderColor: '#36CE5D'
            },
            pending: {
                value: 'pending',
                verbose: 'Pending',
                color: '#DFDEDE',
                borderColor: '#BFBFBF'
            }
        }
        if (user.user_type === 'CollegeStudent') {
            return statuses[booking.current_trailblazer_status]
        }
        // if it is not college student, use booking status to set the status of the booking
        return statuses[booking.status]
    }, [booking, user])

    const bookingIsGroupSession = useMemo(() => {
        if (!booking) return false
        return booking.trailblazers.length > 1
    }, [booking])

    const handleSelectProposedTime = (time) => {
        setSelectedProposedTime(time);
        setIsConfirmDialogOpen(true);
    };

    const handleConfirmProposedTime = async () => {
        if (!selectedProposedTime) return;
        try {
            
            setLoadingConfirmedProposedTime(true);
            const authToken = getToken();

            // Call the new combined endpoint
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/bookings/${booking.id}/confirm_proposed_time/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`
                },
                body: JSON.stringify({
                    start_time: selectedProposedTime.start_time,
                    creator_timezone: Intl.DateTimeFormat().resolvedOptions().timeZone
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to confirm proposed time');
            }

            const updatedBooking = await response.json();
            setTrailblazerStatus(updatedBooking.id, updatedBooking.status, updatedBooking.meet_link);
            setProposedTimeConfirmed(true);
            setLoadingConfirmedProposedTime(false);
            toast({ title: 'Success!', description: 'New booking created successfully' });
            
            setIsConfirmDialogOpen(false);
        } catch (error) {
            toast({ variant: 'destructive', description: error.message });
            
        }
    };

    // Function to check if a proposed time has passed
    const isTimeInPast = (utcTime) => {
        const userTimeZone = Intl.DateTimeFormat().resolvedOptions().timeZone;
        const localTime = new Date(utcTime).toLocaleString("en-US", { timeZone: userTimeZone });
        return new Date(localTime) < new Date();
    };

    // Function to remove a proposed time
    const handleRemoveProposedTime = async (bookingId, timeToRemove) => {
        try {
            const authToken = getToken();
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/trailblazer/${bookingId}/remove_proposed_time/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`
                },
                body: JSON.stringify({ start_time: timeToRemove })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.detail || 'Failed to remove proposed time');
            }

            const updatedBooking = await response.json();
            setBookingStatus(bookingId, updatedBooking.status);
            toast({ title: 'Success!', description: 'Proposed time removed successfully' });
        } catch (error) {
            toast({ variant: 'destructive', description: error.message });
        }
    };


    return (
        <Card className="bg-white rounded-lg shadow mb-4">
            <CardContent className="p-8 flex flex-col lg3:flex-row gap-8">
                <div className="w-full md:w-44 md:flex-shrink-0 text-base flex flex-col justify-center items-center">
                    <div className="flex flex-col text-left">
                        <span>{bookingDatetime.date}</span>
                        <span>{bookingDatetime.startTime} - {bookingDatetime.endTime}</span>
                    </div>
                </div>
                <div className="flex-grow">
                    {!booking.is_historical && (
                        <div className="flex flex-wrap lg3:flex-nowrap flex-col lg3:flex-row items-start mb-4">
                            <Badge
                                className="pt-1 px-4 border text-foreground text-sm"
                                style={{
                                    backgroundColor: bookingStatus.color,
                                    borderColor: bookingStatus.borderColor
                                }}
                            >
                                {bookingStatus.verbose}
                            </Badge>
                            <div className="flex flex-row flex-grow justify-end items-start gap-2 lg3:gap-4 mt-4 lg3:mt-0">
                                {user?.user_type === 'CollegeStudent' && (
                                    <>
                                        {bookingStatus.value === 'pending' && (
                                            <>
                                                <DeclineButton
                                                    onClick={() => setIsDeclineDialogOpen(true)}
                                                    loading={loadingDecline}
                                                />
                                                <ApproveButton
                                                    onClick={handleConfirm}
                                                    loading={loadingConfirm}
                                                />
                                            </>
                                        )}
                                        {bookingStatus.value === 'confirmed' && (
                                            <>
                                                <VideoCallButton
                                                    onClick={() => handleJoinVideoCall()}
                                                />
                                                <DeclineButton
                                                    onClick={() => setIsDeclineDialogOpen(true)}
                                                    loading={loadingDecline}
                                                />
                                            </>
                                        )}
                                    </>
                                )}
                                {user?.user_type === 'CounselorAdministrator' && (
                                    <>
                                        {bookingStatus.value === 'pending' && (
                                            <>
                                                <CancelButton
                                                    onClick={() => setIsCancelDialogOpen(true)}
                                                    loading={loadingCancel}
                                                />
                                            </>
                                        )}
                                        {bookingStatus.value === 'confirmed' && (
                                            <>
                                                <VideoCallButton
                                                    onClick={() => handleJoinVideoCall()}
                                                />
                                                <EmailButton
                                                    onClick={() => handleMailTo()}
                                                />
                                                <CancelButton
                                                    onClick={() => setIsCancelDialogOpen(true)}
                                                    loading={loadingCancel}
                                                />
                                            </>
                                        )}
                                    </>
                                )}
                                {user?.user_type === 'HighSchoolStudent' && (
                                    <>
                                        {bookingStatus.value === 'pending' && (
                                            <>
                                                <CancelButton
                                                    onClick={() => setIsCancelDialogOpen(true)}
                                                    loading={loadingCancel}
                                                />
                                            </>
                                        )}
                                        {bookingStatus.value === 'confirmed' && (
                                            <>
                                                <VideoCallButton
                                                    onClick={() => handleJoinVideoCall()}
                                                />
                                                <CancelButton
                                                    onClick={() => setIsCancelDialogOpen(true)}
                                                    loading={loadingCancel}
                                                />
                                            </>
                                        )}
                                    </>
                                )}
                            </div>
                        </div>
                    )}
                    <div className="mb-2">
                        <h3 className="font-semibold">{bookingTitle}</h3>
                        {(user?.user_type === 'CollegeStudent' || !bookingIsGroupSession) && <p>{BookingSubtitle}</p>}
                    </div>
                    {user?.user_type !== 'CollegeStudent' && (<span className="text-gray-500">Your message</span>)}
                    <p>{booking.message}</p>
                    {user?.user_type !== 'CollegeStudent' && bookingIsGroupSession && (
                        booking.trailblazers.map(trailblazer => (
                            <div key={trailblazer.id} className="flex flex-row items-center gap-4 mt-4">
                                {trailblazer.status === 'confirmed' && (<CircleCheck className="w-6 h-6 text-green-500" />)}
                                {trailblazer.status === 'declined' && (<CircleX className="w-6 h-6 text-red-500" />)}
                                {trailblazer.status === 'pending' && (<div className="w-6 h-6"></div>)}
                                <div>
                                    <h4 className="font-bold">{`${trailblazer.first_name} ${trailblazer.last_name}`}</h4>
                                    <p>{`${trailblazer.profile?.university} • ${trailblazer.profile?.college_major}`}</p>
                                </div>
                            </div>
                        ))
                    )}
                    {user?.user_type !== 'CollegeStudent' && booking.proposed_times && booking.proposed_times.length > 0 && (
                        <div className="mt-4">
                            <h4 className="font-semibold mb-2">Select a Proposed Time:</h4>
                            {booking.proposed_times.map((time, index) => (
                                !isTimeInPast(time.start_time) && (
                                    <Button
                                        key={index}
                                        onClick={() => {

                                            handleSelectProposedTime(time)
                                        }}
                                        className="mr-2 mb-2"
                                        disabled={proposedTimeConfirmed}

                                    >
                                        {convertUTCToLocal(time.start_time)}
                                    </Button>
                                )
                            ))}
                        </div>
                    )}
                    {user?.user_type === 'CollegeStudent' && booking.proposed_times && booking.proposed_times.length > 0 && (
                        <div className="mt-4">
                            <h4 className="font-semibold mb-2">New meeting times proposed by you:</h4>
                            {booking.proposed_times.map((time, index) => (
                                !isTimeInPast(time.start_time) && (
                                    <div key={index} className="flex items-center justify-between mr-2 mb-2">
                                        <span>{convertUTCToLocal(time.start_time)}</span>
                                        <Button
                                            variant="outline"
                                            className="ml-2"
                                            onClick={() => handleRemoveProposedTime(booking.id, time.start_time)}
                                        >
                                            <Trash className="w-4 h-4" />
                                        </Button>
                                    </div>
                                )
                            ))}
                        </div>
                    )}
                </div>
            </CardContent>
            <CancelSessionDialog
                isDecline={false}
                isOpen={isCancelDialogOpen}
                onClose={() => setIsCancelDialogOpen(false)}
                onSubmit={(formData) => handleCancel(formData)}
                bookingNames={bookingNames}
                sessionDate={bookingDatetime.date}
                sessionTime={bookingDatetime.startTime}
                isLoading={loadingCancel}
            />
            <CancelSessionDialog
                isDecline={true}
                isOpen={isDeclineDialogOpen}
                onClose={() => setIsDeclineDialogOpen(false)}
                onSubmit={(formData) => handleDecline(formData)}
                bookingNames={bookingNames}
                sessionDate={bookingDatetime.date}
                sessionTime={bookingDatetime.startTime}
                isLoading={loadingDecline}
            />
            <ConfirmBookingChangeDialog
                isOpen={isConfirmDialogOpen}
                time={selectedProposedTime ? convertUTCToLocal(selectedProposedTime.start_time) : ''}
                onClose={() => setIsConfirmDialogOpen(false)}
                onConfirm={handleConfirmProposedTime}
                isLoading={loadingConfirmedProposedTime}
            />
        </Card>
    )
}
