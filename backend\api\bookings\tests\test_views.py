from datetime import datetime, timedelta, time
import calendar
import pytz

from django.urls import reverse
from django.contrib.auth import get_user_model
from django.utils import timezone

from rest_framework.test import APITestCase
from rest_framework import status

from api.bookings.models import Booking, TrailblazerBookingStatus
from api.users.models import Availability
from api.organizations.models import Organization

User = get_user_model()


class BookingAPITestCase(APITestCase):
    def setUp(self):
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=self.user)
        self.url = reverse('booking-list')
        self.start_time = timezone.now() + timedelta(days=4)

    # def test_create_booking_with_valid_data(self):
    #     data = {
    #         'trailblazers': [self.trailblazer.id],
    #         'start_time': self.start_time.isoformat(),
    #         'message': "Looking forward to the session.",
    #         'number_of_students': 5
    #     }
    #     response = self.client.post(self.url, data, format='json')
    #     self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    #     self.assertEqual(response.data['status'], 'pending')
    #     self.assertEqual(response.data['booked_by']['id'], self.user.id)

    def test_create_booking_without_authentication(self):
        self.client.force_authenticate(user=None)
        data = {
            'trailblazers': [self.trailblazer.id],
            'start_time': self.start_time.isoformat(),
            'message': "Looking forward to the session.",
            'number_of_students': 5
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_create_overlapping_booking_by_another_user(self):
        # Create an initial booking by the authenticated user
        booking= Booking.objects.create(
            booked_by=self.user,
            start_time=self.start_time,
            end_time=self.start_time + timedelta(hours=1),
            message="Existing booking.",
            number_of_students=2
        )
        booking.set_trailblazers([(self.trailblazer, 'pending')])

        # Attempt to create an overlapping booking by another user
        another_user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=another_user)
        overlapping_start_time = self.start_time  # Overlaps with the existing booking
        data = {
            'trailblazers': [self.trailblazer.id],
            'start_time': overlapping_start_time.isoformat(),
            'message': "Attempt to book at the same time.",
            'number_of_students': 4
        }
        response = self.client.post(self.url, data, format='json')

        # Assert that the response is 400 and that there are non-field errors indicating an overlap
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('non_field_errors', response.data)

    # def test_create_booking_with_insufficient_advance_notice(self):
    #     data = {
    #         'trailblazers': [self.trailblazer.id],
    #         'start_time': (timezone.now() + timedelta(hours=71)).isoformat(),
    #         'message': "Short notice booking.",
    #         'number_of_students': 3
    #     }
    #     response = self.client.post(self.url, data, format='json')
    #     self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    #     self.assertIn('start_time', response.data)

    def test_create_overlapping_booking_by_same_user(self):
        booking = Booking.objects.create(
            booked_by=self.user,
            start_time=self.start_time,
            end_time=self.start_time + timedelta(hours=1),
            message="Existing booking.",
            number_of_students=2
        )
        booking.set_trailblazers([(self.trailblazer, 'pending')])
        data = {
            'trailblazers': [self.trailblazer.id],
            'start_time': self.start_time.isoformat(),
            'message': "Overlapping booking.",
            'number_of_students': 4
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('non_field_errors', response.data)

    # def test_start_time_stored_in_utc(self):
    #     data = {
    #         'trailblazers': [self.trailblazer.id],
    #         'start_time': self.start_time.isoformat(),
    #         'message': "UTC time test.",
    #         'number_of_students': 1
    #     }
    #     response = self.client.post(self.url, data, format='json')
    #     self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    #     booking = Booking.objects.get(id=response.data['id'])
    #     self.assertEqual(booking.start_time.tzinfo, timezone.utc)


class BookingUpdateCreatorStatusTestCase(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=self.user)

        # Create a booking
        self.booking = Booking.objects.create(
            booked_by=self.user,
            start_time=timezone.now() + timedelta(days=4),
            message="Test booking.",
            number_of_students=2,
            creator_status='pending'  # Initial status
        )
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.url = reverse('booking-detail', args=[self.booking.id])

    def test_update_creator_status_to_confirmed(self):
        data = {'creator_status': 'confirmed'}
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.creator_status, 'confirmed')

    def test_update_creator_status_to_cancelled(self):
        data = {'creator_status': 'cancelled'}
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.booking.refresh_from_db()
        self.assertEqual(self.booking.creator_status, 'cancelled')
        self.assertEqual(self.booking.status, 'cancelled')

    def test_update_creator_status_unauthorized_user(self):
        # Authenticate as a different user
        another_user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=another_user)
        
        data = {'creator_status': 'confirmed'}
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_update_creator_status_unauthorized_user(self):
        # Authenticate as a different user
        another_user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=another_user)
        
        data = {'creator_status': 'confirmed'}
        response = self.client.patch(self.url, data, format='json')
        
        # Assert that the response is 404 because the booking is not visible to this user
        self.assertIn(response.status_code, [status.HTTP_403_FORBIDDEN, status.HTTP_404_NOT_FOUND])



class RetrieveStudentDaysAvailabilityTest(APITestCase):
    def setUp(self):
        # Create users
        self.student1 = User.objects.create_user(email='<EMAIL>', password='password123')
        self.student2 = User.objects.create_user(email='<EMAIL>', password='password123')
        
        # Create availabilities for student1
        Availability.objects.create(
            user=self.student1,
            time_zone='EST',
            monday_available=True,
            tuesday_available=True,
            wednesday_available=True,
            thursday_available=False,
            friday_available=True,
            monday_time_ranges=[{'start_time': '09:00', 'end_time': '17:00'}],
            tuesday_time_ranges=[{'start_time': '10:00', 'end_time': '16:00'}],
            wednesday_time_ranges=[{'start_time': '08:00', 'end_time': '12:00'}],
            friday_time_ranges=[{'start_time': '11:00', 'end_time': '15:00'}],
        )

        # Create availabilities for student2
        Availability.objects.create(
            user=self.student2,
            time_zone='EST',
            monday_available=True,
            tuesday_available=False,
            wednesday_available=True,
            thursday_available=True,
            friday_available=True,
            monday_time_ranges=[{'start_time': '10:00', 'end_time': '16:00'}],
            wednesday_time_ranges=[{'start_time': '09:00', 'end_time': '13:00'}],
            thursday_time_ranges=[{'start_time': '12:00', 'end_time': '16:00'}],
            friday_time_ranges=[{'start_time': '10:00', 'end_time': '14:00'}],
        )

        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=self.user)
        self.url = reverse('booking-availability-retrieve-student-days-availability')
    
    def get_expected_dates(self, start_date, end_date, students):
        """
        Helper function to calculate expected available dates based on students' availability.
        """
        expected_dates = set()
        date = start_date
        while date <= end_date:
            weekday = date.weekday()  # Monday is 0
            day_name = calendar.day_name[weekday].lower()
            day_available_attr = f"{day_name}_available"
            available = []
            for student in students:
                availability = student.availability
                if getattr(availability, day_available_attr):
                    available.append(date)
            if len(available) == len(students):
                expected_dates.add(date.isoformat())
            date += timedelta(days=1)
        return expected_dates
    
    def test_single_student_available_dates(self):
        """Test availability for a single student within a given date range"""
        start_date = (datetime.now() + timedelta(days=5)).date()
        end_date = start_date + timedelta(days=7)
        response = self.client.get(self.url, {
            'student_ids': [str(self.student1.id)],
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data['available_dates'], list)
        # Calculate expected dates based on student1's availability
        expected_dates = self.get_expected_dates(start_date, end_date, [self.student1])
        self.assertEqual(set(response.data['available_dates']), expected_dates)
    
    def test_multiple_students_overlapping_availability(self):
        """Test availability for multiple students where only overlapping dates are returned"""
        start_date = (datetime.now() + timedelta(days=5)).date()
        end_date = start_date + timedelta(days=7)
        response = self.client.get(self.url, {
            'student_ids': [str(self.student1.id), str(self.student2.id)],
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIsInstance(response.data['available_dates'], list)
        # Calculate expected overlapping dates
        expected_dates = self.get_expected_dates(start_date, end_date, [self.student1, self.student2])
        self.assertEqual(set(response.data['available_dates']), expected_dates)

    # def test_fully_booked_slots_excluded(self):
    #     """Test that fully booked slots are excluded from the available dates"""
    #     # Create a booking that fully books a specific date
    #     booked_date = (datetime.now() + timedelta(days=5)).date()
    #     booking = Booking.objects.create(
    #         booked_by=self.student2,
    #         start_time=datetime.combine(booked_date, datetime.min.time()) + timedelta(hours=9),
    #         end_time=datetime.combine(booked_date, datetime.min.time()) + timedelta(hours=17),
    #         message='Booked',
    #         number_of_students=1,
    #         is_available=False
    #     )
    #     booking.set_trailblazers([(self.student1, 'confirmed')])
    #     response = self.client.get(self.url, {
    #         'student_ids': [str(self.student1.id)],
    #         'start_date': booked_date.isoformat(),
    #         'end_date': booked_date.isoformat()
    #     })
    #     self.assertEqual(response.status_code, status.HTTP_200_OK)
    #     self.assertIsInstance(response.data['available_dates'], list)
    #     # Since the date is fully booked, it should not be in the available dates
    #     self.assertNotIn(booked_date.isoformat(), response.data['available_dates'])
    
    def test_no_overlapping_availability_returns_empty(self):
        """Test when there is no overlapping availability between students, returns an empty list"""
        # Set student2 to be unavailable on all days
        availability = self.student2.availability
        availability.monday_available = False
        availability.tuesday_available = False
        availability.wednesday_available = False
        availability.thursday_available = False
        availability.friday_available = False
        availability.save()
        
        start_date = (datetime.now() + timedelta(days=5)).date()
        end_date = start_date + timedelta(days=7)
        response = self.client.get(self.url, {
            'student_ids': [str(self.student1.id), str(self.student2.id)],
            'start_date': start_date.isoformat(),
            'end_date': end_date.isoformat()
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Since there is no overlapping availability, we expect an empty list
        self.assertEqual(response.data['available_dates'], [])
    
    def test_invalid_date_range_returns_400(self):
        """Test that invalid date range returns a 400 Bad Request"""
        response = self.client.get(self.url, {
            'student_ids': [str(self.student1.id)],
            'start_date': '2023-13-01',  # Invalid date
            'end_date': '2023-12-01'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_nonexistent_student_ids_returns_400(self):
        """Test when non-existent student IDs are provided, the response returns an empty list"""
        response = self.client.get(self.url, {
            'student_ids': ['e20c6a37-5283-42de-b3b6-d6b3b7b5174a'],  # Nonexistent student ID
            'start_date': '2023-12-01',
            'end_date': '2023-12-10'
        })
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)


class RetrieveStudentTimesAvailabilityTest(APITestCase):
    def setUp(self):
        self.student1 = User.objects.create_user(email='<EMAIL>', password='password123')
        self.student2 = User.objects.create_user(email='<EMAIL>', password='password123')
        
        # Create availability for students
        Availability.objects.create(
            user=self.student1,
            time_zone='UTC',
            monday_available=True,
            monday_time_ranges=[
                {'start_time': '09:00', 'end_time': '12:00'},
                {'start_time': '13:00', 'end_time': '17:00'}
            ],
            # Assume similar fields for other weekdays
        )
        Availability.objects.create(
            user=self.student2,
            time_zone='UTC',
            monday_available=True,
            monday_time_ranges=[
                {'start_time': '10:00', 'end_time': '11:00'},
                {'start_time': '14:00', 'end_time': '16:00'}
            ],
        )
        
        self.user = User.objects.create_user(email='<EMAIL>', password='password123')
        self.client.force_authenticate(user=self.user)
        self.url = reverse('booking-availability-retrieve-student-times-availability')
    
    def test_valid_student_ids_and_date(self):
        date = '2024-04-29'  # Assuming it's a Monday
        response = self.client.get(self.url, {'student_ids': [self.student1.id, self.student2.id], 'date': date, 'timezone': 'UTC'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_slots = [
            {"start": "10:00", "end": "11:00"},
            {"start": "14:00", "end": "15:00"},
            {"start": "14:30", "end": "15:30"},
            {"start": "15:00", "end": "16:00"}
        ]
        self.assertEqual(response.data['available_time_slots'], expected_slots)
        self.assertEqual(response.data['time_zone'], 'UTC')
    
    def test_missing_student_ids(self):
        date = '2024-04-29'
        response = self.client.get(self.url, {'date': date})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_missing_date(self):
        response = self.client.get(self.url, {'student_ids': [self.student1.id]})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_invalid_student_ids(self):
        date = '2024-04-29'
        response = self.client.get(self.url, {'student_ids': ['e20c6a37-5283-42de-b3b6-d6b3b7b5174a'], 'date': date}) # Nonexistent student ID
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_invalid_date_format(self):
        response = self.client.get(self.url, {'student_ids': [self.student1.id], 'date': '29-04-2024'})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('error', response.data)
    
    def test_no_available_slots(self):
        date = '2024-04-29'
        # Create a booking that overlaps with available slots
        booking = Booking.objects.create(
            booked_by=self.user,
            start_time=datetime(2024, 4, 29, 10, 0, tzinfo=pytz.UTC),
            end_time=datetime(2024, 4, 29, 11, 0, tzinfo=pytz.UTC)
        )
        booking.set_trailblazers([(self.student1, 'confirmed')])
        response = self.client.get(self.url, {'student_ids': [self.student1.id, self.student2.id], 'date': date, 'timezone': 'UTC'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        expected_slots = [
            {"start": "14:00", "end": "15:00"},
            {"start": "14:30", "end": "15:30"},
            {"start": "15:00", "end": "16:00"},
        ]
        self.assertEqual(response.data['available_time_slots'], expected_slots)
    
    def test_timezone_conversion(self):
        date = '2024-04-29'
        response = self.client.get(self.url, {
            'student_ids': [self.student1.id, self.student2.id],
            'date': date,
            'timezone': 'US/Eastern'
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['time_zone'], 'US/Eastern')
        expected_slots = [
            {"start": "06:00", "end": "07:00"},
            {"start": "10:00", "end": "11:00"},
            {"start": "10:30", "end": "11:30"},
            {"start": "11:00", "end": "12:00"},
        ]
        self.assertEqual(response.data['available_time_slots'], expected_slots)

    def test_partial_hours_not_included(self):
        Availability.objects.filter(user=self.student1).update(monday_time_ranges=[{'start_time': '09:15', 'end_time': '09:45'}])
        response = self.client.get(self.url, {'student_ids': [self.student1.id], 'date': '2024-04-29'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        time_slots = response.data['available_time_slots']
        self.assertEqual(time_slots, [])


class TrailblazerBookingViewSetTestCase(APITestCase):
    def setUp(self):
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.other_trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=self.trailblazer)
        
        # Create upcoming booking
        self.upcoming_booking = Booking.objects.create(
            booked_by=self.user,
            start_time=timezone.now() + timedelta(days=5),
            end_time=timezone.now() + timedelta(days=5, hours=1),
            message="Upcoming session.",
            number_of_students=3
        )
        self.upcoming_booking.add_trailblazer(self.trailblazer)
        
        # Create historical booking
        self.historical_booking = Booking.objects.create(
            booked_by=self.user,
            start_time=timezone.now() - timedelta(days=5),
            end_time=timezone.now() - timedelta(days=5, hours=1),
            message="Historical session.",
            number_of_students=2
        )
        self.historical_booking.add_trailblazer(self.trailblazer)
        
        # Booking for another trailblazer
        self.other_booking = Booking.objects.create(
            booked_by=self.user,
            start_time=timezone.now() + timedelta(days=10),
            end_time=timezone.now() + timedelta(days=10, hours=1),
            message="Other trailblazer's session.",
            number_of_students=4
        )
        self.other_booking.add_trailblazer(self.other_trailblazer)
        self.url = reverse('booking-trailblazer-list')
    
    def test_retrieve_bookings_for_trailblazer(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 2)
        # Check booking_status field
        historical_statuses = {booking['id']: booking['is_historical'] for booking in response.data['results']}
        self.assertEqual(historical_statuses[self.upcoming_booking.id], False)
        self.assertEqual(historical_statuses[self.historical_booking.id], True)
    
    def test_bookings_sorted_by_start_time(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        booking_ids = [booking['id'] for booking in response.data['results']]
        self.assertEqual(booking_ids, sorted(booking_ids, key=lambda x: Booking.objects.get(id=x).start_time))
    
    def test_endpoint_requires_authentication(self):
        self.client.force_authenticate(user=None)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_endpoint_returns_only_trailblazer_bookings(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        booking_ids = [booking['id'] for booking in response.data['results']]
        self.assertIn(self.upcoming_booking.id, booking_ids)
        self.assertIn(self.historical_booking.id, booking_ids)
        self.assertNotIn(self.other_booking.id, booking_ids)
    
    def test_no_bookings_returns_empty_list(self):
        # Remove existing bookings
        Booking.objects.filter(trailblazer_statuses__trailblazer=self.trailblazer).delete()
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['results'], [])


class TrailblazerBookingStatusUpdateTest(APITestCase):
    def setUp(self):
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booked_by = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=timezone.now() + timedelta(days=4),
            message="Test booking.",
            number_of_students=2
        )
        self.status_obj = TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer, status='pending')
        self.url = reverse('booking-trailblazer-update-status', args=[self.booking.id])
        self.client.login(email='<EMAIL>', password='Password123!')

    def test_authorized_trailblazer_confirm_pending_booking(self):
        data = {'status': 'confirmed'}
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.status_obj.refresh_from_db()
        self.assertEqual(self.status_obj.status, 'confirmed')

    def test_authorized_trailblazer_decline_pending_booking(self):
        data = {'status': 'declined', 'decline_reason': 'decline message'}
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.status_obj.refresh_from_db()
        self.assertEqual(self.status_obj.status, 'declined')

    def test_authorized_trailblazer_cancel_confirmed_booking(self):
        self.status_obj.status = 'confirmed'
        self.status_obj.save()
        data = {'status': 'declined', 'decline_reason': 'decline message'}
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.status_obj.refresh_from_db()
        self.assertEqual(self.status_obj.status, 'declined')

    def test_unauthorized_user_cannot_update_status(self):
        self.client.logout()
        unauthorized_user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.login(email='<EMAIL>', password='Password123!')
        data = {'status': 'confirmed'}
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_invalid_status_transition(self):
        self.status_obj.status = 'confirmed'
        self.status_obj.save()
        data = {'status': 'pending'}
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.status_obj.refresh_from_db()
        self.assertEqual(self.status_obj.status, 'confirmed')


class ImpactStatisticsViewSetTestCase(APITestCase):
    def setUp(self):
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COUNSELOR_ADMINISTRATOR_TYPE
        )
        self.organization1 = Organization.objects.create(name='University A')
        self.organization2 = Organization.objects.create(name='College B')
        
        # Assign organizations to users
        self.user1.profile.organization = self.organization1
        self.user1.profile.save()
        self.user2.profile.organization = self.organization2
        self.user2.profile.save()
        
        # Create completed bookings
        self.booking1 = Booking.objects.create(
            booked_by=self.user1,
            start_time=timezone.now() - timedelta(days=10, hours=2),
            end_time=timezone.now() - timedelta(days=10)
        )
        self.booking1.add_trailblazer(self.trailblazer, 'confirmed')
        
        self.booking2 = Booking.objects.create(
            booked_by=self.user2,
            start_time=timezone.now() - timedelta(days=5, hours=3),
            end_time=timezone.now() - timedelta(days=5)
        )
        self.booking2.add_trailblazer(self.trailblazer, 'confirmed')
        
        # Create non-completed bookings
        self.booking3 = Booking.objects.create(
            booked_by=self.user1,
            start_time=timezone.now() + timedelta(days=2),
            end_time=timezone.now() + timedelta(days=2, hours=1),
            creator_status='cancelled'
        )
        self.booking3.add_trailblazer(self.trailblazer, 'declined')
        
        self.url = reverse('booking-trailblazer-impact-statistics')
        self.client.force_authenticate(user=self.trailblazer)
    
    def test_impact_statistics_with_completed_bookings(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_hours'], 5)  # 2 hours + 3 hours
        self.assertEqual(response.data['total_schools_connected'], 2)
    
    def test_impact_statistics_with_no_completed_bookings(self):
        Booking.objects.filter(creator_status='confirmed').update(creator_status='cancelled')
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_hours'], 0)
        self.assertEqual(response.data['total_schools_connected'], 0)
    
    def test_impact_statistics_with_multiple_organizations(self):
        # Already setup with two organizations
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['total_schools_connected'], 2)
    
    def test_impact_statistics_handles_overlapping_bookings(self):
        # Create overlapping completed booking
        overlapping_booking = Booking.objects.create(
            booked_by=self.user1,
            start_time=timezone.now() - timedelta(days=10, hours=1),
            end_time=timezone.now() - timedelta(days=10, minutes=30),
        )
        overlapping_booking.add_trailblazer(self.trailblazer, 'confirmed')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # total_hours should now be 5.0 + 0.5 = 5.5 = 6 (rounded)
        self.assertEqual(response.data['total_hours'], 6)
        self.assertEqual(response.data['total_schools_connected'], 2)
    
    def test_impact_statistics_handles_zero_duration_bookings(self):
        zero_duration_booking = Booking.objects.create(
            booked_by=self.user1,
            start_time=timezone.now() - timedelta(days=8),
            end_time=timezone.now() - timedelta(days=8)
        )
        zero_duration_booking.add_trailblazer(self.trailblazer, 'confirmed')
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # total_hours should remain 5.0
        self.assertEqual(response.data['total_hours'], 5)
        self.assertEqual(response.data['total_schools_connected'], 2)
    
    def test_impact_statistics_handles_missing_data(self):
        # Delete organization from a user
        self.user1.profile.organization = None
        self.user1.profile.save()
        
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # total_schools_connected should now be 1
        self.assertEqual(response.data['total_hours'], 5)
        self.assertEqual(response.data['total_schools_connected'], 1)
