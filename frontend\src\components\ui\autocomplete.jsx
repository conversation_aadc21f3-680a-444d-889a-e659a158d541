import { Command as CommandPrimitive } from "cmdk";
import { useState, useRef, useCallback } from "react";
import {
  CommandGroup,
  CommandItem,
  CommandList,
} from "@/components/ui/command";
import { Skeleton } from "@/components/ui/skeleton";
import { Check, X } from "lucide-react";
import { cn } from "@/lib/utils";
import { rankSearchAlphabetically } from "@/lib/utils";

export const AutoComplete = ({
  options = [],
  placeholder,
  emptyMessage,
  value,
  onValueChange,
  disabled,
  isLoading = false,
  searchThreshold = 3,
  showAllOptionsByDefault = false,
  clearInputOnSelect = false,
  className,
}) => {
  const inputRef = useRef(null);

  const [isOpen, setOpen] = useState(false);
  const [selected, setSelected] = useState(
    typeof value === "string" ? { value } : value
  );
  const [inputValue, setInputValue] = useState(
    typeof value === "string" ? value : value?.label || value?.value || ""
  );

  const getLabel = useCallback((option) => option.label || option.value, []);

  const handleKeyDown = useCallback(
    (event) => {
      const input = inputRef.current;
      if (!input) return;

      if (!isOpen) setOpen(true);

      if (event.key === "Enter" && input.value !== "") {
        const optionToSelect = options.find(
          (option) => getLabel(option) === input.value
        );

        if (optionToSelect) {
          setSelected(optionToSelect);
          onValueChange?.(
            typeof value === "string" ? optionToSelect.value : optionToSelect
          );
        } else {
          setSelected(null);
          setInputValue("");
        }
      }

      if (event.key === "Escape") {
        input.blur();
      }
    },
    [isOpen, options, onValueChange, value, getLabel]
  );

  const handleBlur = useCallback(() => {
    setOpen(false);

    const optionToSelect = options.find(
      (option) => getLabel(option) === inputValue
    );
    if (!optionToSelect) {
      setSelected(null);
      setInputValue("");
    }
  }, [inputValue, options, getLabel]);

  const handleSelectOption = useCallback(
    (selectedOption) => {
      const label = getLabel(selectedOption);

      // Clear the input field only if clearInputOnSelect is true
      if (clearInputOnSelect) {
        setInputValue("");
      } else {
        setInputValue(label);
      }

      setSelected(selectedOption);
      onValueChange?.(
        typeof value === "string" ? selectedOption.value : selectedOption
      );

      setTimeout(() => {
        inputRef?.current?.blur();
      }, 0);
    },
    [onValueChange, value, getLabel, clearInputOnSelect]
  );

  // Filtered options based on the new prop
  const filteredOptions = showAllOptionsByDefault
    ? options // Show all options when the prop is true
    : inputValue.length >= searchThreshold
    ? options.filter((option) =>
        getLabel(option).toLowerCase().includes(inputValue.toLowerCase())
      )
    : [];

  return (
    <CommandPrimitive
      filter={rankSearchAlphabetically}
      className="relative w-full" // Keep wrapper styles minimal
    >
      <div>
        <CommandPrimitive.Input
          ref={inputRef}
          value={inputValue}
          onValueChange={isLoading ? undefined : setInputValue}
          onBlur={handleBlur}
          onFocus={() => setOpen(true)}
          placeholder={placeholder}
          disabled={disabled}
          className={cn(
            "w-full rounded-lg border border-slate-300 bg-white px-4 py-2 pr-10 text-sm shadow-sm outline-none transition focus:border-blue-500 focus:ring-1 focus:ring-blue-500",
            disabled && "bg-gray-100 text-gray-400 cursor-not-allowed",
            className || "" // Merge custom className with default styles
          )}
        />
        {inputValue && (
          <button
            type="button"
            onClick={() => {
              setSelected(null);
              setInputValue("");
              onValueChange?.(null);
            }}
            className="absolute right-2 top-1/2 -translate-y-1/2 rounded-full p-1 text-gray-500 hover:bg-gray-200 hover:text-gray-700 focus:outline-none"
          >
            <X className="h-4 w-4" />
          </button>
        )}
      </div>
      <div
        className={cn(
          "absolute mt-1 w-full rounded-lg border border-slate-300 bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none z-10",
          !isOpen && "hidden"
        )}
      >
        <CommandList className="max-h-60 overflow-y-auto p-2">
          {isLoading && (
            <CommandPrimitive.Loading>
              <div className="p-1">
                <Skeleton className="h-8 w-full" />
              </div>
            </CommandPrimitive.Loading>
          )}
          {!isLoading &&
            !showAllOptionsByDefault && // Show "Type at least..." message only when showAllOptionsByDefault is false
            inputValue.length < searchThreshold && (
              <CommandPrimitive.Empty className="select-none rounded-md px-2 py-3 text-center text-sm text-gray-500">
                {`Type at least ${searchThreshold} characters to search.`}
              </CommandPrimitive.Empty>
            )}
          {!isLoading &&
            showAllOptionsByDefault && // Show emptyMessage when showAllOptionsByDefault is true and no options are available
            filteredOptions.length === 0 && (
              <CommandPrimitive.Empty className="select-none rounded-md px-2 py-3 text-center text-sm text-gray-500">
                {emptyMessage}
              </CommandPrimitive.Empty>
            )}
          {!isLoading && filteredOptions.length > 0 && (
            <CommandGroup>
              {filteredOptions.map((option) => {
                const isSelected = selected?.value === option.value;
                return (
                  <CommandItem
                    key={option.value}
                    value={getLabel(option)}
                    onMouseDown={(event) => {
                      event.preventDefault();
                      event.stopPropagation();
                    }}
                    onSelect={() => handleSelectOption(option)}
                    className={cn(
                      "flex w-full cursor-pointer items-center gap-2 rounded-md px-3 py-2 text-sm hover:bg-blue-100 hover:text-blue-900",
                      isSelected && "bg-blue-500 text-white"
                    )}
                  >
                    {isSelected ? <Check className="h-4 w-4" /> : null}
                    {getLabel(option)}
                  </CommandItem>
                );
              })}
            </CommandGroup>
          )}
        </CommandList>
      </div>
    </CommandPrimitive>
  );
};
