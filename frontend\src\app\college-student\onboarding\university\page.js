"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { Button } from "@/components/ui/button"
import { ArrowRight } from 'lucide-react'
import { set, useForm } from "react-hook-form"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { getNextOnboardingStep } from '@/lib/utils'
import { useToast } from "@/hooks/use-toast"
import { UniversityCombobox, SchoolTypeToggle } from "@/components/ui/universityPicker"
import { Loader2 } from 'lucide-react'

// NextButton component
const NextButton = ({ loading }) => {
    return (
        <Button
            type="submit"
            className="mt-8"
            disabled={loading}
        >
            {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}
            Next
            <ArrowRight className="ml-2 w-6 h-6" />
        </Button>
    )
}


// Main UniversitySelection component
const UniversitySelection = () => {
    const router = useRouter()
    const { toast } = useToast()
    const { getToken } = useAuth()
    const [isLoading, setIsLoading] = useState(false)

    const formSchema = z.object({
        university: z.string().min(1, "Please select your university"),
        schoolTypes: z.array(z.string()).nonempty("Please select at least one school type"),
        graduationYear: z.string().nonempty("Please select your graduation year"),
    })

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            university: "",
            schoolTypes: [],
            graduationYear: ""
        }
    })

    const onSubmit = async (formData) => {
        setIsLoading(true)
        const payload = {
            step: "university",
            university: formData.university,
            graduation_year: formData.graduationYear,
            university_tags: formData.schoolTypes
        }
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${getToken()}`
                },
                body: JSON.stringify(payload),
            })
            if (response.ok) {
                toast({
                    title: 'success',
                    description: 'University information uploaded successfully'
                })
                router.push(getNextOnboardingStep('CollegeStudent', 'university'))
            } else {
                console.error('Error Response:', response)
                toast({
                    variant: 'destructive',
                    description: result.detail || 'Failed to upload university information'
                })
                setIsLoading(false)
            }
        } catch (error) {
            console.error('Error submitting university:', error)
            toast({
                variant: 'destructive',
                description: 'An unexpected error occurred'
            })
            setIsLoading(false)
        }
    }

    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="py-10">
                    <StepProgressDisplay currentStep={2} totalSteps={6} />
                </div>
                <div className="w-full md:w-5/6 lg:w-5/6">
                    <h1 className="text-4xl font-bold mb-8">And, where are you now?</h1>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <FormField
                                control={form.control}
                                name="university"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormControl>
                                            <UniversityCombobox field={field} form={form}/>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="graduationYear"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Graduation Year*</FormLabel>
                                        <FormControl>
                                            <select
                                                value={field.value}
                                                onChange={(e) => {
                                                    field.onChange(e.target.value);
                                                }}
                                                className="w-full rounded-lg border border-slate-300 bg-white px-4 py-2 pr-10 text-sm shadow-sm outline-none transition focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                                                required
                                            >
                                                <option value="" disabled>Select graduation year</option>
                                                {Array.from({ length: 6 }, (_, i) => new Date().getFullYear() + i).map(year => (
                                                    <option key={year} value={year}>{year}</option>
                                                ))}
                                            </select>
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="schoolTypes"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>How would you describe your school? (Select all that apply)</FormLabel>
                                        <FormControl>
                                            <SchoolTypeToggle
                                                {...field}
                                            />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <div>
                                <NextButton
                                    loading={isLoading}
                                />
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(UniversitySelection)
