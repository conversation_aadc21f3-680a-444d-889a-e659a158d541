openapi: 3.0.3
info:
  title: Trailblazer_v1.1_Updated API
  version: 1.0.0
  description: Documentation of API endpoints of Trailblazer_v1.1_Updated
paths:
  /api/auth-token/:
    post:
      operationId: auth_token_create
      tags:
      - auth-token
      requestBody:
        content:
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/AuthToken'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/AuthToken'
          application/json:
            schema:
              $ref: '#/components/schemas/AuthToken'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/AuthToken'
          description: ''
  /api/recommendations/:
    get:
      operationId: recommendations_retrieve
      description: |-
        API endpoint for retrieving university recommendations.

        GET /api/recommendations/

        Returns categorized recommendations (reach, target, safety) for the authenticated user.
        Each university now includes a 'bookmarked' attribute indicating if it's in the user's shortlist.

        Query parameters:
        - ordering: Field to order universities by (e.g., "acceptance_rate" or "-avg_net_price").
      tags:
      - recommendations
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/schema/:
    get:
      operationId: schema_retrieve
      description: |-
        OpenApi3 schema for this API. Format can be selected via content negotiation.

        - YAML: application/vnd.oai.openapi
        - JSON: application/vnd.oai.openapi+json
      parameters:
      - in: query
        name: format
        schema:
          type: string
          enum:
          - json
          - yaml
      - in: query
        name: lang
        schema:
          type: string
          enum:
          - af
          - ar
          - ar-dz
          - ast
          - az
          - be
          - bg
          - bn
          - br
          - bs
          - ca
          - ckb
          - cs
          - cy
          - da
          - de
          - dsb
          - el
          - en
          - en-au
          - en-gb
          - eo
          - es
          - es-ar
          - es-co
          - es-mx
          - es-ni
          - es-ve
          - et
          - eu
          - fa
          - fi
          - fr
          - fy
          - ga
          - gd
          - gl
          - he
          - hi
          - hr
          - hsb
          - hu
          - hy
          - ia
          - id
          - ig
          - io
          - is
          - it
          - ja
          - ka
          - kab
          - kk
          - km
          - kn
          - ko
          - ky
          - lb
          - lt
          - lv
          - mk
          - ml
          - mn
          - mr
          - ms
          - my
          - nb
          - ne
          - nl
          - nn
          - os
          - pa
          - pl
          - pt
          - pt-br
          - ro
          - ru
          - sk
          - sl
          - sq
          - sr
          - sr-latn
          - sv
          - sw
          - ta
          - te
          - tg
          - th
          - tk
          - tr
          - tt
          - udm
          - uk
          - ur
          - uz
          - vi
          - zh-hans
          - zh-hant
      tags:
      - schema
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/vnd.oai.openapi:
              schema:
                type: object
                additionalProperties: {}
            application/yaml:
              schema:
                type: object
                additionalProperties: {}
            application/vnd.oai.openapi+json:
              schema:
                type: object
                additionalProperties: {}
            application/json:
              schema:
                type: object
                additionalProperties: {}
          description: ''
  /api/shortlist/:
    get:
      operationId: shortlist_list
      description: |-
        API endpoint for retrieving and creating shortlist items for the authenticated user.
        GET returns the list of universities shortlisted by the user.
        POST creates a new shortlist item.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - shortlist
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
    post:
      operationId: shortlist_create
      description: |-
        API endpoint for retrieving and creating shortlist items for the authenticated user.
        GET returns the list of universities shortlisted by the user.
        POST creates a new shortlist item.
      tags:
      - shortlist
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/ShortlistItem'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/ShortlistItem'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/ShortlistItem'
        required: true
      security:
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/ShortlistItem'
          description: ''
  /api/shortlist/{college_id}/:
    delete:
      operationId: shortlist_destroy
      description: |-
        DELETE API endpoint for removing a college from a user's shortlist.

        Permissions:
        - User must be authenticated as a high school student
        - User must own the shortlist item

        Returns:
        - 204 No Content on successful deletion
        - 404 Not Found if shortlist item doesn't exist
      parameters:
      - in: path
        name: college_id
        schema:
          type: string
        required: true
      tags:
      - shortlist
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/universities/:
    get:
      operationId: universities_list
      description: |-
        API endpoint that allows universities to be searched.

        Performs a case-insensitive partial match against the university's
        name, city, or state fields using PostgreSQL trigram similarity.
      parameters:
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: page_size
        required: false
        in: query
        description: Number of results to return per page.
        schema:
          type: integer
      tags:
      - universities
      security:
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUniversitySearchList'
          description: ''
  /api/universities/details/{unitid}/:
    get:
      operationId: universities_details_retrieve
      description: API endpoint that returns detailed information about a university.
      parameters:
      - in: path
        name: unitid
        schema:
          type: string
        required: true
      tags:
      - universities
      security:
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UniversityDetail'
          description: ''
  /api/v1/availabilities/:
    get:
      operationId: v1_availabilities_list
      description: A viewset for viewing and editing user availability.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAvailabilityList'
          description: ''
    post:
      operationId: v1_availabilities_create
      description: A viewset for viewing and editing user availability.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Availability'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Availability'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Availability'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
  /api/v1/availabilities/{id}/:
    get:
      operationId: v1_availabilities_retrieve
      description: A viewset for viewing and editing user availability.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
    put:
      operationId: v1_availabilities_update
      description: A viewset for viewing and editing user availability.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Availability'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Availability'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Availability'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
    patch:
      operationId: v1_availabilities_partial_update
      description: A viewset for viewing and editing user availability.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAvailability'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAvailability'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAvailability'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
    delete:
      operationId: v1_availabilities_destroy
      description: A viewset for viewing and editing user availability.
      parameters:
      - in: path
        name: id
        schema:
          type: string
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/availabilities/current/:
    get:
      operationId: v1_availabilities_current_retrieve
      description: Retrieve the most recent availability for the authenticated user.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
  /api/v1/bookings/availabilities/:
    get:
      operationId: v1_bookings_availabilities_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedAvailabilityList'
          description: ''
    post:
      operationId: v1_bookings_availabilities_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Availability'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Availability'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Availability'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
  /api/v1/bookings/availabilities/{id}/:
    get:
      operationId: v1_bookings_availabilities_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this availability.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
    put:
      operationId: v1_bookings_availabilities_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this availability.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Availability'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Availability'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Availability'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
    patch:
      operationId: v1_bookings_availabilities_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this availability.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedAvailability'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedAvailability'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedAvailability'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
    delete:
      operationId: v1_bookings_availabilities_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this availability.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/bookings/availabilities/student-availability/:
    get:
      operationId: v1_bookings_availabilities_student_availability_retrieve
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
  /api/v1/bookings/availabilities/student-times-availability/:
    get:
      operationId: v1_bookings_availabilities_student_times_availability_retrieve
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Availability'
          description: ''
  /api/v1/bookings/bookings/:
    get:
      operationId: v1_bookings_bookings_list
      description: A viewset for creating and managing bookings by the user who booked
        them.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedBookingList'
          description: ''
    post:
      operationId: v1_bookings_bookings_create
      description: A viewset for creating and managing bookings by the user who booked
        them.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/bookings/bookings/{id}/:
    get:
      operationId: v1_bookings_bookings_retrieve
      description: A viewset for creating and managing bookings by the user who booked
        them.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
    put:
      operationId: v1_bookings_bookings_update
      description: A viewset for creating and managing bookings by the user who booked
        them.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
    patch:
      operationId: v1_bookings_bookings_partial_update
      description: A viewset for creating and managing bookings by the user who booked
        them.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedBooking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedBooking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedBooking'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
    delete:
      operationId: v1_bookings_bookings_destroy
      description: A viewset for creating and managing bookings by the user who booked
        them.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/bookings/bookings/{id}/confirm_proposed_time/:
    post:
      operationId: v1_bookings_bookings_confirm_proposed_time_create
      description: A viewset for creating and managing bookings by the user who booked
        them.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/bookings/reminders/:
    get:
      operationId: v1_bookings_reminders_retrieve
      description: A view to fetch trailblazer booking statuses that need email reminders.
      tags:
      - v1
      responses:
        '200':
          description: No response body
  /api/v1/bookings/trailblazer/:
    get:
      operationId: v1_bookings_trailblazer_list
      description: |-
        List bookings associated with the authenticated trailblazer,
        including pagination.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedBookingList'
          description: ''
    post:
      operationId: v1_bookings_trailblazer_create
      description: A viewset for trailblazers to view and manage their bookings.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/bookings/trailblazer/{id}/:
    get:
      operationId: v1_bookings_trailblazer_retrieve
      description: A viewset for trailblazers to view and manage their bookings.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
    put:
      operationId: v1_bookings_trailblazer_update
      description: A viewset for trailblazers to view and manage their bookings.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
    patch:
      operationId: v1_bookings_trailblazer_partial_update
      description: A viewset for trailblazers to view and manage their bookings.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedBooking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedBooking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedBooking'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
    delete:
      operationId: v1_bookings_trailblazer_destroy
      description: A viewset for trailblazers to view and manage their bookings.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/bookings/trailblazer/{id}/propose_times/:
    post:
      operationId: v1_bookings_trailblazer_propose_times_create
      description: Propose new times for a booking.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/bookings/trailblazer/{id}/remove_proposed_time/:
    post:
      operationId: v1_bookings_trailblazer_remove_proposed_time_create
      description: Remove a proposed time from a booking.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/bookings/trailblazer/{id}/update_status/:
    post:
      operationId: v1_bookings_trailblazer_update_status_create
      description: Update the status of a booking for the authenticated trailblazer.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        description: A unique integer value identifying this booking.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Booking'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Booking'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Booking'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/bookings/trailblazer/impact-statistics/:
    get:
      operationId: v1_bookings_trailblazer_impact_statistics_retrieve
      description: A viewset for trailblazers to view and manage their bookings.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Booking'
          description: ''
  /api/v1/college-students/:
    get:
      operationId: v1_college_students_list
      description: A viewset for viewing and editing college student instances with
        search, filter, and ordering by proximity.
      parameters:
      - in: query
        name: day
        schema:
          type: string
      - in: query
        name: graduation_year
        schema:
          type: string
      - in: query
        name: interests
        schema:
          type: string
      - in: query
        name: major
        schema:
          type: string
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - in: query
        name: organization_tags
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      - in: query
        name: university_tags
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: v1_college_students_create
      description: A viewset for viewing and editing college student instances with
        search, filter, and ordering by proximity.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/college-students/{id}/:
    get:
      operationId: v1_college_students_retrieve
      description: A viewset for viewing and editing college student instances with
        search, filter, and ordering by proximity.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: v1_college_students_update
      description: A viewset for viewing and editing college student instances with
        search, filter, and ordering by proximity.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: v1_college_students_partial_update
      description: A viewset for viewing and editing college student instances with
        search, filter, and ordering by proximity.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUser'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    delete:
      operationId: v1_college_students_destroy
      description: A viewset for viewing and editing college student instances with
        search, filter, and ordering by proximity.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/configuration/configuration/:
    get:
      operationId: v1_configuration_configuration_retrieve
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Configuration'
          description: ''
  /api/v1/counselors/:
    get:
      operationId: v1_counselors_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: v1_counselors_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/counselors/{id}/:
    get:
      operationId: v1_counselors_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: v1_counselors_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: v1_counselors_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUser'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    delete:
      operationId: v1_counselors_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/email-verification/send/:
    post:
      operationId: v1_email_verification_send_create
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/email-verification/verify/:
    post:
      operationId: v1_email_verification_verify_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/VerifyEmail'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/VerifyEmail'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/VerifyEmail'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/VerifyEmail'
          description: ''
  /api/v1/high-school-students/:
    get:
      operationId: v1_high_school_students_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedUserList'
          description: ''
    post:
      operationId: v1_high_school_students_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
  /api/v1/high-school-students/{id}/:
    get:
      operationId: v1_high_school_students_retrieve
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: v1_high_school_students_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/User'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/User'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/User'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    patch:
      operationId: v1_high_school_students_partial_update
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUser'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    delete:
      operationId: v1_high_school_students_destroy
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/organizations/:
    get:
      operationId: v1_organizations_list
      description: A viewset for viewing and editing organization instances.
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedOrganizationList'
          description: ''
    post:
      operationId: v1_organizations_create
      description: A viewset for viewing and editing organization instances.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Organization'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Organization'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Organization'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/organizations/{id}/:
    get:
      operationId: v1_organizations_retrieve
      description: A viewset for viewing and editing organization instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this organization.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
    put:
      operationId: v1_organizations_update
      description: A viewset for viewing and editing organization instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this organization.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Organization'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Organization'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Organization'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
    patch:
      operationId: v1_organizations_partial_update
      description: A viewset for viewing and editing organization instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this organization.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedOrganization'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedOrganization'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedOrganization'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
    delete:
      operationId: v1_organizations_destroy
      description: A viewset for viewing and editing organization instances.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this organization.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '204':
          description: No response body
  /api/v1/organizations/{id}/name/:
    get:
      operationId: v1_organizations_name_retrieve
      description: Custom action to retrieve only the name of an organization.
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this organization.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/organizations/tags/batch-verify/:
    post:
      operationId: v1_organizations_tags_batch_verify_create
      description: A viewset for viewing and editing organization instances.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Organization'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Organization'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Organization'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/organizations/tags/create/:
    post:
      operationId: v1_organizations_tags_create_create
      description: A viewset for viewing and editing organization instances.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/Organization'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/Organization'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/Organization'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/organizations/tags/list/:
    get:
      operationId: v1_organizations_tags_list_retrieve
      description: Retrieve a list of verified organization tags with their IDs and
        names.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/organizations/tags/user/:
    get:
      operationId: v1_organizations_tags_user_retrieve
      description: Retrieve the organization tags associated with the authenticated
        user's profile.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/organizations/tags/verify/:
    get:
      operationId: v1_organizations_tags_verify_retrieve
      description: |-
        Checks if a tag is verified.
        - Tags in the Organization table are automatically verified.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/Organization'
          description: ''
  /api/v1/payments/automation-setting/:
    get:
      operationId: v1_payments_automation_setting_retrieve
      tags:
      - v1
      responses:
        '200':
          description: No response body
  /api/v1/payments/payment-logs/:
    get:
      operationId: v1_payments_payment_logs_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPaymentLogList'
          description: ''
    post:
      operationId: v1_payments_payment_logs_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PaymentLog'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PaymentLog'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PaymentLog'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaymentLog'
          description: ''
  /api/v1/payments/payment-logs/user/:
    get:
      operationId: v1_payments_payment_logs_user_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPaymentLogList'
          description: ''
  /api/v1/payments/skipped-payment-logs/:
    get:
      operationId: v1_payments_skipped_payment_logs_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedSkippedPaymentLogList'
          description: ''
    post:
      operationId: v1_payments_skipped_payment_logs_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/SkippedPaymentLog'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/SkippedPaymentLog'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/SkippedPaymentLog'
        required: true
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/SkippedPaymentLog'
          description: ''
  /api/v1/payments/trailblazer-hours/:
    get:
      operationId: v1_payments_trailblazer_hours_list
      parameters:
      - name: ordering
        required: false
        in: query
        description: Which field to use when ordering the results.
        schema:
          type: string
      - name: page
        required: false
        in: query
        description: A page number within the paginated result set.
        schema:
          type: integer
      - name: search
        required: false
        in: query
        description: A search term.
        schema:
          type: string
      tags:
      - v1
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/PaginatedPaymentPeriodTrailblazerHoursList'
          description: ''
  /api/v1/payments/trailblazer-hours/update-all/:
    get:
      operationId: v1_payments_trailblazer_hours_update_all_retrieve
      tags:
      - v1
      responses:
        '200':
          description: No response body
  /api/v1/payments/update-payment-log/:
    post:
      operationId: v1_payments_update_payment_log_create
      tags:
      - v1
      responses:
        '200':
          description: No response body
  /api/v1/users/:
    post:
      operationId: v1_users_create
      description: User view set
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CreateUser'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CreateUser'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CreateUser'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CreateUser'
          description: ''
  /api/v1/users/{id}/:
    get:
      operationId: v1_users_retrieve
      description: User view set
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/User'
          description: ''
    put:
      operationId: v1_users_update
      description: User view set
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdate'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    patch:
      operationId: v1_users_partial_update
      description: User view set
      parameters:
      - in: path
        name: id
        schema:
          type: string
          format: uuid
        description: A UUID string identifying this user.
        required: true
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdate'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
  /api/v1/users/college-students/onboarding/:
    get:
      operationId: v1_users_college_students_onboarding_retrieve
      description: Generic onboarding view for all user types.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollegeStudentOnboarding'
          description: ''
    put:
      operationId: v1_users_college_students_onboarding_update
      description: Generic onboarding view for all user types.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollegeStudentOnboarding'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CollegeStudentOnboarding'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CollegeStudentOnboarding'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollegeStudentOnboarding'
          description: ''
    patch:
      operationId: v1_users_college_students_onboarding_partial_update
      description: Generic onboarding view for all user types.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCollegeStudentOnboarding'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCollegeStudentOnboarding'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCollegeStudentOnboarding'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollegeStudentOnboarding'
          description: ''
  /api/v1/users/college-students/signup/:
    post:
      operationId: v1_users_college_students_signup_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CollegeStudentSignUp'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CollegeStudentSignUp'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CollegeStudentSignUp'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CollegeStudentSignUp'
          description: ''
  /api/v1/users/counselors/onboarding/:
    get:
      operationId: v1_users_counselors_onboarding_retrieve
      description: Generic onboarding view for all user types.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounselorAdministratorOnboarding'
          description: ''
    put:
      operationId: v1_users_counselors_onboarding_update
      description: Generic onboarding view for all user types.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CounselorAdministratorOnboarding'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CounselorAdministratorOnboarding'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CounselorAdministratorOnboarding'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounselorAdministratorOnboarding'
          description: ''
    patch:
      operationId: v1_users_counselors_onboarding_partial_update
      description: Generic onboarding view for all user types.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedCounselorAdministratorOnboarding'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedCounselorAdministratorOnboarding'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedCounselorAdministratorOnboarding'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounselorAdministratorOnboarding'
          description: ''
  /api/v1/users/counselors/signup/:
    post:
      operationId: v1_users_counselors_signup_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/CounselorAdministratorSignUp'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/CounselorAdministratorSignUp'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/CounselorAdministratorSignUp'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/CounselorAdministratorSignUp'
          description: ''
  /api/v1/users/high-school-student-profile/:
    post:
      operationId: v1_users_high_school_student_profile_create
      description: API endpoint for creating high school student profile data.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentProfileCreate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentProfileCreate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentProfileCreate'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HighSchoolStudentProfileCreate'
          description: ''
  /api/v1/users/high-school-students/onboarding/:
    get:
      operationId: v1_users_high_school_students_onboarding_retrieve
      description: Generic onboarding view for all user types.
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HighSchoolStudentOnboarding'
          description: ''
    put:
      operationId: v1_users_high_school_students_onboarding_update
      description: Generic onboarding view for all user types.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentOnboarding'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentOnboarding'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentOnboarding'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HighSchoolStudentOnboarding'
          description: ''
    patch:
      operationId: v1_users_high_school_students_onboarding_partial_update
      description: Generic onboarding view for all user types.
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedHighSchoolStudentOnboarding'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedHighSchoolStudentOnboarding'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedHighSchoolStudentOnboarding'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HighSchoolStudentOnboarding'
          description: ''
  /api/v1/users/high-school-students/signup/:
    post:
      operationId: v1_users_high_school_students_signup_create
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentSignUp'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentSignUp'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/HighSchoolStudentSignUp'
        required: true
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '201':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HighSchoolStudentSignUp'
          description: ''
  /api/v1/users/highschoolstudentprofile/{id}/:
    get:
      operationId: v1_users_highschoolstudentprofile_retrieve
      description: |-
        API endpoint for retrieving a specific high school student profile.

        Requires authentication and ensures users can only access their own profile.
      parameters:
      - in: path
        name: id
        schema:
          type: integer
        required: true
      tags:
      - v1
      security:
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/HighSchoolStudentProfileDetail'
          description: ''
  /api/v1/users/login/:
    post:
      operationId: v1_users_login_create
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/users/majors/:
    get:
      operationId: v1_users_majors_retrieve
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      - {}
      responses:
        '200':
          description: No response body
  /api/v1/users/me/:
    get:
      operationId: v1_users_me_retrieve
      description: User view set
      tags:
      - v1
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    put:
      operationId: v1_users_me_update
      description: User view set
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/UserUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/UserUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/UserUpdate'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
    patch:
      operationId: v1_users_me_partial_update
      description: User view set
      tags:
      - v1
      requestBody:
        content:
          application/json:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdate'
          application/x-www-form-urlencoded:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdate'
          multipart/form-data:
            schema:
              $ref: '#/components/schemas/PatchedUserUpdate'
      security:
      - cookieAuth: []
      - tokenAuth: []
      responses:
        '200':
          content:
            application/json:
              schema:
                $ref: '#/components/schemas/UserUpdate'
          description: ''
  /api/v1/users/profile/update/:
    post:
      operationId: v1_users_profile_update_create
      description: |-
        API endpoint for updating high school student profiles and triggering recommendation regeneration.

        This endpoint:
        1. Validates and updates the profile data
        2. Sets the recommendations_status to "pending"
        3. Triggers the recommendation regeneration process
        4. Returns the updated profile with the "pending" status

        Rate limited to 3 requests per 6-hour window.
      tags:
      - v1
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
  /api/v1/users/recommendations/status/:
    get:
      operationId: v1_users_recommendations_status_retrieve
      description: |-
        API endpoint to check the status of university recommendations for the authenticated user.

        GET /api/recommendations/status/

        Returns:
            recommendations_status: Current status of recommendations (not_requested, pending, ready)
      tags:
      - v1
      security:
      - tokenAuth: []
      responses:
        '200':
          description: No response body
components:
  schemas:
    AuthToken:
      type: object
      properties:
        username:
          type: string
          writeOnly: true
        password:
          type: string
          writeOnly: true
        token:
          type: string
          readOnly: true
      required:
      - password
      - token
      - username
    Availability:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: string
          format: uuid
          readOnly: true
        time_zone:
          type: string
          maxLength: 50
        monday_available:
          type: boolean
        tuesday_available:
          type: boolean
        wednesday_available:
          type: boolean
        thursday_available:
          type: boolean
        friday_available:
          type: boolean
        saturday_available:
          type: boolean
        sunday_available:
          type: boolean
        monday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        tuesday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        wednesday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        thursday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        friday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        saturday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        sunday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - time_zone
      - updated_at
      - user
    BlankEnum:
      enum:
      - ''
    Booking:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        trailblazers:
          type: array
          items:
            type: string
            format: uuid
          writeOnly: true
        booked_by:
          type: string
          readOnly: true
        start_time:
          type: string
          format: date-time
        end_time:
          type: string
          format: date-time
          readOnly: true
        message:
          type: string
          maxLength: 280
        number_of_students:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        creator_status:
          allOf:
          - $ref: '#/components/schemas/CreatorStatusEnum'
          default: confirmed
        creator_status_updated_at:
          type: string
          format: date-time
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        status:
          type: string
          readOnly: true
        current_trailblazer_status:
          type: string
          readOnly: true
        is_historical:
          type: string
          readOnly: true
        meet_link:
          type: string
          format: uri
          readOnly: true
          nullable: true
        creator_timezone:
          type: string
          nullable: true
          maxLength: 64
        creator_cancel_reason:
          type: string
          nullable: true
        proposed_times:
          nullable: true
        proposed_time_confirmed:
          type: boolean
      required:
      - booked_by
      - created_at
      - creator_status_updated_at
      - current_trailblazer_status
      - end_time
      - id
      - is_historical
      - meet_link
      - message
      - start_time
      - status
      - trailblazers
    CollegeStudentOnboarding:
      type: object
      properties:
        step:
          type: string
          maxLength: 30
        first_name:
          type: string
          maxLength: 30
        last_name:
          type: string
          maxLength: 30
        paypal_email:
          type: string
          format: email
        bio:
          type: string
          maxLength: 280
        high_school_name:
          type: string
          maxLength: 255
        high_school_zip_code:
          type: string
          maxLength: 10
        high_school_city:
          type: string
          maxLength: 100
        high_school_state:
          type: string
          maxLength: 100
        university:
          type: string
          maxLength: 255
        graduation_year:
          type: string
          maxLength: 255
        university_tags:
          type: array
          items:
            type: string
            maxLength: 255
        college_major:
          type: string
          maxLength: 255
        organization_tags:
          type: array
          items:
            type: integer
        interests:
          type: array
          items:
            type: string
            maxLength: 255
        availability:
          $ref: '#/components/schemas/Availability'
        avatar:
          type: string
          format: uri
      required:
      - step
    CollegeStudentSignUp:
      type: object
      properties:
        email:
          type: string
          format: email
          maxLength: 254
        password:
          type: string
          writeOnly: true
        auth_token:
          type: string
          readOnly: true
      required:
      - auth_token
      - email
      - password
    Configuration:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        use_waitlist:
          type: boolean
      required:
      - id
    CounselorAdministratorOnboarding:
      type: object
      properties:
        step:
          type: string
          maxLength: 30
        first_name:
          type: string
          maxLength: 30
        last_name:
          type: string
          maxLength: 30
        position:
          type: string
          maxLength: 255
        organization:
          type: string
          format: uuid
        avatar:
          type: string
          format: uri
      required:
      - step
    CounselorAdministratorSignUp:
      type: object
      properties:
        email:
          type: string
          format: email
          maxLength: 254
        password:
          type: string
          writeOnly: true
        auth_token:
          type: string
          readOnly: true
      required:
      - auth_token
      - email
      - password
    CountryEnum:
      enum:
      - usa
      - other
      type: string
      description: |-
        * `usa` - USA
        * `other` - Other
    CreateUser:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        password:
          type: string
          maxLength: 128
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
        email:
          type: string
          format: email
          maxLength: 254
        user_type:
          $ref: '#/components/schemas/CreateUserUserTypeEnum'
        auth_token:
          type: string
          readOnly: true
        terms_accepted:
          type: boolean
          writeOnly: true
      required:
      - auth_token
      - email
      - id
      - password
      - terms_accepted
    CreateUserUserTypeEnum:
      enum:
      - CollegeStudent
      - HighSchoolStudent
      - CounselorAdministrator
      type: string
      description: |-
        * `CollegeStudent` - CollegeStudent
        * `HighSchoolStudent` - HighSchoolStudent
        * `CounselorAdministrator` - CounselorAdministrator
    CreatorStatusEnum:
      enum:
      - confirmed
      - cancelled
      type: string
      description: |-
        * `confirmed` - Confirmed
        * `cancelled` - Cancelled
    EthnicityEnum:
      enum:
      - native_american
      - south_asian
      - east_asian
      - black
      - hispanic
      - middle_eastern
      - native_hawaiian
      - white
      - multiracial
      - other
      type: string
      description: |-
        * `native_american` - Native American or Alaska Native
        * `south_asian` - South Asian
        * `east_asian` - East Asian
        * `black` - Black or African American
        * `hispanic` - Hispanic or Latine
        * `middle_eastern` - Middle Eastern or North African
        * `native_hawaiian` - Native Hawaiian or Pacific Islander
        * `white` - White
        * `multiracial` - Multiracial
        * `other` - Other
    FinancialAidNeedEnum:
      enum:
      - high
      - medium
      - low
      - no_need
      type: string
      description: |-
        * `high` - High need
        * `medium` - Medium need
        * `low` - Low need
        * `no_need` - No financial aid need
    GenderEnum:
      enum:
      - female
      - male
      - non-binary
      - other
      - prefer_not_to_say
      type: string
      description: |-
        * `female` - Female
        * `male` - Male
        * `non-binary` - Non-binary
        * `other` - Other
        * `prefer_not_to_say` - Prefer not to say
    GeographicPreferenceTypeEnum:
      enum:
      - states
      - zip
      - none
      type: string
      description: |-
        * `states` - States
        * `zip` - ZIP Code
        * `none` - None
    HighSchoolStudentOnboarding:
      type: object
      properties:
        step:
          type: string
          maxLength: 30
        first_name:
          type: string
          maxLength: 30
        last_name:
          type: string
          maxLength: 30
        grade_level:
          type: integer
        avatar:
          type: string
          format: uri
      required:
      - step
    HighSchoolStudentProfileCreate:
      type: object
      description: Serializer for creating/updating high school student profile data
        with validation.
      properties:
        unweighted_gpa:
          type: number
          format: double
          nullable: true
        weighted_gpa:
          type: number
          format: double
          nullable: true
        sat_math_score:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        sat_reading_score:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        act_score:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        high_school_name:
          type: string
          nullable: true
          maxLength: 255
        graduation_year:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        intended_majors: {}
        extracurriculars: {}
        interests: {}
        college_type_preferences: {}
        location_type_preferences: {}
        geographic_preference_type:
          $ref: '#/components/schemas/GeographicPreferenceTypeEnum'
        preferred_states: {}
        preferred_zip:
          type: string
          nullable: true
          maxLength: 10
        preferred_radius:
          nullable: true
          description: |-
            Preferred radius from ZIP code

            * `0-20` - 0-20 miles (short drive)
            * `21-50` - 21-50 miles (medium drive)
            * `51-200` - 51-200 miles (long drive)
            * `201-500` - 201-500 miles (short flight)
            * `501+` - 501+ miles (long flight)
          oneOf:
          - $ref: '#/components/schemas/PreferredRadiusEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        country:
          $ref: '#/components/schemas/CountryEnum'
        current_zip_code:
          type: string
          nullable: true
          maxLength: 10
        gender:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/GenderEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        ethnicity:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/EthnicityEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        household_income:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/HouseholdIncomeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        financial_aid_need:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/FinancialAidNeedEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        organization:
          type: string
          format: uuid
          readOnly: true
      required:
      - organization
    HighSchoolStudentProfileDetail:
      type: object
      description: |-
        Serializer for retrieving high school student profile details.
        Returns data in the same structure as HighSchoolStudentProfileCreateSerializer accepts.
      properties:
        unweighted_gpa:
          type: number
          format: double
          nullable: true
        weighted_gpa:
          type: number
          format: double
          nullable: true
        sat_math_score:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        sat_reading_score:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        act_score:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        high_school_name:
          type: string
          nullable: true
          maxLength: 255
        graduation_year:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
        intended_majors: {}
        extracurriculars: {}
        interests: {}
        college_type_preferences: {}
        location_type_preferences: {}
        geographic_preference_type:
          $ref: '#/components/schemas/GeographicPreferenceTypeEnum'
        preferred_states: {}
        preferred_zip:
          type: string
          nullable: true
          maxLength: 10
        preferred_radius:
          nullable: true
          description: |-
            Preferred radius from ZIP code

            * `0-20` - 0-20 miles (short drive)
            * `21-50` - 21-50 miles (medium drive)
            * `51-200` - 51-200 miles (long drive)
            * `201-500` - 201-500 miles (short flight)
            * `501+` - 501+ miles (long flight)
          oneOf:
          - $ref: '#/components/schemas/PreferredRadiusEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        country:
          $ref: '#/components/schemas/CountryEnum'
        current_zip_code:
          type: string
          nullable: true
          maxLength: 10
        gender:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/GenderEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        ethnicity:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/EthnicityEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        household_income:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/HouseholdIncomeEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        financial_aid_need:
          nullable: true
          oneOf:
          - $ref: '#/components/schemas/FinancialAidNeedEnum'
          - $ref: '#/components/schemas/BlankEnum'
          - $ref: '#/components/schemas/NullEnum'
        organization:
          type: string
          format: uuid
          readOnly: true
      required:
      - organization
    HighSchoolStudentSignUp:
      type: object
      properties:
        email:
          type: string
          format: email
          maxLength: 254
        password:
          type: string
          writeOnly: true
        organization:
          type: string
          format: uuid
          writeOnly: true
        auth_token:
          type: string
          readOnly: true
      required:
      - auth_token
      - email
      - organization
      - password
    HouseholdIncomeEnum:
      enum:
      - under_30k
      - 30k_48k
      - 48k_75k
      - 75k_100k
      - 100k_plus
      type: string
      description: |-
        * `under_30k` - Under $30,000
        * `30k_48k` - $30,001-$48,000
        * `48k_75k` - $48,001-$75,000
        * `75k_100k` - $75,001-$100,000
        * `100k_plus` - $100,001+
    NullEnum:
      enum:
      - null
    Organization:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          maxLength: 255
        zip_code:
          type: string
          maxLength: 10
        city:
          type: string
          maxLength: 100
        state:
          type: string
          maxLength: 100
        school_district:
          type: string
          nullable: true
          maxLength: 255
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - city
      - created_at
      - id
      - name
      - state
      - updated_at
      - zip_code
    PaginatedAvailabilityList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Availability'
    PaginatedBookingList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Booking'
    PaginatedOrganizationList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/Organization'
    PaginatedPaymentLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PaymentLog'
    PaginatedPaymentPeriodTrailblazerHoursList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/PaymentPeriodTrailblazerHours'
    PaginatedSkippedPaymentLogList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/SkippedPaymentLog'
    PaginatedUniversitySearchList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/UniversitySearch'
    PaginatedUserList:
      type: object
      required:
      - count
      - results
      properties:
        count:
          type: integer
          example: 123
        next:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=4
        previous:
          type: string
          nullable: true
          format: uri
          example: http://api.example.org/accounts/?page=2
        results:
          type: array
          items:
            $ref: '#/components/schemas/User'
    PatchedAvailability:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: string
          format: uuid
          readOnly: true
        time_zone:
          type: string
          maxLength: 50
        monday_available:
          type: boolean
        tuesday_available:
          type: boolean
        wednesday_available:
          type: boolean
        thursday_available:
          type: boolean
        friday_available:
          type: boolean
        saturday_available:
          type: boolean
        sunday_available:
          type: boolean
        monday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        tuesday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        wednesday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        thursday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        friday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        saturday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        sunday_time_ranges:
          type: array
          items:
            $ref: '#/components/schemas/TimeRange'
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    PatchedBooking:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        trailblazers:
          type: array
          items:
            type: string
            format: uuid
          writeOnly: true
        booked_by:
          type: string
          readOnly: true
        start_time:
          type: string
          format: date-time
        end_time:
          type: string
          format: date-time
          readOnly: true
        message:
          type: string
          maxLength: 280
        number_of_students:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
        creator_status:
          allOf:
          - $ref: '#/components/schemas/CreatorStatusEnum'
          default: confirmed
        creator_status_updated_at:
          type: string
          format: date-time
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
        status:
          type: string
          readOnly: true
        current_trailblazer_status:
          type: string
          readOnly: true
        is_historical:
          type: string
          readOnly: true
        meet_link:
          type: string
          format: uri
          readOnly: true
          nullable: true
        creator_timezone:
          type: string
          nullable: true
          maxLength: 64
        creator_cancel_reason:
          type: string
          nullable: true
        proposed_times:
          nullable: true
        proposed_time_confirmed:
          type: boolean
    PatchedCollegeStudentOnboarding:
      type: object
      properties:
        step:
          type: string
          maxLength: 30
        first_name:
          type: string
          maxLength: 30
        last_name:
          type: string
          maxLength: 30
        paypal_email:
          type: string
          format: email
        bio:
          type: string
          maxLength: 280
        high_school_name:
          type: string
          maxLength: 255
        high_school_zip_code:
          type: string
          maxLength: 10
        high_school_city:
          type: string
          maxLength: 100
        high_school_state:
          type: string
          maxLength: 100
        university:
          type: string
          maxLength: 255
        graduation_year:
          type: string
          maxLength: 255
        university_tags:
          type: array
          items:
            type: string
            maxLength: 255
        college_major:
          type: string
          maxLength: 255
        organization_tags:
          type: array
          items:
            type: integer
        interests:
          type: array
          items:
            type: string
            maxLength: 255
        availability:
          $ref: '#/components/schemas/Availability'
        avatar:
          type: string
          format: uri
    PatchedCounselorAdministratorOnboarding:
      type: object
      properties:
        step:
          type: string
          maxLength: 30
        first_name:
          type: string
          maxLength: 30
        last_name:
          type: string
          maxLength: 30
        position:
          type: string
          maxLength: 255
        organization:
          type: string
          format: uuid
        avatar:
          type: string
          format: uri
    PatchedHighSchoolStudentOnboarding:
      type: object
      properties:
        step:
          type: string
          maxLength: 30
        first_name:
          type: string
          maxLength: 30
        last_name:
          type: string
          maxLength: 30
        grade_level:
          type: integer
        avatar:
          type: string
          format: uri
    PatchedOrganization:
      type: object
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        name:
          type: string
          maxLength: 255
        zip_code:
          type: string
          maxLength: 10
        city:
          type: string
          maxLength: 100
        state:
          type: string
          maxLength: 100
        school_district:
          type: string
          nullable: true
          maxLength: 255
        created_at:
          type: string
          format: date-time
          readOnly: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
    PatchedUser:
      type: object
      description: User serializer
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
        email:
          type: string
          format: email
          readOnly: true
        user_type:
          allOf:
          - $ref: '#/components/schemas/UserUserTypeEnum'
          readOnly: true
        profile:
          type: string
          readOnly: true
        distance:
          type: number
          format: double
          readOnly: true
        onboarding_steps_completed:
          type: string
          readOnly: true
    PatchedUserUpdate:
      type: object
      properties:
        first_name:
          type: string
          readOnly: true
        last_name:
          type: string
          readOnly: true
        organization:
          type: string
          readOnly: true
    PaymentLog:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        user:
          type: string
          format: uuid
        paypal_email:
          type: string
          format: email
          maxLength: 254
        hours:
          type: number
          format: double
          nullable: true
        amount:
          type: string
          format: decimal
          pattern: ^-?\d{0,8}(?:\.\d{0,2})?$
          nullable: true
        payment_status:
          type: string
          maxLength: 1000
        time_processed:
          type: string
          format: date-time
          nullable: true
        payment_period_start:
          type: string
          format: date-time
          nullable: true
        payment_period_end:
          type: string
          format: date-time
          nullable: true
        payment_date:
          type: string
          format: date-time
          nullable: true
        payout_batch_id:
          type: string
          nullable: true
          maxLength: 255
        payout_item_id:
          type: string
          nullable: true
          maxLength: 255
        batch_status:
          type: string
          nullable: true
          maxLength: 50
        time_completed:
          type: string
          format: date-time
          nullable: true
        error_message:
          type: string
          nullable: true
      required:
      - id
      - payment_status
      - payout_batch_id
      - payout_item_id
      - paypal_email
      - user
    PaymentPeriodTrailblazerHours:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        paypal_email:
          type: string
          readOnly: true
        user:
          type: string
          format: uuid
        total_hours:
          type: number
          format: double
        payment_period_start:
          type: string
          format: date-time
          nullable: true
        payment_period_end:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
      required:
      - id
      - payment_period_end
      - payment_period_start
      - paypal_email
      - user
    PreferredRadiusEnum:
      enum:
      - 0-20
      - 21-50
      - 51-200
      - 201-500
      - 501+
      type: string
      description: |-
        * `0-20` - 0-20 miles (short drive)
        * `21-50` - 21-50 miles (medium drive)
        * `51-200` - 51-200 miles (long drive)
        * `201-500` - 201-500 miles (short flight)
        * `501+` - 501+ miles (long flight)
    ShortlistItem:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        university_id:
          type: string
          writeOnly: true
        university_name:
          type: string
          readOnly: true
        created_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - created_at
      - id
      - university_id
      - university_name
    SkippedPaymentLog:
      type: object
      properties:
        id:
          type: integer
          readOnly: true
        paypal_email:
          type: string
          maxLength: 254
        hours:
          type: number
          format: double
          nullable: true
        error_message:
          type: string
          maxLength: 255
        time_processed:
          type: string
          format: date-time
          nullable: true
        payment_status:
          type: string
          nullable: true
          maxLength: 1000
        payment_period_start:
          type: string
          format: date-time
          nullable: true
        payment_period_end:
          type: string
          format: date-time
          nullable: true
        payout_batch_id:
          type: string
          nullable: true
          maxLength: 255
        payout_item_id:
          type: string
          nullable: true
          maxLength: 255
        payment_date:
          type: string
          format: date-time
          nullable: true
        batch_status:
          type: string
          nullable: true
          maxLength: 50
        time_completed:
          type: string
          format: date-time
          nullable: true
        created_at:
          type: string
          format: date-time
        user:
          type: string
          format: uuid
          nullable: true
        updated_at:
          type: string
          format: date-time
          readOnly: true
      required:
      - error_message
      - id
      - payout_batch_id
      - payout_item_id
      - paypal_email
      - updated_at
    TimeRange:
      type: object
      properties:
        start_time:
          type: string
          format: time
        end_time:
          type: string
          format: time
      required:
      - end_time
      - start_time
    UniversityDemographics:
      type: object
      description: Serializer for university demographics section
      properties:
        gender:
          type: string
          readOnly: true
        ethnicity:
          type: string
          readOnly: true
      required:
      - ethnicity
      - gender
    UniversityDetail:
      type: object
      description: Serializer for detailed university information
      properties:
        unitid:
          type: string
          description: Unique identifier for the institution
          maxLength: 50
        name:
          type: string
        location:
          type: string
          readOnly: true
        type:
          type: string
          readOnly: true
        website:
          type: string
        stats:
          $ref: '#/components/schemas/UniversityStats'
        facts:
          type: string
          readOnly: true
        programs:
          type: string
          readOnly: true
        financials:
          $ref: '#/components/schemas/UniversityFinancials'
        pell:
          $ref: '#/components/schemas/UniversityPell'
        demographics:
          $ref: '#/components/schemas/UniversityDemographics'
        connect_with_trailblazers:
          type: string
          readOnly: true
        is_bookmarked:
          type: string
          readOnly: true
      required:
      - connect_with_trailblazers
      - demographics
      - facts
      - financials
      - is_bookmarked
      - location
      - name
      - pell
      - programs
      - stats
      - type
      - unitid
      - website
    UniversityFinancials:
      type: object
      description: Serializer for university financials section
      properties:
        cost_after_aid:
          type: string
          readOnly: true
        cost_by_income:
          type: string
          readOnly: true
        median_annual_earnings:
          type: string
          readOnly: true
        earnings_range:
          type: string
          readOnly: true
        scorecard_url:
          type: string
          readOnly: true
      required:
      - cost_after_aid
      - cost_by_income
      - earnings_range
      - median_annual_earnings
      - scorecard_url
    UniversityPell:
      type: object
      description: Serializer for university Pell grant information
      properties:
        students_awarded:
          type: string
          readOnly: true
        median_debt:
          type: string
          readOnly: true
      required:
      - median_debt
      - students_awarded
    UniversitySearch:
      type: object
      description: Serializer for university search results.
      properties:
        unitid:
          type: string
          description: Unique identifier for the institution
          maxLength: 50
        name:
          type: string
        location:
          type: string
          readOnly: true
        type:
          type: string
        undergrad_count:
          type: integer
          maximum: 2147483647
          minimum: -2147483648
          nullable: true
          description: Number of undergraduate students
        is_bookmarked:
          type: string
          readOnly: true
      required:
      - is_bookmarked
      - location
      - name
      - type
      - unitid
    UniversityStats:
      type: object
      description: Serializer for university statistics section
      properties:
        undergrad_students:
          type: string
          readOnly: true
        student_faculty_ratio:
          type: string
          readOnly: true
        acceptance_rate:
          type: string
          readOnly: true
        sat_reading_writing:
          type: string
          readOnly: true
        sat_math:
          type: string
          readOnly: true
        act_composite:
          type: string
          readOnly: true
        retention_rate:
          type: string
          readOnly: true
        graduation_rate:
          type: string
          readOnly: true
        open_admissions:
          type: string
          readOnly: true
      required:
      - acceptance_rate
      - act_composite
      - graduation_rate
      - open_admissions
      - retention_rate
      - sat_math
      - sat_reading_writing
      - student_faculty_ratio
      - undergrad_students
    User:
      type: object
      description: User serializer
      properties:
        id:
          type: string
          format: uuid
          readOnly: true
        first_name:
          type: string
          maxLength: 150
        last_name:
          type: string
          maxLength: 150
        email:
          type: string
          format: email
          readOnly: true
        user_type:
          allOf:
          - $ref: '#/components/schemas/UserUserTypeEnum'
          readOnly: true
        profile:
          type: string
          readOnly: true
        distance:
          type: number
          format: double
          readOnly: true
        onboarding_steps_completed:
          type: string
          readOnly: true
      required:
      - distance
      - email
      - id
      - onboarding_steps_completed
      - profile
      - user_type
    UserUpdate:
      type: object
      properties:
        first_name:
          type: string
          readOnly: true
        last_name:
          type: string
          readOnly: true
        organization:
          type: string
          readOnly: true
      required:
      - first_name
      - last_name
      - organization
    UserUserTypeEnum:
      enum:
      - CollegeStudent
      - HighSchoolStudent
      - CounselorAdministrator
      type: string
      description: |-
        * `CollegeStudent` - College Student
        * `HighSchoolStudent` - High School Student
        * `CounselorAdministrator` - Counselor/Administrator
    VerifyEmail:
      type: object
      properties:
        code:
          type: string
          maxLength: 6
      required:
      - code
  securitySchemes:
    cookieAuth:
      type: apiKey
      in: cookie
      name: sessionid
    tokenAuth:
      type: apiKey
      in: header
      name: Authorization
      description: Token-based authentication with required prefix "Token"
