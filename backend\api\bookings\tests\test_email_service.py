from django.test import TestCase
from django.core import mail
from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta
from unittest.mock import patch
from api.bookings.services.email_service import BookingEmailService
from api.bookings.models import Booking, TrailblazerBookingStatus

User = get_user_model()

class BookingEmailServiceTest(TestCase):
    def setUp(self):
        # Start patches for email and calendar services
        patcher_send_mail = patch('api.bookings.services.email_service.send_mail')
        self.mock_send_mail = patcher_send_mail.start()
        self.addCleanup(patcher_send_mail.stop)

        patcher_create_events = patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
        self.mock_create_events = patcher_create_events.start()
        self.addCleanup(patcher_create_events.stop)

        patcher_delete_events = patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
        self.mock_delete_events = patcher_delete_events.start()
        self.addCleanup(patcher_delete_events.stop)
        
        # Create test data after patches are applied to ensure signals are mocked
        self.college_student = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        
        self.requester = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        
        self.booking = Booking.objects.create(
            booked_by=self.requester,
            start_time=timezone.now() + timedelta(days=4),
            message="Test booking",
            number_of_students=5
        )
        self.booking.set_trailblazers([(self.college_student, 'confirmed')])

    def test_send_session_request_email_success(self):
        BookingEmailService.send_session_request_email(self.booking)
        self.assertTrue(self.mock_send_mail.called)
        self.assertEqual(self.mock_send_mail.call_count, 1)

    def test_send_session_request_email_failure(self):
        self.mock_send_mail.side_effect = Exception("Email error")
        BookingEmailService.send_session_request_email(self.booking)
        self.assertTrue(self.mock_send_mail.called)


class SessionDeclineEmailTest(TestCase):
    def setUp(self):
        # Start patch for email service
        patcher_send_mail = patch('api.bookings.services.email_service.send_mail')
        self.mock_send_mail = patcher_send_mail.start()
        self.addCleanup(patcher_send_mail.stop)

        patcher_create_events = patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
        self.mock_create_events = patcher_create_events.start()
        self.addCleanup(patcher_create_events.stop)

        patcher_delete_events = patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
        self.mock_delete_events = patcher_delete_events.start()
        self.addCleanup(patcher_delete_events.stop)

        # Create test data
        self.trailblazer = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.booked_by = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.start_time = timezone.now() + timedelta(days=1)
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Test booking",
            creator_status='confirmed'
        )
        self.trailblazer_status = TrailblazerBookingStatus.objects.create(
            booking=self.booking,
            trailblazer=self.trailblazer,
            status='pending'
        )

    def test_decline_email_sent(self):
        """
        Test that an email is sent when a trailblazer's status changes to 'declined'.
        """
        # Change trailblazer status to 'declined'
        self.trailblazer_status.status = 'declined'
        self.trailblazer_status.decline_reason = "Unavailable for the session"
        self.trailblazer_status.save()

        # Assert that send_mail was called
        self.assertTrue(self.mock_send_mail.called)
        self.assertEqual(self.mock_send_mail.call_count, 1)

        # Verify the email recipient and subject
        call_args = self.mock_send_mail.call_args[1]
        self.assertIn(self.booking.booked_by.email, call_args['recipient_list'])
        self.assertEqual(call_args['subject'], '[Trailblazer] Session Declined')

    def test_decline_email_not_sent_for_other_status_changes(self):
        """
        Test that no email is sent if the status changes to anything other than 'declined'.
        """
        # Change trailblazer status to 'cancelled'
        self.trailblazer_status.status = 'cancelled'
        self.trailblazer_status.save()

        # Assert that send_mail was not called
        self.assertFalse(self.mock_send_mail.called)


class SessionConfirmationEmailTest(TestCase):
    def setUp(self):
        # Start patch for email service
        patcher_send_mail = patch('api.bookings.services.email_service.send_mail')
        self.mock_send_mail = patcher_send_mail.start()
        self.addCleanup(patcher_send_mail.stop)

        patcher_create_events = patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
        self.mock_create_events = patcher_create_events.start()
        self.addCleanup(patcher_create_events.stop)

        patcher_delete_events = patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
        self.mock_delete_events = patcher_delete_events.start()
        self.addCleanup(patcher_delete_events.stop)

        # Create test users
        self.booked_by = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.trailblazer1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.trailblazer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test booking
        self.start_time = timezone.now() + timedelta(days=1)
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Test booking",
            creator_status='confirmed'
        )

        # Create trailblazer statuses
        self.trailblazer_status1 = TrailblazerBookingStatus.objects.create(
            booking=self.booking,
            trailblazer=self.trailblazer1,
            status='confirmed'
        )
        self.trailblazer_status2 = TrailblazerBookingStatus.objects.create(
            booking=self.booking,
            trailblazer=self.trailblazer2,
            status='confirmed'
        )

    def test_confirmation_email_sent_to_all_recipients(self):
        """
        Test that confirmation emails are sent to all recipients.
        """
        BookingEmailService.send_session_confirmation_email(self.trailblazer_status1)

        # Check that send_mail was called 3 times (one for each recipient)
        self.assertEqual(self.mock_send_mail.call_count, 3)

        # Get the list of recipients from call arguments
        sent_emails = [call.kwargs['recipient_list'][0] for call in self.mock_send_mail.mock_calls]
        self.assertIn(self.booked_by.email, sent_emails)
        self.assertIn(self.trailblazer1.email, sent_emails)
        self.assertIn(self.trailblazer2.email, sent_emails)

    def test_confirmation_email_handles_errors_gracefully(self):
        """
        Test that errors in sending emails are handled gracefully.
        """
        # Simulate an error in send_mail
        self.mock_send_mail.side_effect = Exception("Email error")

        try:
            BookingEmailService.send_session_confirmation_email(self.trailblazer_status1)
        except Exception:
            self.fail("send_session_confirmation_email() raised an exception")


class SessionCancellationEmailTest(TestCase):
    def setUp(self):
        # Start patch for email service
        patcher_send_mail = patch('api.bookings.services.email_service.send_mail')
        self.mock_send_mail = patcher_send_mail.start()
        self.addCleanup(patcher_send_mail.stop)

        patcher_create_events = patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
        self.mock_create_events = patcher_create_events.start()
        self.addCleanup(patcher_create_events.stop)

        patcher_delete_events = patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
        self.mock_delete_events = patcher_delete_events.start()
        self.addCleanup(patcher_delete_events.stop)

        # Create test users
        self.booked_by = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.trailblazer1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.trailblazer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )

        # Create test booking
        self.start_time = timezone.now() + timedelta(days=1)
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Test booking",
            creator_status='cancelled'
        )

        # Create trailblazer statuses
        self.trailblazer_status1 = TrailblazerBookingStatus.objects.create(
            booking=self.booking,
            trailblazer=self.trailblazer1,
            status='declined'
        )
        self.trailblazer_status2 = TrailblazerBookingStatus.objects.create(
            booking=self.booking,
            trailblazer=self.trailblazer2,
            status='declined'
        )

    def test_cancellation_email_sent_to_all_recipients(self):
        """
        Test that cancellation emails are sent to all recipients.
        """
        BookingEmailService.send_session_cancellation_email(self.booking)

        # Check that send_mail was called 3 times (one for each recipient)
        self.assertEqual(self.mock_send_mail.call_count, 3)

        # Get the list of recipients from call arguments
        sent_emails = [call.kwargs['recipient_list'][0] for call in self.mock_send_mail.mock_calls]
        self.assertIn(self.booked_by.email, sent_emails)
        self.assertIn(self.trailblazer1.email, sent_emails)
        self.assertIn(self.trailblazer2.email, sent_emails)

    def test_cancellation_email_handles_errors_gracefully(self):
        """
        Test that errors in sending emails are handled gracefully.
        """
        # Simulate an error in send_mail
        self.mock_send_mail.side_effect = Exception("Email error")

        try:
            BookingEmailService.send_session_cancellation_email(self.booking)
        except Exception:
            self.fail("send_session_cancellation_email() raised an exception")