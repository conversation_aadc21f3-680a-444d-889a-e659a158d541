import React, { useEffect, useState } from 'react';
import { useAuth } from '@/context/AuthProvider';
import {
    Table,
    TableBody,
    TableCell,
    TableContainer,
    TableHead,
    TableRow,
    Paper,
    Typography,
  } from '@mui/material';
  import { Skeleton } from "@/components/ui/skeleton";


const PaymentHistory = ({ user, userLoading, userError }) => {
  const [paymentLogs, setPaymentLogs] = useState([]);
  const { getToken } = useAuth(); 
  const [isLoading, setIsLoading] = useState(true);

   // Function to sort and slice the payment logs
   const getRecentLogs = (logs) => {
    return logs
      .sort((a, b) => new Date(b.payment_date) - new Date(a.payment_date))
      .slice(0, 10);
  };

  useEffect(() => {

    if (userLoading || userError) return;

    const fetchPaymentLogs = async () => {

      try {
        const authToken = getToken();
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/payments/payment-logs/user/?user=${user.id}`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${authToken}`,
          },
        });
        if (response.ok) {
          const data = await response.json();
          const recentLogs = getRecentLogs(data['results']);
          setPaymentLogs(recentLogs);
        }
      } catch (error) {
        console.error('Error fetching payment logs:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchPaymentLogs();
  }, [userLoading, userError]);

  const calculatePaymentPeriod = (paymentDate) => {
    const date = new Date(paymentDate);
    const dayOfWeek = date.getDay();
    const previousMonday = new Date(date);
    previousMonday.setDate(date.getDate() - ((dayOfWeek + 6) % 7 + 7));
    const previousSunday = new Date(previousMonday);
    previousSunday.setDate(previousMonday.getDate() + 6);

    return `${previousMonday.toLocaleDateString()} - ${previousSunday.toLocaleDateString()}`;
  };

  return (
      <TableContainer component={Paper} sx={{ mt: 4 }}>
      <Typography variant="h6" component="div" sx={{ padding: 2 }}>
        Payment History
      </Typography>
      {isLoading ? (
        <div>
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-10 mb-2" />
          <Skeleton className="h-10 mb-2" />
        </div>
      ) : (
      <Table>
        <TableHead>
          <TableRow>
            <TableCell>Payment Date</TableCell>
            <TableCell>Payment Period</TableCell>
            <TableCell>Hours</TableCell>
            <TableCell>Amount</TableCell>
          </TableRow>
        </TableHead>
        <TableBody>
          {paymentLogs.map((log) => (
            <TableRow key={log.id}>
            <TableCell sx={{ width: '15%' }}>{new Date(log.payment_date).toLocaleDateString()}</TableCell>
            <TableCell sx={{ width: '15%' }}>{calculatePaymentPeriod(log.payment_date)}</TableCell>
            <TableCell sx={{ width: '15%' }}>{log.hours}</TableCell>
            <TableCell sx={{ width: '15%' }}>{log.amount}</TableCell>
          </TableRow>
          ))}
        </TableBody>
      </Table>
      )}
    </TableContainer>
  );
};

export default PaymentHistory;