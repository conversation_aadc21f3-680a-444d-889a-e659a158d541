volumes:
  909ba300__trailblazer_v1.1_updated_backend_local_postgres_data: {}
  909ba300__trailblazer_v1.1_updated_backend_local_postgres_data_backups: {}

services:
  backend:
    build:
      context: ./backend
      dockerfile: ./compose/Dockerfile
    image: a7a37880__trailblazer-v3_backend_django_staging
    container_name: a7a37880__trailblazer-v3_backend_django_staging
    volumes:
      - ./backend:/app
    env_file:
      - ./backend/.envs/.local/.django
      - ./backend/.envs/.local/.postgres
    ports:
      - '${BACKEND_PORT:-8002}:8000'
    command: /start

  frontend:
    build:
      context: ./frontend
      dockerfile: ./compose/Dockerfile
    image: a7a37880__trailblazer-v3_frontend_nextjs_staging
    container_name: a7a37880__trailblazer-v3_frontend_nextjs_staging
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      # Don't mount node_modules and .next to avoid conflicts
      - /app/node_modules
      - /app/.next
    ports:
      - '${FRONTEND_PORT:-3002}:3000'
    command: "npm run dev"
