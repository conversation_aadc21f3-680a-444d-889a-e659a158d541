import django_filters
from django.db.models import Q
from rest_framework.exceptions import ValidationError
from api.users.models import User

DAY_CHOICES = [
    'sunday',
    'monday',
    'tuesday',
    'wednesday',
    'thursday',
    'friday',
    'saturday',
]


class CollegeStudentFilter(django_filters.FilterSet):
    university_tags = django_filters.CharFilter(method='filter_multiple_fields')
    major = django_filters.CharFilter(method='filter_multiple_fields')
    interests = django_filters.CharFilter(method='filter_multiple_fields')
    graduation_year = django_filters.CharFilter(method='filter_multiple_fields')
    day = django_filters.CharFilter(method='filter_by_day')
    organization_tags = django_filters.CharFilter(method='filter_multiple_fields')

    class Meta:
        model = User
        fields = ['university_tags', 'major', 'interests', 'day', 'graduation_year', 'organization_tags']

    def filter_multiple_fields(self, queryset, name, value):
        """
        Filters the queryset based on multiple comma-separated values for the specified field.
        """
        values = value.split(',')  # Split comma-separated values
        field_mapping = {
            'university_tags': 'college_student_profile__university_tags',
            'major': 'college_student_profile__college_major',
            'interests': 'college_student_profile__interests',
            'graduation_year': 'college_student_profile__graduation_year',
            'organization_tags': 'college_student_profile__organization_tags__id',  # Use __id for Many-to-Many
        }
        field_name = field_mapping.get(name)

        if not field_name:
            return queryset  # If the field is not recognized, return the queryset unfiltered

        # Use __in for Many-to-Many fields and exact matches
        if name == 'organization_tags':
            return queryset.filter(**{f"{field_name}__in": [val.strip() for val in values]}).distinct()

        # Use __icontains for other fields
        filters = Q()
        for val in values:
            filters |= Q(**{f"{field_name}__icontains": val.strip()})

        return queryset.filter(filters)

    def filter_by_day(self, queryset, name, value):
        """
        Filters the queryset based on availability on specific days.
        """
        days = value.lower().split(',')
        invalid_days = [day for day in days if day not in DAY_CHOICES]

        if invalid_days:
            raise ValidationError(
                f"Invalid day(s): {', '.join(invalid_days)}. "
                f"Valid options are: {', '.join(DAY_CHOICES)}."
            )

        # Build a dynamic filter query for each day
        filters = Q()
        for day in days:
            availability_field = f"{day}_available"
            filters |= Q(**{f"availability__{availability_field}": True})

        # Filter the queryset by the availability on the specified days
        return queryset.filter(filters).exclude(availability__isnull=True)
