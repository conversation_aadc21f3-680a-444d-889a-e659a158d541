"use client"

import Link from 'next/link'
import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { But<PERSON> } from "@/components/ui/button"
import { Head<PERSON> } from "@/components/ui/header"
import { IntroBanner } from "@/components/ui/introBanner"
import { Circle, CircleCheckBig } from 'lucide-react'
import withoutAuth from '@/hoc/withoutAuth';

// RightSection component
const RightSection = () => {
    const router = useRouter()
    const [selectedRole, setSelectedRole] = useState(null)

    const handleRoleSelection = (role) => {
        setSelectedRole(role)
        
        // Navigate to the appropriate sign-up flow
        const routesMap = {
            counselor: '/counselor/signup',
            student: '/college-student/signup'
        }
        router.push(routesMap[role])
    }
    return (
        <div className="p-8 md:w-1/2 lg:w-6/12 flex flex-col justify-center items-center" id="right-section">
            <div className="w-full max-w-md" id="content">
                <h1 className="text-3xl font-bold mb-8" id="question">
                    What brings you here?
                </h1>
                <div className="space-y-6" id="options">
                    <Button
                        variant="outline"
                        className="whitespace-normal w-full p-4 py-8 text-left flex border border-gray-300 rounded-lg justify-start hover:bg-gray-100 transition focus:ring-2 focus:ring-green-500 focus:bg-green-100"
                        id="counselor-option"
                        onClick={() => handleRoleSelection('counselor')}
                    >
                        {selectedRole && selectedRole === 'counselor' ?
                            (
                                <CircleCheckBig
                                    size={18}
                                    strokeWidth={2.5}
                                    className="text-green-500 ml-3 mr-4"
                                />
                            ) : (
                                <Circle
                                    size={18}
                                    strokeWidth={1.5}
                                    className="text-gray-400 ml-3 mr-4"
                                />
                            )
                        }
                        I&apos;m a counselor or non-profit administrator
                    </Button>
                    <Button
                        variant="outline"
                        className="whitespace-normal w-full p-4 py-8 text-left flex border border-gray-300 rounded-lg justify-start hover:bg-gray-100 transition focus:ring-2 focus:ring-green-500 focus:bg-green-100"
                        id="student-option"
                        onClick={() => handleRoleSelection('student')}
                    >
                        {selectedRole && selectedRole === 'student' ?
                            (
                                <CircleCheckBig
                                    size={18}
                                    strokeWidth={2.5}
                                    className="text-green-500 ml-3 mr-4"
                                />
                            ) : (
                                <Circle
                                    size={18}
                                    strokeWidth={1.5}
                                    className="text-gray-400 ml-3 mr-4"
                                />
                            )
                        }
                        I&apos;m a college student
                    </Button>
                    <p className="mt-4 text-center text-sm text-gray-600">
                        Already have an account?
                        <Link href="/login" className="font-medium text-green-600 hover:text-green-500 ml-1">
                            Log In
                        </Link>
                    </p>
                </div>
            </div>
        </div>
    )
}

// Main LandingPage component
const LandingPage = () => {
    return (
        <div className="min-h-screen flex flex-col">
            <Header />
            <main className="flex-grow flex flex-col md:flex-row" id="main-content">
                <IntroBanner />
                <RightSection />
            </main>
        </div>
    )
}

export default withoutAuth(LandingPage)
