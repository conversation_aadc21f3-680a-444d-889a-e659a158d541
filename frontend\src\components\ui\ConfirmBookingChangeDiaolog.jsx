import { Dialog, DialogContent, Di<PERSON>Header, Di<PERSON>Title, Di<PERSON>Footer } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
const ConfirmBookingChangeDialog = ({ isOpen, onClose, onConfirm, time, isLoading }) => (
    <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent>
            <DialogHeader>
                <DialogTitle>Confirm Booking Change</DialogTitle>
            </DialogHeader>
            <p>Are you sure you want to reschedule the booking to {time}?</p>
            <DialogFooter>
                {isLoading ? (<Loader2 className="mr-2 w-4 h-4 md:w-6 md:h-6 animate-spin" />) :
                    (
                        <><Button variant="outline" onClick={onClose}>Cancel</Button><Button onClick={onConfirm}>Confirm</Button></>
                    )
                }
            </DialogFooter>
        </DialogContent>
    </Dialog>
);

export default ConfirmBookingChangeDialog;