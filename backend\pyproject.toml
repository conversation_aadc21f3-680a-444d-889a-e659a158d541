# ==== pytest ====
[tool.pytest.ini_options]
minversion = "6.0"
addopts = "--ds=api.settings.test --reuse-db --import-mode=importlib" 
python_files = [
    "tests.py",
    "test_*.py",
]

# ==== Coverage ====
[tool.coverage.run]
include = ["api/**"]
omit = ["*/migrations/*", "*/tests/*"]
plugins = ["django_coverage_plugin"]

# ==== mypy ====
[tool.mypy]
python_version = "3.12"
check_untyped_defs = true
ignore_missing_imports = true
warn_unused_ignores = true
warn_redundant_casts = true
warn_unused_configs = true
plugins = [
    "mypy_django_plugin.main",
    "mypy_drf_plugin.main",
]

[[tool.mypy.overrides]]
# Django migrations should not produce any errors:
module = "*.migrations.*"
ignore_errors = true

[tool.django-stubs]
django_settings_module = "api.settings.test"

# ==== djLint ====
[tool.djlint]
blank_line_after_tag = "load,extends"
close_void_tags = true
format_css = true
format_js = true
# TODO: remove T002 when fixed https://github.com/djlint/djLint/issues/687
ignore = "H006,H030,H031,T002"
include = "H017,H035"
indent = 2
max_line_length = 119
profile = "django"

[tool.djlint.css]
indent_size = 2

[tool.djlint.js]
indent_size = 2

[tool.ruff]
# Exclude a variety of commonly ignored directories.
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
    "*/migrations/*.py",
    "staticfiles/*"
]
# Same as Django: https://github.com/cookiecutter/cookiecutter-django/issues/4792.
line-length = 88
indent-width = 4
target-version = "py312"

[tool.ruff.lint]
select = [
  "F",
  "E",
  "W",
  "C90",
  "I",
  "N",
  "UP",
  "YTT",
  # "ANN", # flake8-annotations: we should support this in the future but 100+ errors atm
  "ASYNC",
  "S",
  "BLE",
  "FBT",
  "B",
  "A",
  "COM",
  "C4",
  "DTZ",
  "T10",
  "DJ",
  "EM",
  "EXE",
  "FA",
  'ISC',
  "ICN",
  "G",
  'INP',
  'PIE',
  "T20",
  'PYI',
  'PT',
  "Q",
  "RSE",
  "RET",
  "SLF",
  "SLOT",
  "SIM",
  "TID",
  "TCH",
  "INT",
  # "ARG", # Unused function argument
  "PTH",
  "ERA",
  "PD",
  "PGH",
  "PL",
  "TRY",
  "FLY",
  # "NPY",
  # "AIR",
  "PERF",
  # "FURB",
  # "LOG",
  "RUF"
]
ignore = [
  "S101", # Use of assert detected https://docs.astral.sh/ruff/rules/assert/
  "RUF012", # Mutable class attributes should be annotated with `typing.ClassVar`
  "SIM102", # sometimes it's better to nest
  "UP038" # Checks for uses of isinstance/issubclass that take a tuple 
          # of types for comparison.
          # Deactivated because it can make the code slow: 
          # https://github.com/astral-sh/ruff/issues/7871
]
# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []
# The fixes in extend-unsafe-fixes will require 
# provide the `--unsafe-fixes` flag when fixing.
extend-unsafe-fixes = [
    "UP038"
]
# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
skip-magic-trailing-comma = false
line-ending = "auto"

[tool.ruff.lint.isort]
force-single-line = true
