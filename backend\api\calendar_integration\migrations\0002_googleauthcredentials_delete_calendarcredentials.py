# Generated by Django 4.2.13 on 2024-11-05 15:01

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('calendar_integration', '0001_initial'),
    ]

    operations = [
        migrations.CreateModel(
            name='GoogleAuthCredentials',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.TextField()),
                ('refresh_token', models.TextField()),
                ('token_uri', models.CharField(max_length=200)),
                ('client_id', models.CharField(max_length=200)),
                ('client_secret', models.Char<PERSON>ield(max_length=200)),
                ('scopes', models.TextField()),
                ('expiry', models.DateTimeField()),
                ('user', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
        ),
        migrations.DeleteModel(
            name='CalendarCredentials',
        ),
    ]
