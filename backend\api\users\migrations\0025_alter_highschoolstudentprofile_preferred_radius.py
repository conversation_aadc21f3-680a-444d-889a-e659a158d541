# Generated by Django 4.2.13 on 2025-06-03 03:26

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0024_highschoolstudentprofile_act_score_and_more'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='highschoolstudentprofile',
            name='preferred_radius',
            field=models.CharField(blank=True, choices=[('0-20 miles (short drive)', '0-20 miles (short drive)'), ('21-50 miles (medium drive)', '21-50 miles (medium drive)'), ('51-200 miles (long drive)', '51-200 miles (long drive)'), ('201-500 miles (short flight)', '201-500 miles (short flight)'), ('501+ miles (long flight)', '501+ miles (long flight)')], help_text='Preferred radius from ZIP code', max_length=50, null=True),
        ),
    ]
