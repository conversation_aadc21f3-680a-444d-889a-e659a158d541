"use client"

import { useState, useMemo } from 'react'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { useRouter } from 'next/navigation'

import { useForm } from "react-hook-form"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"

import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { ArrowRight } from 'lucide-react'
import { getNextOnboardingStep } from "@/lib/utils"
import { AvatarSelection, avatarToFile } from "@/components/ui/avatarSelection"
import { Loader2 } from 'lucide-react'


// Define the form schema with Zod
const formSchema = z.object({
    firstName: z.string().min(2, { message: "First name is required" }),
    lastName: z.string().min(2, { message: "Last name is required" }),
    avatar: z.string().min(1, { message: "Avatar is required" }),
    schoolYear: z.string({ required_error: "Grade level is required" })
})

// Profile Completion Form component
const ProfileCompletionForm = () => {
    const router = useRouter()
    const [selectedAvatar, setSelectedAvatar] = useState('')
    const [userType, setUserType] = useState('highSchool')
    const [isLoading, setIsLoading] = useState(false)
    const { getToken, updateAuthenticatedUser } = useAuth()

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: "",
            lastName: "",
            avatar: "",
            schoolYear: ""
        }
    })

    const schoolYearOptions = useMemo(() => {
        const currentYear = new Date().getFullYear()
        const years = Array.from({ length: 9 }, (_, i) => currentYear + i)
        return years.map((year) => ({ value: year.toString(), label: year.toString() }))
    }, [])

    const onSubmit = async (data) => {
        setIsLoading(true)
        const authToken = getToken()
        try {
            const formData = new FormData()
            formData.append('step', 'basic_info')
            formData.append('first_name', data.firstName)
            formData.append('last_name', data.lastName)
            formData.append('grade_level', data.schoolYear)

            // Fetch the avatar image as a File
            const avatarFile = await avatarToFile(data.avatar)

            // Append the avatar file to the form data
            formData.append('avatar', avatarFile)
            
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/high-school-students/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Token ${authToken}`
                },
                body: formData
            })
                        
            if (!response.ok) {
                Object.keys(result).forEach((key) => {
                    form.setError(key, { message: result[key][0] })
                })
                setIsLoading(false)
            } else {
                await updateAuthenticatedUser()
                router.push(getNextOnboardingStep('HighSchoolStudent', 'profile'))
            }
        } catch (error) {
            // Handle network errors    
            console.error('An unexpected error happened:', error)
            form.setError('form', { message: 'An unexpected error happened. Please try again.' })
            setIsLoading(false)
        }
    }

    return (
        <div className="">
            <h1 className="text-4xl font-bold mb-8">Complete your profile</h1>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>First Name*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your first name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Last Name*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your last name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="schoolYear"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>What year will you graduate high school?*</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a school year" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {schoolYearOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="avatar"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Pick an avatar</FormLabel>
                                <FormControl>
                                    <AvatarSelection
                                        onSelect={(avatar) => {
                                            setSelectedAvatar(avatar)
                                            field.onChange(avatar)
                                        }}
                                        selectedAvatar={selectedAvatar}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div>
                        <Button
                            type="submit"
                            className="mt-8"
                            disabled={isLoading}
                        >
                            {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Complete Profile <ArrowRight className="ml-2 w-6 h-6" />
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    )
}

// Main Page component
const ProfileCompletionPage = () => {
    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="w-full md:w-5/6 lg:w-5/6">
                    <ProfileCompletionForm />
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(ProfileCompletionPage)
