from google.oauth2.credentials import Credentials as UserCredentials
from google.oauth2.service_account import Credentials as ServiceAccountCredentials
from google_auth_oauthlib.flow import Flow
from google.auth.transport.requests import Request
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError

import json
import logging
import requests
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception, RetryError

from django.conf import settings
from django.utils import timezone

from api.calendar_integration.models import MasterGoogleCredentials, CalendarEvent

logger = logging.getLogger(__name__)


def should_retry_exception(exception):
    """Return True if we should retry (i.e., not a 403), False otherwise."""
    if isinstance(exception, HttpError):
        if exception.resp.status == 403:
            return False  # Do not retry on 403 Forbidden errors
    return True

def refresh_token(client_id, client_secret, refresh_token):
    token_url = "https://oauth2.googleapis.com/token"
    data = {
        "client_id": client_id,
        "client_secret": client_secret,
        "refresh_token": refresh_token,
        "grant_type": "refresh_token"
    }
    response = requests.post(token_url, data=data)
    response.raise_for_status()
    return response.json()


class CalendarService:
    def __init__(self):
        self.credentials = None
        self.service = None
        self.calendar_id = settings.GOOGLE_CALENDAR_ID
        self._load_credentials()

    def _load_credentials(self):
        try:
            creds = MasterGoogleCredentials.get_credentials()
            if creds.service_account_enabled:
                self._load_service_account_credentials()
            else:
                self._load_user_credentials(creds)
        except Exception as e:
            logger.error(f"Error loading credentials: {str(e)}")
            raise ValueError(f"Failed to load Google Calendar credentials: {str(e)}")

    def _load_user_credentials(self, creds):
        try:
            # Initialize Google Credentials object
            self.credentials = UserCredentials.from_authorized_user_info(
                creds.to_credentials_dict(),
                scopes=settings.GOOGLE_CALENDAR_SCOPES
            )
            
            # Refresh token if necessary
            if not self.credentials.valid or self.credentials.expired:
                if not self.credentials.refresh_token:
                    raise RuntimeError("Missing refresh_token. Cannot refresh access token.")
                
                refreshed_token = refresh_token(
                    creds.client_id,
                    creds.client_secret,
                    creds.refresh_token
                )

                # Update self.credentials with the new token and expiry
                self.credentials.token = refreshed_token['access_token']
                self.credentials.expiry = timezone.make_naive(
                    timezone.now() + timezone.timedelta(seconds=refreshed_token['expires_in']),
                    timezone.utc
                )
                
                # Save updated credentials to the database
                creds.token = refreshed_token['access_token']
                creds.expiry = self.credentials.expiry
                creds.save()

            return self.credentials  # Return updated credentials for consistency
        except Exception as e:
            logger.error(f"Error refreshing token: {str(e)}")
            if 'creds' in locals():
                creds.error = str(e)
                creds.save()
            raise

    def _load_service_account_credentials(self):
        # Load service account key
        service_account_info = settings.GOOGLE_SERVICE_ACCOUNT_INFO
        
        # If it's a string, parse it as JSON
        if isinstance(service_account_info, str):
            service_account_info = json.loads(service_account_info)

        self.credentials = ServiceAccountCredentials.from_service_account_info(service_account_info)

    def _build_service(self):
        if not self.service:
            self.service = build('calendar', 'v3', credentials=self.credentials)
        return self.service

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception(should_retry_exception)
    )
    def create_individual_event(self, summary, description, start_time, end_time, user, include_meet_link=False, location=None):
        """Create a calendar event for a single user, optionally with a Google Meet link and custom location."""
        try:
            service = self._build_service()
            event_body = {
                'summary': summary,
                'description': description,
                'start': {
                    'dateTime': start_time.isoformat(),
                    'timeZone': settings.TIME_ZONE,
                },
                'end': {
                    'dateTime': end_time.isoformat(),
                    'timeZone': settings.TIME_ZONE,
                },
                'attendees': [{'email': user.email}],
                'reminders': {'useDefault': True},
            }

            # Include Google Meet conference data if requested
            if include_meet_link:
                event_body['conferenceData'] = {
                    'createRequest': {
                        'requestId': f"meet_{start_time.timestamp()}_{user.id}",
                        'conferenceSolutionKey': {'type': 'hangoutsMeet'}
                    }
                }

            # Add the location (e.g., Google Meet link)
            if location:
                event_body['location'] = location

            event = service.events().insert(
                calendarId=self.calendar_id,
                body=event_body,
                sendUpdates='all',
                conferenceDataVersion=1 if include_meet_link else 0
            ).execute()

            return event
        except HttpError as error:
            if error.resp.status == 403:
                logger.error(f"Permission denied for user {user.email}. Cannot create event.")
            else:
                logger.exception(f"Error creating calendar event for {user.email}: {error}")
            raise
        except Exception as error:
            # Catch all other exceptions
            logger.exception(f"Unexpected error creating calendar event for {user.email}: {error}")
            raise

    def create_events_for_booking(self, booking):
        """Create separate calendar events for each participant in a booking."""

        # Get primary event summary
        trailblazer_names = [f"{trailblazer.first_name} {trailblazer.last_name}" for trailblazer in booking.trailblazers]
        # Format the names as a string separated by commas, with "and" before the last name
        if len(trailblazer_names) > 1:
            formatted_names = ", ".join(trailblazer_names[:-1]) + f", and {trailblazer_names[-1]}"
        else:
            formatted_names = trailblazer_names[0] if trailblazer_names else ""

        created_events = []
        primary_summary = f"Trailblazer Meeting with {formatted_names}"
        description = booking.message
        start_time = booking.start_time
        end_time = booking.end_time

        # Step 1: Create the primary event for `booked_by` user with conferenceData (to generate the Meet link)
        try:
            primary_event = self.create_individual_event(
                primary_summary,
                description,
                start_time,
                end_time,
                booking.booked_by,
                include_meet_link=True
            )

            # Extract the Google Meet link from the primary event
            meet_link = primary_event.get('conferenceData', {}).get('entryPoints', [{}])[0].get('uri')

            # Save the Meet link in the Booking instance
            booking.meet_link = meet_link
            booking.save()

            # Save the primary event in the database without the Meet link (now in Booking)
            CalendarEvent.objects.create(
                calendar_event_id=primary_event['id'],
                booking=booking,
                user=booking.booked_by
            )

            created_events.append(primary_event)

            # Step 2: Create separate events for each trailblazer using the Meet link as the location
            trailblazers = booking.trailblazers
            trailblazers_summary = f"Trailblazer Meeting with {booking.booked_by.first_name} {booking.booked_by.last_name}"
            for trailblazer in trailblazers:
                try:
                    event = self.create_individual_event(
                        trailblazers_summary,
                        description,
                        start_time,
                        end_time,
                        trailblazer,
                        include_meet_link=False,
                        location=meet_link  # Add the Meet link as the location
                    )

                    # Save each trailblazer event in the database
                    CalendarEvent.objects.create(
                        calendar_event_id=event['id'],
                        booking=booking,
                        user=trailblazer
                    )

                    created_events.append(event)
                except Exception as e:
                    logger.exception(f"Failed to create event for {trailblazer.email}: {e}")

        except RetryError as e:
            underlying_exception = e.last_attempt.exception()
            logger.exception(f"Failed to create primary event for {booking.booked_by.email}: {underlying_exception}")
        except Exception as e:
            logger.exception(f"Failed to create primary event for {booking.booked_by.email}: {e}")

        return created_events

    @retry(stop=stop_after_attempt(3), wait=wait_exponential(multiplier=1, min=4, max=10))
    def delete_event(self, event_id):
        """Delete a calendar event."""
        try:
            service = self._build_service()
            service.events().delete(
                calendarId=self.calendar_id,
                eventId=event_id,
                sendUpdates='all'
            ).execute()
            return True
        except HttpError as error:
            if error.resp.status == 404:
                logger.warning(f"Event with ID {event_id} not found. Skipping deletion.")
                return False
            else:
                logger.error(f"Unexpected HTTP error while deleting event {event_id}: {error}")
                raise
        except Exception as e:
            logger.error(f"Unexpected error in delete_event for event {event_id}: {str(e)}")
            raise

    def delete_events_for_booking(self, booking):
        """Delete all calendar events associated with a booking."""
        events = CalendarEvent.objects.filter(booking=booking)
        for event in events:
            try:
                self.delete_event(event.calendar_event_id)
                event.delete()
            except Exception as e:
                logger.error(f"Failed to delete event {event.calendar_event_id} for booking {booking.id}: {str(e)}")
