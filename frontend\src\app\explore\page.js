"use client"

import React, { useState, useEffect } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"

import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import DashboardLayout from "@/components/layouts/DashboardLayout"

import {
    MessageCircle, Users, RotateCcw, SearchCheck, Loader2, SearchX, Search, Bookmark
} from 'lucide-react'
import { useShortlist } from '@/hooks/useShortlist'
import { useAuth } from '@/context/AuthProvider'

// Dummy data for colleges - used for demonstration purposes
const DUMMY_COLLEGES = [
    { id: 'stanford', name: 'Stanford University', location: 'Stanford, CA', type: 'Private', students: 7800, bookmarked: false },
    { id: 'mit', name: 'Massachusetts Institute of Technology', location: 'Cambridge, MA', type: 'Private', students: 4600, bookmarked: true },
    { id: 'ucberkeley', name: 'University of California, Berkeley', location: 'Berkeley, CA', type: 'Public', students: 32000, bookmarked: false },
    { id: 'harvard', name: 'Harvard University', location: 'Cambridge, MA', type: 'Private', students: 7100, bookmarked: false },
    { id: 'ucla', name: 'University of California, Los Angeles', location: 'Los Angeles, CA', type: 'Public', students: 31600, bookmarked: true }
]

// US States data for the dropdown filter
const US_STATES = [
  { value: "AL", label: "Alabama" },
  { value: "AK", label: "Alaska" },
  { value: "AS", label: "American Samoa" },
  { value: "AZ", label: "Arizona" },
  { value: "AR", label: "Arkansas" },
  { value: "CA", label: "California" },
  { value: "CO", label: "Colorado" },
  { value: "CT", label: "Connecticut" },
  { value: "DE", label: "Delaware" },
  { value: "DC", label: "District of Columbia" },
  { value: "FL", label: "Florida" },
  { value: "GA", label: "Georgia" },
  { value: "GU", label: "Guam" },
  { value: "HI", label: "Hawaii" },
  { value: "ID", label: "Idaho" },
  { value: "IL", label: "Illinois" },
  { value: "IN", label: "Indiana" },
  { value: "IA", label: "Iowa" },
  { value: "KS", label: "Kansas" },
  { value: "KY", label: "Kentucky" },
  { value: "LA", label: "Louisiana" },
  { value: "ME", label: "Maine" },
  { value: "MD", label: "Maryland" },
  { value: "MA", label: "Massachusetts" },
  { value: "MI", label: "Michigan" },
  { value: "MN", label: "Minnesota" },
  { value: "MS", label: "Mississippi" },
  { value: "MO", label: "Missouri" },
  { value: "MT", label: "Montana" },
  { value: "NE", label: "Nebraska" },
  { value: "NV", label: "Nevada" },
  { value: "NH", label: "New Hampshire" },
  { value: "NJ", label: "New Jersey" },
  { value: "NM", label: "New Mexico" },
  { value: "NY", label: "New York" },
  { value: "NC", label: "North Carolina" },
  { value: "ND", label: "North Dakota" },
  { value: "MP", label: "Northern Mariana Islands" },
  { value: "OH", label: "Ohio" },
  { value: "OK", label: "Oklahoma" },
  { value: "OR", label: "Oregon" },
  { value: "PA", label: "Pennsylvania" },
  { value: "PR", label: "Puerto Rico" },
  { value: "RI", label: "Rhode Island" },
  { value: "SC", label: "South Carolina" },
  { value: "SD", label: "South Dakota" },
  { value: "TN", label: "Tennessee" },
  { value: "TX", label: "Texas" },
  { value: "UT", label: "Utah" },
  { value: "VT", label: "Vermont" },
  { value: "VA", label: "Virginia" },
  { value: "VI", label: "U.S. Virgin Islands" },
  { value: "WA", label: "Washington" },
  { value: "WV", label: "West Virginia" },
  { value: "WI", label: "Wisconsin" },
  { value: "WY", label: "Wyoming" },
]

// Zod schema for form validation
const searchFormSchema = z.object({
  searchByNameLocation: z.string().optional(),
  searchState: z.string().optional(),
})

// Connect with Trailblazers Card Component
const ConnectStudentsCard = () => {
  return (
    <div className="my-6">
      <div className="bg-green-50 border border-green-200 p-5 shadow-md rounded-lg">
        <div className="md:flex md:items-center md:justify-between">
          <div className="md:flex md:items-center">
            <div className="hidden md:flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary mr-3">
              <MessageCircle className="w-4 h-4" />
            </div>
            <div className="flex-grow">
              <h3 className="text-lg font-semibold text-gray-800 mb-1 md:mb-0">Unlock Student Insights</h3>
              <p className="text-gray-700 text-sm mb-3 md:mb-0">Chat with current students from various colleges to get firsthand advice.</p>
            </div>
          </div>
          <div className="hidden md:flex ml-4 mr-4 shrink-0">
            <div className="flex -space-x-2">
              <Avatar className="w-8 h-8 border-2 border-white">
                <AvatarImage src="/avatars/boy_1.png" alt="Student 1" />
                <AvatarFallback>S1</AvatarFallback>
              </Avatar>
              <Avatar className="w-8 h-8 border-2 border-white">
                <AvatarImage src="/avatars/boy_6.png" alt="Student 2" />
                <AvatarFallback>S2</AvatarFallback>
              </Avatar>
              <Avatar className="w-8 h-8 border-2 border-white">
                <AvatarImage src="/avatars/boy_8.png" alt="Student 3" />
                <AvatarFallback>S3</AvatarFallback>
              </Avatar>
               <Avatar className="w-8 h-8 border-2 border-white">
                <AvatarImage src="/avatars/boy_4.png" alt="Student 4" />
                <AvatarFallback>S4</AvatarFallback>
              </Avatar>
            </div>
          </div>
          <Button asChild className="bg-primary text-white hover:bg-green-500 hover:text-white w-full md:w-auto mt-3 md:mt-0">
            <Link href="/paywall">
              <Users className="w-4 h-4 mr-2" />
              Connect with Students
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

// Search Form Component with name/location input and state filter
const ExploreSearchForm = ({ onSearch, onReset }) => {
  const form = useForm({
    resolver: zodResolver(searchFormSchema),
    defaultValues: {
      searchByNameLocation: "",
      searchState: "all",
    },
  })

  // Handle form reset functionality
  const handleReset = () => {
    form.reset({
      searchByNameLocation: "",
      searchState: "all",
    })
    onReset()
  }

  return (
    <div className="bg-white p-6 rounded-lg shadow-md">
      <Form {...form}>
        <form onSubmit={form.handleSubmit(onSearch)} className="space-y-4 md:space-y-0 md:grid md:grid-cols-3 md:gap-4 md:items-end">
          {/* Name/Location Search Input */}
          <div className="md:col-span-2">
            <FormField
              control={form.control}
              name="searchByNameLocation"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-sm font-medium text-gray-700 mb-1">Search by Name or Location</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="e.g., Stanford, Boston, MA" 
                      {...field} 
                      className="w-full py-3 px-4 border-gray-300 rounded-md focus:border-primary focus:ring-primary/50 focus:ring-2" 
                    />
                  </FormControl>
                </FormItem>
              )}
            />
          </div>
          
          {/* State Filter Dropdown */}
          <div>
            <FormField
              control={form.control}
              name="searchState"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-sm font-medium text-gray-700 mb-1">State</FormLabel>
                  <Select onValueChange={field.onChange} value={field.value || "all"}>
                    <FormControl>
                      <SelectTrigger className="w-full py-3 px-4 border-gray-300 rounded-md focus:border-primary focus:ring-primary/50 focus:ring-2">
                        <SelectValue placeholder="All States" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="all">All States</SelectItem>
                      {US_STATES.map(state => (
                        <SelectItem key={state.value} value={state.value}>{state.label}</SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </FormItem>
              )}
            />
          </div>
          
          {/* Action Buttons */}
          <div className="md:col-start-3 md:flex md:space-x-2 md:justify-end">
            <Button 
              type="button" 
              variant="secondary" 
              onClick={handleReset} 
              className="bg-gray-200 text-gray-700 hover:bg-gray-300 w-full md:w-auto justify-center mb-2 md:mb-0"
            >
              <RotateCcw className="w-4 h-4 mr-2" />Reset
            </Button>
            <Button 
              type="submit" 
              className="bg-primary text-white hover:bg-green-500 hover:text-white w-full md:w-auto justify-center"
            >
              <Search className="w-4 h-4 mr-2" />Search
            </Button>
          </div>
        </form>
      </Form>
    </div>
  )
}

// Individual College List Item Component
const CollegeListItem = ({ college, onToggleBookmark }) => {
  const [isBookmarked, setIsBookmarked] = useState(college.bookmarked)

  // Handle bookmark toggle functionality
  const handleBookmarkToggle = (e) => {
    e.preventDefault() // Prevent navigation if item is wrapped in Link
    e.stopPropagation()
    setIsBookmarked(!isBookmarked)
    onToggleBookmark(college.id, !isBookmarked)
  }
  
  const tooltipText = isBookmarked ? 'Remove from shortlist' : 'Add to shortlist'

  return (
    <li className="p-4 hover:bg-gray-50">
      <div className="flex items-center justify-between">
        <div>
          <Link href={`/colleges/${college.id}`} className="text-md font-medium text-primary hover:underline">
            {college.name}
          </Link>
          <p className="text-sm text-gray-500">
            {college.location} • {college.type} • {college.students.toLocaleString()} Students
          </p>
        </div>
        <TooltipProvider delayDuration={300}>
          <Tooltip>
            <TooltipTrigger asChild>
              <Button 
                variant="ghost" 
                size="icon" 
                onClick={handleBookmarkToggle} 
                className={`p-1 rounded-md ${isBookmarked ? 'text-primary' : 'text-gray-400 hover:text-primary'}`}
              >
                <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-primary' : ''}`} />
              </Button>
            </TooltipTrigger>
            <TooltipContent>
              <p>{tooltipText}</p>
            </TooltipContent>
          </Tooltip>
        </TooltipProvider>
      </div>
    </li>
  )
}

// Main Explore Page Component
export default function ExplorePage() {
  // State management for colleges and search functionality
  const [colleges, setColleges] = useState(DUMMY_COLLEGES)
  const [filteredColleges, setFilteredColleges] = useState([])
  const [searchStatus, setSearchStatus] = useState('initial') // 'initial', 'loading', 'results', 'noResults'
  const [currentPage, setCurrentPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [nextUrl, setNextUrl] = useState(null)
  const [prevUrl, setPrevUrl] = useState(null)
  const { addCollege, removeCollege } = useShortlist()
  const { getToken } = useAuth()

  // Handle search form submission
  const handleSearch = async (formData = null, pageUrl = null) => {
    setSearchStatus('loading')
  
    try {
      const token = getToken() || localStorage.getItem('trailblazers.auth.token')
      if (!token) {
        router.push('/profile-setup')
        return
      }
  
      let url = null
  
      if (pageUrl && typeof pageUrl === 'string') {
        // Pagination call
        url = pageUrl
      } else if (formData && typeof formData === 'object') {
        // New search
        const searchTerm = formData.searchByNameLocation?.trim() || ""
        const state = formData.searchState === 'all' ? "" : (formData.searchState || "")
        const page = 1
        const page_size = 10
  
        const queryParams = new URLSearchParams({
          search: searchTerm,
          state,
          page: page.toString(),
          page_size: page_size.toString(),
        })
  
        url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/universities?${queryParams.toString()}`
      }
  
      if (!url) {
        throw new Error("No URL available to fetch")
      }
  
      const res = await fetch(url, {
        method: 'GET',
        headers: {
          'Authorization': `Token ${token}`,
        },
      })
  
      if (!res.ok) throw new Error("Failed to fetch colleges")
  
      const dataResponse = await res.json()
  
      const results = dataResponse.results || []
      const formattedResults = results.map(college => ({
        id: college.unitid,
        name: college.name,
        location: college.location,
        type: college.type,
        students: college.undergrad_count,
        bookmarked: college.is_bookmarked,
      }))
  
      setFilteredColleges(formattedResults)
      setSearchStatus(results.length > 0 ? 'results' : 'noResults')
  
      setNextUrl(dataResponse.next)
      setPrevUrl(dataResponse.previous)
      setTotalPages(Math.ceil(dataResponse.count / 10))
  
      const currentUrl = new URL(url)
      const currentPageFromUrl = currentUrl.searchParams.get('page') || '1'
      setCurrentPage(parseInt(currentPageFromUrl))
    } catch (error) {
      console.error(error)
      setSearchStatus('noResults')
    }
  }
  

  // Handle search form reset
  const handleResetSearch = () => {
    setFilteredColleges([])
    setSearchStatus('initial')
  }

  // Handle bookmark toggle for individual colleges
  const handleToggleBookmark = async (collegeId, newBookmarkState) => {
    // Optimistically update local state first
    setColleges(prevColleges =>
      prevColleges.map(c => c.id === collegeId ? { ...c, bookmarked: newBookmarkState } : c)
    )
    setFilteredColleges(prevFiltered =>
      prevFiltered.map(c => c.id === collegeId ? { ...c, bookmarked: newBookmarkState } : c)
    )
    // Update shortcode via API
    if (newBookmarkState) {
      const success = await addCollege(collegeId)
      if (!success) {
        // If API call fails, revert state change
        setColleges(prevColleges =>
          prevColleges.map(c => c.id === collegeId ? { ...c, bookmarked: !newBookmarkState } : c)
        )
        setFilteredColleges(prevFiltered =>
          prevFiltered.map(c => c.id === collegeId ? { ...c, bookmarked: !newBookmarkState } : c)
        )
      }
    } else {
      const success = await removeCollege(collegeId)
      if (!success) {
        // If API call fails, revert state change
        setColleges(prevColleges =>
          prevColleges.map(c => c.id === collegeId ? { ...c, bookmarked: !newBookmarkState } : c)
        )
        setFilteredColleges(prevFiltered =>
          prevFiltered.map(c => c.id === collegeId ? { ...c, bookmarked: !newBookmarkState } : c)
        )
      }
    }
  }
  
  // Initialize component with proper bookmark states
  // useEffect(() => {
  //   handleResetSearch()
  // // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, []) // Run once on mount

  return (
    <>
      <Head>
        <title>Trailblazer V2 - Explore Colleges</title>
      </Head>

      <DashboardLayout>
        <div>
          {/* Page Header */}
          <div className="mb-8">
            <h1 className="text-3xl font-bold text-gray-800 mb-2">Explore Colleges</h1>
            <p className="text-gray-600">Search and filter any U.S. college to learn more and add them to your shortlist.</p>
          </div>

          {/* Connect with Students Card */}
          <ConnectStudentsCard />
          
          {/* Search Form */}
          <ExploreSearchForm onSearch={handleSearch} onReset={handleResetSearch} />

          {/* Results Area with different states */}
          <div className="mt-8">
            {/* Initial State - No search performed */}
            {searchStatus === 'initial' && (
              <Card className="text-center py-10 bg-white rounded-lg shadow-sm">
                <CardContent className="flex flex-col items-center">
                  <SearchCheck className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-gray-500">Enter your search criteria above to find colleges.</p>
                </CardContent>
              </Card>
            )}
            
            {/* Loading State */}
            {searchStatus === 'loading' && (
              <div className="text-center py-10">
                <Loader2 className="w-12 h-12 text-primary mx-auto mb-4 animate-spin" />
                <p className="text-gray-500">Searching for colleges...</p>
              </div>
            )}
            
            {/* No Results State */}
            {searchStatus === 'noResults' && (
               <Card className="text-center py-10 bg-white rounded-lg shadow-sm">
                <CardContent className="flex flex-col items-center">
                  <SearchX className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                  <p className="text-xl font-semibold text-gray-700 mb-2">No Colleges Found</p>
                  <p className="text-gray-500">Try adjusting your search terms or filters.</p>
                </CardContent>
              </Card>
            )}
            
            {/* Results State - Display search results */}
            {searchStatus === 'results' && (
              <>
              <Card className="bg-white rounded-lg shadow-sm overflow-hidden">
                <ul className="divide-y divide-gray-200">
                  {filteredColleges.map(college => (
                    <CollegeListItem 
                      key={college.id} 
                      college={college} 
                      onToggleBookmark={handleToggleBookmark} 
                    />
                  ))}
                </ul>
              </Card>
              {(totalPages > 1) && (<div className="flex justify-between items-center mt-4 px-4">
                <Button 
                  variant="outline" 
                  onClick={() => handleSearch(null, prevUrl)} 
                  disabled={!prevUrl}
                >
                  ← Previous
                </Button>
                <span className="text-sm text-gray-600">
                  Page {currentPage} of {totalPages}
                </span>
                <Button 
                  variant="outline" 
                  onClick={() => handleSearch(null, nextUrl)} 
                  disabled={!nextUrl}
                >
                  Next →
                </Button>
              </div>)}
              </>
            )}
          </div>
        </div>
      </DashboardLayout>
    </>
  )
}