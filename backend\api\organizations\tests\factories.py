import factory
import faker
from api.organizations.models import Organization

fake = faker.Faker()


class OrganizationFactory(factory.django.DjangoModelFactory):
    class Meta:
        model = Organization

    id = factory.Faker('uuid4')
    name = factory.Faker('company')
    total_subscribed_hours = factory.Faker('random_int', min=0, max=10000)
    per_student_hour_cap = factory.Faker('random_int', min=0, max=100)
    total_hours_used = factory.Faker('random_int', min=0, max=10000)
    zip_code = factory.Faker('zipcode')
    city = factory.Faker('city')
    state = factory.Faker('state')
    school_district = factory.Faker('company')
    contact_phone = factory.LazyFunction(lambda: fake.phone_number()[:20])
    contact_email = factory.Faker('email')
    registration_number = factory.Faker('ean13')
    created_at = factory.LazyFunction(lambda: fake.date_time())
    updated_at = factory.LazyFunction(lambda: fake.date_time())

