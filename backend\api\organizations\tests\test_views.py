from rest_framework.test import APITestCase
from rest_framework import status
from django.urls import reverse
from django.contrib.auth import get_user_model
from api.organizations.models import Organization

User = get_user_model()

class OrganizationAPITestCase(APITestCase):
    
    def setUp(self):
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='adminpass',
        )
        self.regular_user = User.objects.create_user(
            email='<EMAIL>',
            password='userpass',
        )
        self.organization = Organization.objects.create(
            name='Test Org',
            zip_code='12345',
            city='Test City',
            state='Test State',
            registration_number='REG123456'
        )
        self.organization.administrators.add(self.admin_user)
        self.url = reverse('organization-list')
    
    def test_user_can_create_organization(self):
        self.client.force_authenticate(user=self.admin_user)
        data = {
            'name': 'New Org',
            'zip_code': '67890',
            'city': 'New City',
            'state': 'New State',
            'registration_number': 'REG654321',
            'administrators': [self.admin_user.id]
        }
        response = self.client.post(self.url, data)
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Organization.objects.count(), 2)
        self.assertEqual(Organization.objects.get(name='New Org').city, 'New City')
    
    def test_authenticated_user_can_view_organizations(self):
        self.client.force_authenticate(user=self.regular_user)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
    
    def test_admin_can_update_organization(self):
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('organization-detail', kwargs={'pk': self.organization.pk})
        data = {'name': 'Updated Org'}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.organization.refresh_from_db()
        self.assertEqual(self.organization.name, 'Updated Org')
    
    def test_regular_user_cannot_update_organization(self):
        self.client.force_authenticate(user=self.regular_user)
        url = reverse('organization-detail', kwargs={'pk': self.organization.pk})
        data = {'name': 'Hacked Org'}
        response = self.client.patch(url, data)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_admin_can_delete_organization(self):
        self.client.force_authenticate(user=self.admin_user)
        url = reverse('organization-detail', kwargs={'pk': self.organization.pk})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Organization.objects.count(), 0)
    
    def test_regular_user_cannot_delete_organization(self):
        self.client.force_authenticate(user=self.regular_user)
        url = reverse('organization-detail', kwargs={'pk': self.organization.pk})
        response = self.client.delete(url)
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

    def test_anonymous_user_can_access_organization_name(self):
        """
        Test that an anonymous user can access the organization name action.
        """
        url = reverse('organization-organization-name', kwargs={'pk': self.organization.pk})
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, {"name": self.organization.name})

    def test_organization_name_action_returns_404_for_invalid_id(self):
        """
        Test that the organization name action returns 404 for a non-existent organization.
        """
        url = reverse('organization-organization-name', kwargs={'pk': 9999})  # Non-existent ID
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)

    def test_throttling_on_organization_name_action(self):
        """
        Test that the custom throttle is applied to the organization name action.
        This requires configuring a low throttle rate in the settings for testing.
        """
        url = reverse('organization-organization-name', kwargs={'pk': self.organization.pk})

        # Simulate exceeding throttle limit
        for _ in range(10):  # Assuming throttle allows fewer than 10 requests
            response = self.client.get(url)

        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)