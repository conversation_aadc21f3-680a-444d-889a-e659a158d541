# Generated by Django 4.2.13 on 2025-04-14 14:22

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0002_organizationtag'),
        ('users', '0021_collegestudentprofile_organization_tags'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='collegestudentprofile',
            name='organization_tags',
        ),
        migrations.AddField(
            model_name='collegestudentprofile',
            name='organization_tags',
            field=models.ManyToManyField(blank=True, related_name='college_student_profile', to='organizations.organizationtag'),
        ),
    ]
