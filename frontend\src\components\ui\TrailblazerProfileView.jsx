"use client"

import { useRouter } from 'next/navigation'
import { useEffect, useState, useMemo } from 'react'
import { useParams } from 'next/navigation'
import { useAuth } from '@/context/AuthProvider';
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Skeleton } from '@/components/ui/skeleton'
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import { Card, CardContent } from "@/components/ui/card"
import { Loader2 } from "lucide-react"
import SessionRequestDialog from "@/components/ui/sessionRequestDialog"
import { useToast } from "@/hooks/use-toast"
import { dashboardRoutes, UserType } from '@/lib/utils'
import ErrorPopup from '@/components/ui/organizationHoursErrorPopup';


// ProfileSection component
const ProfileSection = ({ user }) => {
    const { getToken } = useAuth();
    const [verifiedTags, setVerifiedTags] = useState(null);
    const [loadingTags, setLoadingTags] = useState(true);

    const fetchVerifiedTags = async (tagIds) => {
        try {
            const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/tags/batch-verify/`;
            const response = await fetch(url, {
                method: "POST",
                headers: {
                    "Content-Type": "application/json",
                    "Authorization": `Token ${getToken()}`,
                },
                body: JSON.stringify({ tag_ids: tagIds }),
            });

            if (response.ok) {
                const data = await response.json();
                return data.tags.map((tag) => tag.name); // Extract tag names
            } else {
                console.error("Failed to fetch verified tags:", response.statusText);
                return [];
            }
        } catch (error) {
            console.error("Error fetching verified tags:", error);
            return [];
        }
    };

    useEffect(() => {
        if (!user.profile.organization_tags || user.profile.organization_tags.length === 0) {
            setVerifiedTags([]); // Set an empty array if there are no tags
            setLoadingTags(false);
            return;
        }

        const verifyTags = async () => {
            setLoadingTags(true);
            const tagIds = user.profile.organization_tags;
            const verified = await fetchVerifiedTags(tagIds);
            setVerifiedTags(verified);
            setLoadingTags(false);
        };

        verifyTags();
    }, [user.profile.organization_tags]);

    const avatarUrl = user.profile.avatar
        ? user.profile.avatar.startsWith("https")
            ? user.profile.avatar
            : `${process.env.NEXT_PUBLIC_API_BASE_URL}${user.profile.avatar}`
        : null;

    return (
        <div className="text-gray-800 space-y-0.5">
            <Avatar className="h-16 w-16 mb-2">
                <AvatarImage src={avatarUrl} alt={user.name} />
                <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <h2 className="font-bold">{user.name}</h2>
            <p className="">
                Currently at <span className="font-bold">{user.profile.university}</span>
            </p>
            <p className="">
                Studying <span className="font-bold">{user.profile.college_major}</span>
            </p>
            <p className="">
                Graduating <span className="font-bold">{user.profile.graduation_year}</span>
            </p>
            <p className="">
                From{" "}
                <span className="font-bold">{`${user.profile.high_school_city}, ${user.profile.high_school_state}`}</span>
            </p>
            <p className="">
                High School <span className="font-bold">{user.profile.high_school_name}</span>
            </p>
            <p className="pt-3">{user.profile.bio}</p>
            <div className="flex flex-wrap gap-2 pt-2">
                {user.profile.interests?.map((tag, index) => (
                    <Badge
                        key={index}
                        variant="secondary"
                        className="cursor-pointer bg-[#ADFFC2] border-[#36CE5D] hover:bg-gray-200 hover:border-transparent capitalize"
                    >
                        {tag}
                    </Badge>
                ))}
            </div>
            <div className="flex flex-wrap gap-2 pt-2">
                {loadingTags ? (
                    Array.from({ length: 3 }).map((_, index) => (
                        <Skeleton key={index} className="h-6 w-20 rounded-md" />
                    ))
                ) : (
                    verifiedTags.map((tag, index) => (
                        <Badge
                            key={index}
                            variant="secondary"
                            className="cursor-pointer bg-green-400 text-white border-white hover:bg-gray-200 hover:border-transparent capitalize"
                        >
                            {tag}
                        </Badge>
                    ))
                )}
            </div>
        </div>
    );
};


// CalendarSection component
const CalendarSection = ({ trailblazerIds, selectedDate, setSelectedDate }) => {
    // Calendar navigation
    const [currentMonth, setCurrentMonth] = useState(new Date())
    const [error, setError] = useState(null)
    const [loading, setLoading] = useState(false)
    const { getToken } = useAuth()

    const handleMonthChange = (newMonth) => {
        setCurrentMonth(newMonth);
        setSelectedDate(null);
    };

    const fetchAvailableDates = async (startDate, endDate, studentIds) => {
        const queryParams = new URLSearchParams();
        queryParams.append('start_date', startDate);
        queryParams.append('end_date', endDate);
        studentIds.forEach(id => queryParams.append('student_ids', id));
    
        const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/availabilities/student-availability?${queryParams.toString()}`;
    
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Token ${getToken()}`
            }
        });
        if (!response.ok) {
            throw new Error('Failed to fetch availability');
        }
        const data = await response.json();
        return data.available_dates;
    }

    useEffect(() => {
        if (!trailblazerIds || trailblazerIds.length === 0) {
            return
        }
        const startDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth(), 1).toISOString().split('T')[0]
        const endDate = new Date(currentMonth.getFullYear(), currentMonth.getMonth() + 1, 0).toISOString().split('T')[0]
        setLoading(true);
        setError(null)
        fetchAvailableDates(startDate, endDate, trailblazerIds)
            .then(dates => {
                setAvailableDates(dates);
                setLoading(false); // End loading
            })
            .catch(err => {
                setError(err.message);
                setLoading(false); // End loading
            });
      }, [currentMonth, trailblazerIds])

    // Date availability
    const [availableDates, setAvailableDates] = useState([])
    const isDateAvailable = (date) => {
        if (!availableDates || !date) {
            return false;
        }
    
        // Get today's date, tomorrow, and the day after tomorrow
        const today = new Date();
        today.setHours(0, 0, 0, 0); // Clear the time part to focus only on the date
    
        // const tomorrow = new Date(today);
        // tomorrow.setDate(today.getDate() + 1);
    
        // const dayAfterTomorrow = new Date(today);
        // dayAfterTomorrow.setDate(today.getDate() + 2);
    
        // // If the date is before the day after tomorrow, it's not available
        // if (date <= dayAfterTomorrow) {
        //     return false;
        // }
    
        // Return true if the date is in the availableDates list
        return availableDates.includes(date.toISOString().split('T')[0]);
    };

    return (
        <div className="relative space-y-4">
            <Calendar
                mode="single"
                selected={selectedDate}
                onSelect={setSelectedDate}
                month={currentMonth}
                onMonthChange={handleMonthChange}
                modifiers={{
                    available: isDateAvailable,
                    disabled: (day) => !isDateAvailable(day), // Mark unavailable dates as disabled
                }}
                modifiersStyles={{
                    available: { fontWeight: 'normal' }, // Available dates are displayed with normal text
                    disabled: { color: 'gray', opacity: 0.5 }, // Unavailable dates are greyed out
                }}
                className="h-full w-full flex px-0 md:px-3 md:pb-3 md:pt-1"
                classNames={{
                    months: "flex w-full flex-col sm:flex-row space-y-4 sm:space-x-4 sm:space-y-0 flex-1",
                    month: "space-y-4 w-full flex flex-col",
                    table: "w-full h-full border-collapse space-y-1",
                    // Custom styles for day names (head cells)
                    head_row: "",
                    head_cell: "text-sm font-bold uppercase text-center",
                    row: "w-full mt-4", // Add more margin (or use padding) between day rows
                    cell: "relative md:p-1 text-center text-sm", // Increase padding between individual days
                    // Custom styles for the navigation part
                    caption: "flex items-center justify-between bg-transparent px-2 pb-2 pt-0 ", // Ensure the layout is flex and justify content
                    caption_label: "text-base font-bold", // Default text size and bold for the month label
                    nav: "flex space-x-2", // Flex container for the navigation buttons
                    nav_button_previous: "ml-auto", // Align previous button to the right
                    nav_button_next: "", // Remove default styles for the navigation buttons
                    nav_button: "", // Remove default styles for the navigation buttons
                }}
            />
            {loading && (
                <div className="absolute inset-0 flex items-center justify-center z-10">
                    <Loader2 className="animate-spin inline-block" />
                </div>
            )}
            {availableDates.length === 0 && !error && !loading && (
                <div className="text-gray-500">No available dates for this month</div>
            )}
            {error && <div className="text-red-500 mb-4">{error}</div>}
        </div>
    )
}


// TimeSection component
const TimeSection = ({ trailblazerIds, selectedDate, selectedSlot, setSelectedSlot, onRequestSession }) => {
    const [timeSlots, setTimeSlots] = useState([])
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)
    const { getToken } = useAuth()

    // Mock function to check if a date is available
    const isDateAvailable = (date) => {
        if (!date) {
            return false
        }
        return true
    }

    const fetchAvailableTimes = async (date, studentIds) => {
        const queryParams = new URLSearchParams();
        queryParams.append('date', date);
        studentIds.forEach(id => queryParams.append('student_ids', id));
    
        const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/availabilities/student-times-availability?${queryParams.toString()}`;
    
        const response = await fetch(url, {
            method: 'GET',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Token ${getToken()}`
            }
        });
        if (!response.ok) {
            throw new Error('Failed to fetch time availability');
        }
        const data = await response.json();
        return data;
    }

    useEffect(() => {
        if (!trailblazerIds || trailblazerIds.length === 0) {
            return
        }
        if (!selectedDate) {
            return
        }
        setSelectedSlot(null);
        setLoading(true);
        setError(null)
        const date = selectedDate.toISOString().split('T')[0]
        fetchAvailableTimes(date, trailblazerIds)
            .then(data => {
                const localTimeSlots = data.available_time_slots.map(slot => {
                    const timeZone = data.time_zone === 'UTC' ? 'Z' : data.time_zone;
                
                    // Construct the start and end time strings with the provided time zone
                    const startDateStr = `${selectedDate.toISOString().split('T')[0]}T${slot.start}:00${timeZone}`;
                    const endDateStr = `${selectedDate.toISOString().split('T')[0]}T${slot.end}:00${timeZone}`;
                
                    // Parse the dates using the provided time zone
                    const start = new Date(startDateStr);
                    const end = new Date(endDateStr);
                
                    // Convert to the browser's local time zone
                    return {
                        start: start.toLocaleString(undefined, {
                            hour: '2-digit',
                            minute: '2-digit',
                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone  // Local timezone of the browser
                        }),
                        end: end.toLocaleString(undefined, {
                            hour: '2-digit',
                            minute: '2-digit',
                            timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone  // Local timezone of the browser
                        })
                    };
                });
                setTimeSlots(localTimeSlots);
                setLoading(false);
            })
            .catch(err => {
                setError(err.message);
                setLoading(false);
            });
    }, [selectedDate, trailblazerIds])

    return (
        <div className="flex flex-col h-full justify-between space-y-4">
            <div>
                <h3 className="text-lg font-semibold mb-2">
                    {selectedDate && selectedDate.toLocaleDateString('en-US', { weekday: 'short' }) + ' ' + selectedDate.getDate()}
                </h3>
                <div className="relative space-y-2 md:overflow-y-auto md:max-h-[50vh] md:min-h-[10vh]">
                    {!loading && (
                        isDateAvailable(selectedDate) ? (
                            timeSlots.map((slot, index) => (
                                selectedSlot === slot ? (
                                    // Button when the slot is selected
                                    <Button
                                        key={index}
                                        variant="secondary"
                                        className="w-full justify-center p-6 cursor-pointer bg-[#ADFFC2] border-[#36CE5D] hover:bg-gray-200 hover:border-transparent"
                                        onClick={() => setSelectedSlot(slot)}
                                    >
                                        {slot.start}
                                    </Button>
                                ) : (
                                    // Button when the slot is not selected
                                    <Button
                                        key={index}
                                        variant="outline"
                                        className="w-full justify-center p-6 bg-white"
                                        onClick={() => setSelectedSlot(slot)}
                                    >
                                        {slot.start}
                                    </Button>
                                )
                            ))
                        ) : (
                            <p className="text-gray-500">No available slots for this date.</p>
                        )
                    )}
                    {loading && (
                        <div className="absolute inset-0 flex items-center justify-center z-10">
                            <Loader2 className="animate-spin inline-block" />
                        </div>
                    )}
                    {error && <div className="text-red-500 mb-4">{error}</div>}
                </div>
            </div>
            <div>
                <Button 
                    className="w-full" 
                    onClick={() => onRequestSession(selectedDate, selectedSlot)}
                    disabled={!selectedSlot}
                >
                    Request Session
                </Button>
            </div>
        </div>
    )
}


const TrailblazerProfileView = ({ trailblazerIds }) => {

    // common
    const [error, setError] = useState(null)
    const { toast } = useToast()
    const { user, loadingUser: userLoading, error: userError } = useAuth()
    const isHighSchoolUser = useMemo(() => user.user_type === UserType.HIGH_SCHOOL_STUDENT_TYPE, [user])

    // Trailblazer information
    const params = useParams()
    const trailblazerId = params.trailblazerId
    const router = useRouter()
    const [trailblazers, setTrailblazers] = useState([])
    const [loading, setLoading] = useState(true)
    const { getToken } = useAuth()

    const trailBlazerNames = useMemo(() => {
        // get names list as a string comma separated and with 'and' before the last one
        if (trailblazers.length === 0) {
            return ''
        }
        if (trailblazers.length === 1) {
            return trailblazers[0].name
        }
        if (trailblazers.length === 2) {
            return `${trailblazers[0].name} and ${trailblazers[1].name}`
        }
        return trailblazers.slice(0, -1).map(trailblazer => trailblazer.name).join(', ') + ` and ${trailblazers[trailblazers.length - 1].name}`
    }, [trailblazers])

    // Date selection
    const [selectedDate, setSelectedDate] = useState(null)

    const fetchTrailblazers = async (authToken, trailblazerIds) => {
        setLoading(true)
        try {
            const responses = await Promise.all(trailblazerIds.map(id =>
                fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/college-students/${id}/`, {
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${authToken}`,
                    },
                })
            ))
            const data = await Promise.all(responses.map(res => res.json()))
            setTrailblazers(data.map(trailblazer => ({
                ...trailblazer,
                name: `${trailblazer.first_name} ${trailblazer.last_name}`,
            })))
        } catch (err) {
            setError(`Failed to fetch Trailblazer profiles - ${err.message}`)
        } finally {
            setLoading(false)
        }
    }

    useEffect(() => {
        const authToken = getToken()
        if (!authToken) {
            router.push('/login')
        }
        if (!trailblazerIds || trailblazerIds.length === 0) {
            return
        }
        fetchTrailblazers(authToken, trailblazerIds)
    }, [trailblazerIds])

    // time selection
    const [selectedSlot, setSelectedSlot] = useState(null)

    // Booking form
    const [bookingStatus, setBookingStatus] = useState('') // 'pending' upon successful submission
    const [isLoadingBooking, setIsLoadingBooking] = useState(false)
    const [bookingError, setBookingError] = useState(null)
    const [showErrorPopup, setShowErrorPopup] = useState(false); // State to control the error popup

    function convertToUTC(selectedDate, selectedSlot) {
        // Function to parse the time string (e.g., "11:30 AM")
        function parseTimeString(timeString) {
            const [time, modifier] = timeString.split(' ');
            let [hours, minutes] = time.split(':').map(Number);
        
            if (modifier === 'PM' && hours < 12) {
            hours += 12;
            }
            if (modifier === 'AM' && hours === 12) {
            hours = 0;
            }
        
            return { hours, minutes };
        }
        
        // Parse the start time from selectedSlot
        const { hours, minutes } = parseTimeString(selectedSlot.start);
        
        // Combine selectedDate and selectedSlot.start into a Date object
        const dateWithTime = new Date(
            selectedDate.getFullYear(),
            selectedDate.getMonth(),
            selectedDate.getDate(),
            hours,
            minutes
        );
        
        // Convert to UTC datetime string
        return dateWithTime.toISOString();
    }


    const onSubmit = async (data) => {
        setBookingError(null);
        const utcDateString = convertToUTC(selectedDate, selectedSlot);
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone; // Get the browser's timezone
        setIsLoadingBooking(true);

        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/bookings/bookings/`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${getToken()}`,
                },
                body: JSON.stringify({
                    trailblazers: trailblazerIds,
                    start_time: utcDateString,
                    message: data.sessionTopics,
                    number_of_students: data.studentsCount,
                    creator_timezone: timezone, // Include the timezone in the body
                }),
            });

            if (response.ok) {
                setBookingStatus('pending'); // Update booking status to 'pending'
                router.push(dashboardRoutes[user.user_type]);
                toast({
                    title: "You're All Set!",
                    description: "Your session request has been sent. You'll be notified once the session is confirmed.",
                });
            } else {
                const errorData = await response.json();

                // Handle specific error codes
                if (errorData.code === 'ORG_NO_HOURS' || errorData.code === 'STUDENT_MAX_HOURS') {
                    setBookingError(errorData.detail);
                    setIsDialogOpen(false);
                    setShowErrorPopup(true); 
                } else if (errorData.non_field_errors) {
                    setBookingError(errorData.non_field_errors[0]);
                } else if (errorData.message) {
                    setBookingError(errorData.message);
                } else if (errorData.start_time && errorData.start_time[0]) {
                    setBookingError(errorData.start_time[0]);
                } else {
                    setBookingError(`Failed to submit booking request: ${JSON.stringify(errorData)}`);
                }
                setIsLoadingBooking(false);
            }
        } catch (error) {
            setBookingError(`Failed to submit booking request. ${error}`);
            setIsLoadingBooking(false);
        }
    }

    const handleRequestSession = (date, slot) => {
        if (date && slot) {
            setBookingError(null)
            setIsDialogOpen(true)
        }
    }

    // request session dialog
    const [isDialogOpen, setIsDialogOpen] = useState(false)

    if (loading) {
        return (
            <div className="p-4">
                <Skeleton className="h-24 w-24 rounded-full" />
                <Skeleton className="h-6 w-48 mt-4" />
                <Skeleton className="h-4 w-64 mt-2" />
                <Skeleton className="h-4 w-80 mt-2" />
                <Skeleton className="h-4 w-56 mt-2" />
                <Skeleton className="h-4 w-40 mt-2" />
                <Skeleton className="h-4 w-60 mt-2" />
            </div>
        )
    }

    if (error) {
        return <p className="text-red-500 text-center mt-4">{error}</p>
    }

    if (!trailblazers || trailblazers.length === 0) {
        return <p className="text-red-500 text-center mt-4">Trailblazers data is unavailable.</p>
    }

    // Render only if all fields are present
    return (
        <div>
            {showErrorPopup && (
                <ErrorPopup
                    message={bookingError} // Pass the error message
                    onClose={() => setShowErrorPopup(false)} // Close the popup
                />
            )}
        <Card className="w-full h-full md:min-h-[50vh] md:max-h-[80vh] px-0 py-3 md:px-6 md:py-6">
            <CardContent className="md:p-6 h-full">
                <div className="md:flex md:space-x-10 h-full">
                    <div className="md:w-2/5 mb-6 md:mb-0 flex flex-col gap-8 md:overflow-y-auto md:max-h-[60vh]">
                        {trailblazers.map(trailblazer => (
                            <ProfileSection key={trailblazer.id} user={trailblazer} />
                        ))}
                    </div>
                    <div className="md:w-2/5">
                        <CalendarSection
                            trailblazerIds={trailblazerIds}
                            selectedDate={selectedDate}
                            setSelectedDate={setSelectedDate}
                        />
                    </div>
                    <div className="md:w-1/5">
                        {selectedDate && (
                            <TimeSection 
                                trailblazerIds={trailblazerIds}
                                selectedDate={selectedDate}
                                selectedSlot={selectedSlot}
                                setSelectedSlot={setSelectedSlot}
                                onRequestSession={handleRequestSession} 
                                onRequestError={setError}
                            />
                        )}
                    </div>
                </div>
            </CardContent>
            <SessionRequestDialog
                isOpen={isDialogOpen}
                onSubmit={onSubmit}
                onClose={() => setIsDialogOpen(false)}
                trailblazerName={trailBlazerNames}
                sessionDate={selectedDate?.toLocaleDateString('en-US', { weekday: 'long', year: 'numeric', month: 'long', day: 'numeric' })}
                sessionTime={`${selectedSlot?.start} - ${selectedSlot?.end}`}
                error={bookingError}
                isLoadingBooking={isLoadingBooking}
                displayStudentsCount={!isHighSchoolUser}
            />
        </Card>
        </div>
    )
}

export default TrailblazerProfileView
