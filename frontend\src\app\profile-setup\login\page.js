"use client"

import React, { useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { useAuth } from "@/context/AuthProvider"
import { Check, Eye, EyeOff, Loader2 } from "lucide-react"

// Define the form schema with Zod
const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password must be set")
})

export default function ProfileSetupLoginPage() {
  const router = useRouter()
  const { loginWithToken, profileData, clearProfileData } = useAuth()
  const [apiError, setApiError] = useState("")
  const [loading, setLoading] = useState(false)
  const [showPassword, setShowPassword] = useState(false)

  const form = useForm({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      password: ""
    }
  })

  const onSubmit = async (data) => {
    setApiError("")
    setLoading(true)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/login/`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json"
        },
        body: JSON.stringify(data)
      })
      const result = await response.json()
      if (response.ok) {
        // Ensure only HighSchool student profiles can log in
        if (result.user_type !== "HighSchoolStudent") {
          setApiError("Only HighSchool student profiles can use this login.")
          setLoading(false)
          return
        }

        const token = result.token
        await loginWithToken(result.token)

        if (profileData) {
          const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/high-school-student-profile/`, {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
              "Authorization": `Token ${token}`
            },
            body: JSON.stringify(profileData)
          })
          if (!res.ok) {
            const errorData = await res.json()
            setFormError(errorData.error || "Failed to update profile")
            setIsLoading(false)
            return
          } else {
            clearProfileData()
            // Redirect to recommendations page after short delay
            setTimeout(() => {
              router.push("/recommendations")
            }, 1000)
          }
        }
      } else {
        setApiError(result.error || "Invalid email or password")
        setLoading(false)
      }
    } catch (error) {
      setApiError(`Failed to connect to the server: ${error}`)
      setLoading(false)
    }
  }

  const steps = [
    { name: "Profile", status: "completed" },
    { name: "Account", status: "active" },
    { name: "Recommendations", status: "upcoming" },
  ]

  return (
    <>
      <style jsx>{`
        /* Stepper styles */
        .stepper-item {
          flex: 1;
          display: flex;
          justify-content: center;
        }

        .stepper-circle {
          width: 2rem;
          height: 2rem;
          border-radius: 50%;
          display: flex;
          align-items: center;
          justify-content: center;
          font-size: 0.875rem;
          font-weight: 600;
          background-color: #e5e7eb;
          color: #6b7280;
          border: 2px solid #e5e7eb;
          transition: all 0.2s ease;
        }

        .stepper-item.completed .stepper-circle {
          background-color: hsl(var(--primary));
          color: white;
          border-color: hsl(var(--primary));
        }

        .stepper-item.active .stepper-circle {
          background-color: white;
          color: hsl(var(--primary));
          border-color: hsl(var(--primary));
          box-shadow: 0 0 0 4px rgb(59 130 246 / 0.1);
        }
      `}</style>
      <div className="min-h-screen flex flex-col items-center justify-center p-4 bg-gray-50">
        <div className="w-full max-w-sm mx-auto bg-white rounded-lg shadow-md overflow-hidden border-t-4 border-primary">
          <div className="px-6 pt-6">
            <div className="flex justify-between mb-6">
              {steps.map((step, index) => (
                <div
                  key={step.name}
                  className={`stepper-item ${step.status === 'completed' ? 'completed' : ''} ${step.status === 'active' ? 'active' : ''}`}
                >
                  <div className="flex flex-col items-center">
                    <div className="stepper-circle">
                      {step.status === 'completed' ? (
                        <Check className="w-3 h-3" strokeWidth={3} />
                      ) : (
                        index + 1
                      )}
                    </div>
                    <div className="text-xs mt-2 font-medium text-gray-600">{step.name}</div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Logo and Header */}
          <div className="px-6 pb-2 text-center">
            <div className="flex justify-center mb-8">
              <img
                alt="Trailblazer Logo"
                className="h-8"
                src="/logo.svg"
                onError={(e) => {
                  e.currentTarget.src = 'https://placehold.co/120x32/3b82f6/ffffff?text=Trailblazer'
                  e.currentTarget.onerror = null
                }}
              />
            </div>
            <h1 className="text-2xl font-bold mb-1 text-gray-900">Log In to Your Profile</h1>
            <p className="text-gray-600 text-sm mb-6">
            Enter your credentials to access your HighSchool student account.
            </p>
          </div>

          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="px-6 pt-2 pb-4">
              <FormField
                control={form.control}
                name="email"
                render={({ field }) => (
                  <FormItem className="mb-5">
                    <FormControl>
                      <Input
                        type="email"
                        placeholder="Email Address"
                        {...field}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <FormField
                control={form.control}
                name="password"
                render={({ field }) => (
                  <FormItem className="mb-5">
                    <FormControl>
                      <div className="relative">
                        <Input
                          type={showPassword ? "text" : "password"}
                          placeholder="Password"
                          {...field}
                        />
                        <button
                          type="button"
                          className="absolute inset-y-0 right-0 pr-3 flex items-center"
                          onClick={() => setShowPassword(!showPassword)}
                        >
                          {showPassword ? (
                            <EyeOff className="h-4 w-4 text-gray-500" />
                          ) : (
                            <Eye className="h-4 w-4 text-gray-500" />
                          )}
                        </button>
                      </div>
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              {apiError && <p className="text-red-600 text-center mb-4">{apiError}</p>}
              <Button
                type="submit"
                className="w-full p-6 bg-primary text-white rounded-lg hover:bg-green-600 hover:text-white"
                disabled={loading}
              >
                {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}
                Log In
              </Button>
            </form>
          </Form>
          <div className="px-6 pb-6 text-center">
            <p className="text-sm text-gray-600">
              Don&apos;t have an account?{" "}
              <Link href="/profile-setup/signup" className="font-medium text-primary hover:underline">
                Sign Up
              </Link>
            </p>
          </div>
        </div>
        <div className="mt-8 text-center text-xs text-gray-500">
          <p>© 2024 Trailblazer. All rights reserved.</p>
        </div>
      </div>
    </>
  )
}
