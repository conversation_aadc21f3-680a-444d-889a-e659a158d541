<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Proposed Times for Rescheduling</title>
    <style>
        body {
            background-color: #f8faf6;
            font-family: Arial, sans-serif;
            padding: 16px;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 0 auto;
            padding: 24px;
        }
        .header {
            text-align: center;
            margin-bottom: 24px;
        }
        .logo {
            height: 32px;
            margin-bottom: 12px;
        }
        .badge {
            background-color: #ffd1a9;
            color: #f57c00;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 12px;
            border-radius: 16px;
            display: inline-block;
            margin-top: 8px;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .details {
            margin-bottom: 16px;
        }
        .details span {
            font-weight: bold;
        }
        .proposed-times {
            margin-top: 20px;
        }
        .proposed-time {
            margin-bottom: 10px;
        }
        .footer {
            text-align: center;
            margin-top: 20px;
            font-size: 0.9em;
            color: #777;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <img src="{{ logo_path }}" alt="Trailblazer Logo" class="logo"/>
        </header>
        <div>
            <div class="badge">Cancelled</div>
            <h1 class="title">Trailblazer Session Cancelled</h1>
            <p class="details">
                <span>From:</span> {{ session_with }} <br/>
                <span>Date:</span> {{ session_date }} <br/>
                <span>Time:</span> {{ session_time }} <br/>
                <span>Type:</span> {{ session_type }}
            </p>
            <p>The trailblazer would like to reschedule to one of the following proposed times:</p>
            <div class="proposed-times">
                {% for proposed_time in proposed_times %}
                    <div class="proposed-time">
                        <strong>Date:</strong> {{ proposed_time.date }}<br>
                        <strong>Proposed Time:</strong> {{ proposed_time.session_time }}<br>                        
                    </div>
                {% endfor %}
            </div>
            <p>Please navigate to your dashboard to see and accept a proposed time. If none of the proposed times work for you, you can do nothing and the session will remain cancelled.</p>
        </div>
        
    </div>
</body>
</html>