"use client"

import { useState, useEffect } from "react"
import { useRouter } from 'next/navigation'
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { But<PERSON> } from "@/components/ui/button"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { ArrowRight } from "lucide-react"
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { useToast } from "@/hooks/use-toast"
import { Loader2 } from 'lucide-react'
import { getNextOnboardingStep, getUseWaitlist } from '@/lib/utils'

// Main BackgroundCheckPage component
const BackgroundCheckPage = () => {
    const router = useRouter();
    const [completed, setCompleted] = useState(false);
    const { toast } = useToast();
    const [isLoading, setIsLoading] = useState(true); // Add a loading state
    const { user, getToken, updateAuthenticatedUser } = useAuth();

    useEffect(() => {
        const checkWaitlistStatus = async () => {
            try {
                const useWaitlist = await getUseWaitlist(getToken());
               

                if (useWaitlist && user?.profile?.is_waitlisted) {
                
                    router.push('/college-student/waitlist');
                } else {
                    setIsLoading(false); // Allow the page to render if not waitlisted
                }
            } catch (error) {
                console.error("Error checking waitlist status:", error);
                setIsLoading(false); // Allow the page to render even if there's an error
            }
        };

        if (user) {
            checkWaitlistStatus();
        }
    }, [user])

    // Handler for completing the background check
    const onOpenCheck = () => {
        console.log("Completing background check...")
        // open background check url in a new tab
        window.open(
            'https://408e187a.streaklinks.com/CMpWj_LGxvf4rZ8c-g-iyUW3/https%3A%2F%2Fs2verify.screening.services%2Fswifthire%2Fv2.0%2F%3Ftoken%3DTTxYT0NLW1w7QSIlSE4nREpeQ0wnPDk_ITcpRVY_PzlQRV9aREI1Izo2J1BCXC8rWUwhJ1pLX0dNRV00TQpNPF48OidaSVhVUkMkKSpQKE8hWkJPX1whJl9SNEgoUk4xNVtFI1haLFZFL1cuIy8uX0dLNjkuVVAzRl87Ck0qNTQgNDVaXVdKQj4-LDFATEVHLzJPOzJPWE1WOzMiXSguPENONjE5LDsrX1IvRTIoRkA8NlNWKV1WOD0KTSxPLl5KSDNGOi4tOFo2MkFcVUo5XiRZOjpIS0M-LS9GIihWTyhHIyZIMzIjTVZBJkpJIzRcRzdIK1BMUQpNO05aWUBMK0pUVU5JPCFPIEtRMyovQkheUjAzJkpSOTZTKikxJz9cN1swLFcpXjU8TiUoLVErTEhOW14kCi9ORlRSIyFOPzNfMS5IRkhIWVBLSAo',
            '_blank'
        )
        setCompleted(true)
    }

    const onCompleteCheck = async () => {
        setIsLoading(true)
        const authToken = getToken()
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`,
                },
                body: JSON.stringify({
                    step: "background-check",
                }),
            })
            if (response.ok) {
                toast({ 
                    title: 'Success!', 
                    description: 'Profile information uploaded successfully' 
                })
                await updateAuthenticatedUser()
                router.push(getNextOnboardingStep('CollegeStudent', 'background-check'))
            } else {
                console.error('Error Response:', response)
                toast({ 
                    variant: 'destructive',
                    description: result.detail || 'Failed to upload profile information' 
                })
                setIsLoading(false)
            }
        } catch (error) {
            console.error('Error:', error)
            toast({ 
                variant: 'destructive',
                description: 'An unexpected error occurred' 
            })
            setIsLoading(false)
        }
    }

    // Show a loading indicator or blank page while checking waitlist status
    if (isLoading) {
        return <div>Loading...</div>; // Replace with a spinner or skeleton loader if needed
    }

    // Main page content
    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="py-10">
                    <StepProgressDisplay currentStep={6} totalSteps={6} />
                </div>
                <div className="w-full md:w-5/6 lg:w-5/6">
                    <>
                        <h1 className="text-3xl font-semibold mb-8 mt-2">
                            One last step, complete your background check
                        </h1>
                        <p className="text-base md:text-lg text-gray-600 pb-12">
                            To ensure the safety and trust of our community, please complete your background check. 
                            Once completed and approved, your profile will be activated, making you visible to 
                            counselors and students who can then book sessions with you. 
                            <br/>
                            <br/>
                            Please note that the background check allows for the use of multiple forms of government-issued identification.
                        </p>
                        <div className="flex flex-col w-fit gap-4">
                            <Button 
                                onClick={onOpenCheck}
                                className="bg-white text-gray-800 font-semibold py-3 px-6 border border-gray-300 rounded-lg shadow-sm hover:bg-gray-50 transition duration-300"
                            >
                                Complete Background Check
                            </Button>
                            {
                                completed && (
                                    <Button 
                                        onClick={onCompleteCheck}
                                        className="mt-10 flex items-center justify-center"
                                        disabled={isLoading}
                                    >
                                        {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}
                                        I&apos;ve completed my background check 
                                        <ArrowRight className="ml-2 w-6 h-6" />
                                    </Button>
                                )
                            }
                        </div>
                    </>
                </div>
            </div>
        </OnboardingLayout>
    );
};

export default withAuth(BackgroundCheckPage);
