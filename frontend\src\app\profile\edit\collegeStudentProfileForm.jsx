import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { AvatarSelection } from "@/components/ui/avatarSelection"
import { FormSection } from './formSection'
import { MajorSelect } from '@/components/ui/majorSelect'
import { InterestPicker } from '@/components/ui/interestPicker'
import { UniversityCombobox, SchoolTypeToggle } from "@/components/ui/universityPicker"
import { ZipCodeInput } from '@/components/ui/zipcodeInput'
import { OrganizationTagPicker } from '@/components/ui/organizationTagPicker'
import { useAuth } from '@/context/AuthProvider';

const collegeStudentFormSchema = z.object({
    firstName: z.string().min(2, "First name must be at least 2 characters"),
    lastName: z.string().min(2, "Last name must be at least 2 characters"),
    avatar: z.string(),
    bio: z.string().min(1, "Bio is required").max(280, "Bio must be less than 280 characters"),
    university: z.string().min(1, "University is required"),
    graduationYear: z.string().nonempty("Graduation year is required"),
    collegeTags: z.array(z.string()).nonempty("At least one college tag must be selected"),
    major: z.string().min(1, "Major is required"),
    interests: z.array(z.string()).nonempty("At least one interest must be selected").max(3, "No more than 3 interests can be selected"),
    highSchoolName: z.string(),
    highSchoolZipCode: z.string(),
    highSchoolCity: z.string(),
    highSchoolState: z.string(),
    organizationTags: z
            .array(
                z.object({
                    value: z.number(), // Validate that `value` is a number
                    label: z.string(), // Validate that `label` is a string
                })
            )
            .max(3, { message: "You can select up to 3 organizations" }), // Limit to 3 organizations
    })

export const CollegeStudentProfileForm = ({ userData, onSubmit, setPendingCustomTags }) => {
    const [selectedAvatar, setSelectedAvatar] = useState(userData.profile.avatar) // Initialize with current avatar // State to manage custom tags
    const { getToken } = useAuth(); // Assuming useAuth is a custom hook to get the auth token
    const [isTagsLoaded, setIsTagsLoaded] = useState(false);
    const form = useForm({
        resolver: zodResolver(collegeStudentFormSchema),
        defaultValues: {
            firstName: userData.first_name,
            lastName: userData.last_name,
            avatar: "",
            bio: userData.profile.bio || "",
            university: userData.profile.university || "",
            graduationYear: userData.profile.graduation_year || "",
            collegeTags: userData.profile.university_tags || [],
            major: userData.profile.college_major || "",
            interests: userData.profile.interests || "",
            highSchoolName: userData.profile.high_school_name,
            highSchoolZipCode: userData.profile.high_school_zip_code,
            highSchoolCity: userData.profile.high_school_city,
            highSchoolState: userData.profile.high_school_state,
            organizationTags: userData.profile.organization_tags || [],
            
        }
    })

    useEffect(() => {
        const fetchUserTags = async () => {
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/tags/user/`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Token ${getToken()}`,
                    },
                });

                if (response.ok) {
                    const data = await response.json();
                    form.setValue(
                        "organizationTags",
                        data.tags.map((tag) => ({
                            value: tag.id,
                            label: tag.name,
                        }))
                    );
                    setIsTagsLoaded(true); // Indicate that tags have been loaded
                } else {
                    console.error("Failed to fetch user tags");
                }
            } catch (error) {
                console.error("Error fetching user tags:", error);
            }
        };

        fetchUserTags();
    }, []);

    const onZipMatch = (result) => {
        form.setValue("highSchoolCity", result.city)
        form.setValue("highSchoolState", result.state)
    }

    const onZipError = (error) => {
        if (!error) {
            // reset errors
            delete form.formState.errors.zipCode
        } else {
            form.formState.errors.zipCode = { message: error }
        }
    }

    if (!isTagsLoaded) {
        return <div>Loading...</div>; // Show a loading state until tags are loaded
    }

    return (
        <Form {...form} >
            <form id="profile-form" onSubmit={form.handleSubmit(onSubmit)} className="flex-1 w-full md:w-3/4 lg:w-2/3">
                <FormSection title="About You">
                    <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>First Name*</FormLabel>
                                <FormControl>
                                    <Input {...field} disabled />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Last Name*</FormLabel>
                                <FormControl>
                                    <Input {...field} disabled />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="avatar"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Avatar</FormLabel>
                                <FormControl>
                                    <AvatarSelection
                                        onSelect={(avatar) => {
                                            setSelectedAvatar(avatar)
                                            field.onChange(avatar)
                                        }}
                                        selectedAvatar={selectedAvatar} // Ensure correct prop
                                        initialAvatarUrl={userData.profile.avatar}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="bio"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Bio*</FormLabel>
                                <FormControl>
                                    <Textarea className="bg-white" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </FormSection>
                <FormSection title="Your College Experience">
                    <FormField
                        control={form.control}
                        name="university"
                        render={({ field }) => (
                            <FormItem>
                                <FormControl>
                                    <UniversityCombobox field={field} form={form} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="graduationYear"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Graduation Year*</FormLabel>
                                <div className="mb-4"></div>
                                <FormControl>
                                    <select
                                        value={field.value}
                                        onChange={(e) => {
                                            field.onChange(e.target.value);
                                        }}
                                        className="w-1/2 rounded-lg border border-slate-300 bg-white px-2 py-1 text-sm shadow-sm outline-none transition focus:border-blue-500 focus:ring-1 focus:ring-blue-500"
                                    >
                                        <option value="" disabled>Select graduation year</option>
                                        {Array.from({ length: 6 }, (_, i) => new Date().getFullYear() + i).map(year => (
                                            <option key={year} value={year}>{year}</option>
                                        ))}
                                    </select>
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="collegeTags"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>College Tags*</FormLabel>
                                <FormControl>
                                    <SchoolTypeToggle {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="major"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Major*</FormLabel>
                                <FormControl>
                                    <MajorSelect field={field} form={form} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="interests"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Interests*</FormLabel>
                                <FormControl>
                                    <InterestPicker field={field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </FormSection>
                
                <FormSection title="Your High School Experience">
                    <FormField
                        control={form.control}
                        name="highSchoolName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>High School Name</FormLabel>
                                <FormControl>
                                    <Input {...field} disabled />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="highSchoolZipCode"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Zip Code</FormLabel>
                                <FormControl>
                                    <ZipCodeInput
                                        field={field}
                                        form={form}
                                        onZipMatch={onZipMatch}
                                        onZipError={onZipError}
                                        disabled
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div className="flex flex-col sm:flex-row sm:space-x-4 w-full">
                        <FormField
                            control={form.control}
                            name="highSchoolCity"
                            render={({ field }) => (
                                <FormItem className="w-full">
                                    <FormLabel>City</FormLabel>
                                    <FormControl>
                                        <Input {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                        <FormField
                            control={form.control}
                            name="highSchoolState"
                            render={({ field }) => (
                                <FormItem className="w-full">
                                    <FormLabel>State</FormLabel>
                                    <FormControl>
                                        <Input {...field} disabled />
                                    </FormControl>
                                    <FormMessage />
                                </FormItem>
                            )}
                        />
                    </div>
                    <FormField
                        control={form.control}
                        name="organizationTags"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Organization Tags*</FormLabel>
                                <FormControl>
                                    <OrganizationTagPicker field={field} setPendingCustomTags={setPendingCustomTags} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </FormSection>
            </form>
        </Form>
    )
}
