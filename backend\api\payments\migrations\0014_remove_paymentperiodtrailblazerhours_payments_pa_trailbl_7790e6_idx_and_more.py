# Generated by Django 4.2.13 on 2025-02-28 04:05

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0013_paymentlog_batch_status_paymentlog_time_completed_and_more'),
    ]

    operations = [
        migrations.RemoveIndex(
            model_name='paymentperiodtrailblazerhours',
            name='payments_pa_trailbl_7790e6_idx',
        ),
        migrations.RemoveIndex(
            model_name='skippedpaymentlog',
            name='payments_sk_trailbl_31eeaa_idx',
        ),
        migrations.RenameField(
            model_name='paymentperiodtrailblazerhours',
            old_name='trailblazer',
            new_name='user',
        ),
        migrations.RenameField(
            model_name='skippedpaymentlog',
            old_name='trailblazer',
            new_name='user',
        ),
        migrations.AddIndex(
            model_name='paymentperiodtrailblazerhours',
            index=models.Index(fields=['user'], name='payments_pa_user_id_f37ac1_idx'),
        ),
        migrations.AddIndex(
            model_name='skippedpaymentlog',
            index=models.Index(fields=['user'], name='payments_sk_user_id_1f8351_idx'),
        ),
    ]
