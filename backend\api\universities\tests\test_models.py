from django.test import TestCase
from api.universities.models import University

class UniversityModelTest(TestCase):
    def setUp(self):
        self.university = University.objects.create(
            unitid="123456",
            institution="Test University",
            city="Test City",
            state_territory="Test State",
            zip_code="12345",
            public_private="Public"
        )

    def test_university_creation(self):
        """Test that a university can be created with basic fields"""
        self.assertEqual(self.university.institution, "Test University")
        self.assertEqual(self.university.city, "Test City")
        self.assertEqual(self.university.state_territory, "Test State")
        self.assertEqual(self.university.zip_code, "12345")
        self.assertEqual(self.university.public_private, "Public")
        
    def test_string_representation(self):
        """Test the string representation of the university"""
        self.assertEqual(str(self.university), "Test University")