"use client"

import React, { useState, useEffect, useMemo } from 'react'
import Head from 'next/head'
import Link from 'next/link'
import { useRouter } from 'next/navigation'

// Lucide icons
import {
  LayoutGrid,
  Bookmark,
  MessageCircle,
  Users,
  ChevronsUpDown,
  ChevronUp,
  ChevronDown,
  ArchiveX,
} from 'lucide-react'

// Shadcn/ui components
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardHeader,
} from '@/components/ui/card'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from '@/components/ui/tooltip'
import { useAuth } from '@/context/AuthProvider'
import { useShortlist } from '@/hooks/useShortlist'
import { useToast } from '@/hooks/use-toast'
import { Skeleton } from '@/components/ui/skeleton'
import DashboardLayout from '@/components/layouts/DashboardLayout'

// Initial data for colleges
const initialCollegesData = []

// Student avatar placeholders
const studentPlaceholders = [
    "/avatars/boy_1.png",
    "/avatars/boy_6.png",
    "/avatars/boy_8.png",
    "/avatars/boy_4.png"
]

// Utility function for conditional classes
const cn = (...inputs) => inputs.filter(Boolean).join(' ')

/**
 * Connect with students CTA card component
 */
const ConnectTrailblazersCard = ({ studentCount = 8, avatars = studentPlaceholders, onConnect }) => (
  <Card className="bg-green-50 border-green-200 mb-6">
    <CardContent className="p-6">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex items-start md:items-center gap-3">
          <div className="hidden md:flex items-center justify-center w-10 h-10 rounded-full bg-primary/10 text-primary shrink-0">
            <MessageCircle className="h-5 w-5" />
          </div>
          <div className="flex-1">
            <h3 className="text-lg font-semibold text-gray-800 mb-1">
              Unlock Student Insights
            </h3>
            <p className="text-gray-700 text-sm">
              Chat with <span className="font-semibold text-primary">{studentCount} current students</span> from your shortlisted colleges.
            </p>
          </div>
        </div>
        
        {/* Avatar group */}
        <div className="flex flex-col md:flex-row md:items-center gap-4">
          <div className="flex -space-x-2">
            {avatars.map((src, idx) => (
              <Avatar key={idx} className="w-8 h-8 border-2 border-white shadow-sm">
                <AvatarImage src={src} alt={`Student ${idx + 1}`} />
                <AvatarFallback>S{idx + 1}</AvatarFallback>
              </Avatar>
            ))}
          </div>
          
          <Button onClick={onConnect} className="shrink-0 flex flex-col items-center md:flex-row">
            <Users className="mb-1 md:mb-0 md:mr-2 h-4 w-4" />
            <span>Connect with Students</span>
          </Button>
        </div>
      </div>
    </CardContent>
  </Card>
)

/**
 * Sortable table header cell with sort indicators
 */
const SortableTableHeadCell = ({ children, sortKey, currentSort, onSort, dataType, className }) => {
  const isCurrentSortCol = currentSort.key === sortKey
  const direction = isCurrentSortCol ? currentSort.direction : null
  
  // Determine which icon to show based on sort state
  const getSortIcon = () => {
    if (!isCurrentSortCol) return ChevronsUpDown
    return direction === 'asc' ? ChevronUp : ChevronDown
  }
  
  const SortIcon = getSortIcon()

  const handleClick = () => {
    let newDirection = 'asc'
    if (isCurrentSortCol) {
      if (direction === 'asc') newDirection = 'desc'
      else if (direction === 'desc') {
        // Third click resets sort
        onSort(null, null, dataType)
        return
      }
    }
    onSort(sortKey, newDirection, dataType)
  }

  return (
    <TableHead className={cn("cursor-pointer select-none", className)} onClick={handleClick}>
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <div className="flex items-center justify-between hover:text-primary transition-colors">
              <span>{children}</span>
              <SortIcon className="ml-2 h-4 w-4 opacity-50" />
            </div>
          </TooltipTrigger>
          <TooltipContent>
            <p>Click to sort by {children.toLowerCase()}</p>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    </TableHead>
  )
}

/**
 * Match badge component with different variants
 */
const MatchBadgeComponent = ({ matchType }) => {
  const getVariantAndColor = (type) => {
    switch (type.toLowerCase()) {
      case 'reach':
        return { variant: 'destructive', className: 'bg-red-100 text-red-700 hover:bg-red-100' }
      case 'target':
        return { variant: 'secondary', className: 'bg-yellow-100 text-yellow-700 hover:bg-yellow-100' }
      case 'safety':
        return { variant: 'default', className: 'bg-green-100 text-green-700 hover:bg-green-100' }
      default:
        return { variant: 'outline', className: '' }
    }
  }

  const { className } = getVariantAndColor(matchType)
  
  return (
    ((matchType) && <Badge className={className}>
      {matchType.charAt(0).toUpperCase() + matchType.slice(1)}
    </Badge>)
  )
}

/**
 * Desktop table view for college shortlist
 */
const DesktopShortlistTable = ({ colleges, sortConfig, onSort, onToggleBookmark }) => {
  const [sortColumn, setSortColumn] = useState(null)
  const [sortDirection, setSortDirection] = useState('asc')

  const columnWidths = {
    "College Name": "w-[200px]",
    "Location": "w-[150px]",
    "Type": "w-[120px]",
    "Acceptance": "w-[130px]",
    "Cost": "w-[140px]",
  }

  const tableHeaders = ["College Name", "Location", "Type", "Acceptance", "Annual Cost", "Match"]

  const handleSort = (header) => {
    const keyMap = {
      "College Name": "name",
      "Location": "location",
      "Type": "type",
      "Acceptance": "acceptance",
      "Annual Cost": "cost",
      "Match": "match",
    }
    const key = keyMap[header]

    if (sortColumn === key) {
      setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
    } else {
      setSortColumn(key)
      setSortDirection('asc')
    }
    // Implement additional sorting logic as needed
  }

  const sortedColleges = useMemo(() => {
    if (!sortColumn) return colleges
    const sorted = [...colleges].sort((a, b) => {
      const valueA = a[sortColumn]
      const valueB = b[sortColumn]
      // For numeric fields: Acceptance and Annual Cost, compare as numbers
      if (sortColumn === 'acceptance' || sortColumn === 'cost') {
        const numA = parseFloat(valueA) || 0
        const numB = parseFloat(valueB) || 0
        return sortDirection === 'asc' ? numA - numB : numB - numA
      }
      // For text fields: college name etc.
      if (typeof valueA === 'string' && typeof valueB === 'string') {
        return sortDirection === 'asc'
          ? valueA.localeCompare(valueB)
          : valueB.localeCompare(valueA)
      }
      return 0
    })
    return sorted
  }, [colleges, sortColumn, sortDirection])

  return (
  <div className="hidden md:block overflow-x-auto rounded-lg shadow-md">
    <Table>
      <TableHeader className="bg-gray-50">
        <TableRow>
          {tableHeaders.map((header, idx) => {
            const keyMap = {
              "College Name": "name",
              "Location": "location",
              "Type": "type",
              "Acceptance": "acceptance",
              "Annual Cost": "cost",
              "Match": "match",
            }
            const key = keyMap[header];
            const isActive = sortColumn === key;
            return (
              <TableHead 
                key={idx} 
                className={`px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors ${columnWidths[header] || ''} ${isActive ? 'bg-gray-200 text-primary' : ''}`}
                onClick={() => handleSort(header)}
                role="columnheader"
                tabIndex={0}
                onKeyDown={(e) => {
                  if (e.key === 'Enter' || e.key === ' ') {
                    handleSort(header);
                  }
                }}
              >
                <span>{header}</span>
                {isActive && (
                  <span className="ml-1 text-xs">
                    {sortDirection === 'asc' ? '↑' : '↓'}
                  </span>
                )}
              </TableHead>
            )
          })}
          <TableHead className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Connect
          </TableHead>
          <TableHead className="px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
            Actions
          </TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {sortedColleges.map((college) => (
          <TableRow key={college.id} className="hover:bg-gray-50 bg-white">
            <TableCell>
              <Link 
                href={`/colleges/${college.id}`}
                className="font-medium text-gray-800 hover:text-primary hover:underline transition-colors"
              >
                {college.name}
              </Link>
            </TableCell>
            <TableCell className="text-gray-600">{college.location}</TableCell>
            <TableCell className="text-gray-600">{college.type}</TableCell>
            <TableCell className="text-gray-600">{college.acceptance}</TableCell>
            <TableCell className="text-gray-600">${college.cost.toLocaleString()}</TableCell>
            <TableCell>
              <MatchBadgeComponent matchType={college.match.toLowerCase()} />
            </TableCell>
            <TableCell>
              <Button 
                variant="outline" 
                size="sm" 
                className="border-green-500 text-green-500 hover:bg-green-50" 
                asChild
              >
                <Link href="/paywall">
                  <Users className="mr-1 h-4 w-4 text-green-500" />
                  Connect
                </Link>
              </Button>
            </TableCell>
            <TableCell>
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => onToggleBookmark(college.id)}
                      className={cn(
                        "hover:bg-gray-100",
                        college.bookmarked && "text-primary"
                      )}
                    >
                      <Bookmark 
                        className="h-5 w-5" 
                        fill={college.bookmarked ? "currentColor" : "none"} 
                      />
                    </Button>
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>Remove from shortlist</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  </div>
)}

/**
 * Individual mobile college card component
 */
const MobileCollegeCardItem = ({ college, onToggleBookmark }) => (
  <Card className="mb-4">
    <CardHeader className="pb-3">
      <div className="flex justify-between items-start">
        <Link 
          href={college.collegeLink} 
          className="font-semibold text-lg text-gray-800 hover:text-primary hover:underline transition-colors"
        >
          {college.name}
        </Link>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => onToggleBookmark(college.id)}
          className={cn(
            "hover:bg-gray-100 -mt-2 -mr-2",
            college.bookmarked && "text-primary"
          )}
        >
          <Bookmark 
            className="h-5 w-5" 
            fill={college.bookmarked ? "currentColor" : "none"} 
          />
        </Button>
      </div>
    </CardHeader>
    <CardContent className="pt-0">
      <div className="space-y-3 text-sm">
        <div className="flex justify-between py-2 border-b border-gray-100">
          <span className="font-medium text-gray-600">Location</span>
          <span className="text-gray-800">{college.location}</span>
        </div>
        <div className="flex justify-between py-2 border-b border-gray-100">
          <span className="font-medium text-gray-600">Type</span>
          <span className="text-gray-800">{college.type}</span>
        </div>
        <div className="flex justify-between py-2 border-b border-gray-100">
          <span className="font-medium text-gray-600">Acceptance</span>
          <span className="text-gray-800">{college.acceptance}%</span>
        </div>
        <div className="flex justify-between py-2 border-b border-gray-100">
          <span className="font-medium text-gray-600">Annual Cost</span>
          <span className="text-gray-800">${college.cost.toLocaleString()}</span>
        </div>
        <div className="flex justify-between py-2 items-center">
          <span className="font-medium text-gray-600">Match</span>
          <MatchBadgeComponent matchType={college.match.toLowerCase()} />
        </div>
      </div>
      
      {/* Connect button */}
      <div className="mt-4 pt-4 border-t border-gray-100">
        <Button variant="outline" className="w-full" asChild>
          <Link href={college.connectLink}>
            <Users className="mr-2 h-4 w-4" />
            Connect with Students
          </Link>
        </Button>
      </div>
    </CardContent>
  </Card>
)

/**
 * Mobile cards container
 */
const MobileShortlistCardsContainer = ({ colleges, onToggleBookmark }) => (
  <div className="md:hidden space-y-4">
    {colleges.map((college) => (
      <MobileCollegeCardItem 
        key={college.id} 
        college={college} 
        onToggleBookmark={onToggleBookmark} 
      />
    ))}
  </div>
)

/**
 * Empty state component when no colleges are bookmarked
 */
const EmptyStateComponent = () => (
  <Card className="text-center py-12">
    <CardContent className="space-y-6">
      <div className="flex justify-center">
        <ArchiveX className="h-20 w-20 text-gray-300" />
      </div>
      <div className="space-y-3">
        <h3 className="text-2xl font-semibold text-gray-800">
          Your Shortlist is Empty
        </h3>
        <p className="text-gray-600 max-w-md mx-auto">
          You haven't added any colleges yet. Start by exploring your recommendations or searching for colleges.
        </p>
      </div>
      <Button asChild>
        <Link href="/recommendations">
          <LayoutGrid className="mr-2 h-5 w-5" />
          Browse Recommendations
        </Link>
      </Button>
    </CardContent>
  </Card>
)

/**
 * Main shortlist page component
 */
export default function ShortlistPage() {
  // State management
  const [colleges, setColleges] = useState(initialCollegesData)
  const [sortConfig, setSortConfig] = useState({ key: null, direction: null, type: null })
  const [loading, setLoading] = useState(true)
  const { getToken } = useAuth()
  const router = useRouter()
  const { toast } = useToast()
  const { addCollege, removeCollege } = useShortlist()
  const [ studentCount, setStudentCount ] = useState(0)

  /**
   * Handle bookmark toggle for colleges
   */
  const handleToggleBookmark = async (collegeId) => {
    const success = await removeCollege(collegeId)
    if (success) {
      setColleges(prev => prev.map(c => c.id === collegeId ? { ...c, bookmarked: false } : c))
    }
  }

  /**
   * Get only bookmarked colleges
   */
  const bookmarkedColleges = useMemo(() => 
    colleges.filter(c => c.bookmarked), 
    [colleges]
  )

  /**
   * Sort colleges based on current sort configuration
   */
  const sortedColleges = useMemo(() => {
    let sortableItems = [...bookmarkedColleges]
    
    if (sortConfig.key !== null && sortConfig.direction !== null) {
      sortableItems.sort((a, b) => {
        let aVal = a[sortConfig.key]
        let bVal = b[sortConfig.key]

        // Handle different data types for sorting
        if (sortConfig.type === 'percentage') {
          aVal = parseFloat(String(aVal).replace('%', ''))
          bVal = parseFloat(String(bVal).replace('%', ''))
        } else if (sortConfig.type === 'currency') {
          aVal = parseFloat(String(aVal).replace(/[$,]/g, ''))
          bVal = parseFloat(String(bVal).replace(/[$,]/g, ''))
        } else if (sortConfig.type === 'match') {
          const matchOrder = { 'Safety': 1, 'Target': 2, 'Reach': 3 }
          aVal = matchOrder[aVal] || 0
          bVal = matchOrder[bVal] || 0
        } else if (typeof aVal === 'string' && typeof bVal === 'string') {
          aVal = aVal.toLowerCase()
          bVal = bVal.toLowerCase()
        }

        // Apply sort direction
        if (aVal < bVal) return sortConfig.direction === 'asc' ? -1 : 1
        if (aVal > bVal) return sortConfig.direction === 'asc' ? 1 : -1
        return 0
      })
    }
    
    return sortableItems
  }, [bookmarkedColleges, sortConfig])

  /**
   * Handle column sorting
   */
  const handleSort = (key, direction, type) => {
    // Reset sort if clicking the same column in desc order (third click)
    if (sortConfig.key === key && sortConfig.direction === 'desc') {
      setSortConfig({ key: null, direction: null, type: null })
    } else {
      setSortConfig({ key, direction: direction || 'asc', type })
    }
  }
  
  /**
   * Handle connect CTA click
   */
  const connectCTAHandler = () => {
    // Navigate to paywall or connect page
    router.push('/paywall/')
  }

  useEffect(() => {
    const fetchShortlist = async () => {
      const token = getToken()
      if (!token) {
        router.push('/profile-setup')
        return
      }
      const startTime = Date.now() 
      const timer = setTimeout(() => { setLoading(true) }, 1000) // show loading if fetch takes >1 sec
      try {
        const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/shortlist/`, {
          headers: { 'Authorization': `Token ${token}` }
        })
        if (!res.ok) {
          if (res.status === 401) {
            router.push('/profile-setup')
          }
          const data = await res.json()
          throw new Error(data.error || 'Failed to fetch shortlist data')
        }
        const data = await res.json()
        const transformedData = data.shortlist_data.map(item => ({
          id: item.unitid,
          name: item.college_name,
          logo: item.logo || '',
          location: item.location || '',
          type: item.type || '',
          acceptance: item.acceptance_rate,
          cost: Number(item.annual_cost),
          match: item.match || '',
          bookmarked: true,
          studentAvatars: item.studentAvatars || [],
          connectLink: `/paywall/`,
          collegeLink: `/colleges/${item.unitid}`
        }))
        setStudentCount(data.connect_with_trailblazers || 0)
        setColleges(transformedData)
      } catch (error) {
        toast({ title: error.message, variant: 'destructive' })
      } finally {
        clearTimeout(timer)
        setLoading(false)
      }
    }
    fetchShortlist()
  }, [])

  return (
    <>
      <Head>
        <title>Trailblazer V2 - College Shortlist</title>
        <meta name="description" content="Compare your saved colleges and connect with current students" />
      </Head>

      <div className="bg-gray-50 min-h-screen flex flex-col">
          <DashboardLayout>
            {/* Main content area */}
              {/* Page header */}
              <div className="mb-6">
                <h1 className="text-3xl font-bold text-gray-800 mb-2">
                  Your College Shortlist
                </h1>
                <p className="text-gray-600">
                  Compare your saved colleges and connect with current students.
                </p>
              </div>

              {/* Connect CTA - only show when there are bookmarked colleges */}
              {bookmarkedColleges.length > 0 && (
                <ConnectTrailblazersCard
                  studentCount={studentCount}
                  avatars={studentPlaceholders}
                  onConnect={connectCTAHandler}
                />
              )}

              {/* Main content - show loading indicator, empty state, or college list */}
              { loading ? (
                <div className="space-y-4">
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                  <Skeleton className="h-8 w-full" />
                </div>
              ) : bookmarkedColleges.length === 0 ? (
                <EmptyStateComponent />
              ) : (
                <div className="space-y-6">
                  <DesktopShortlistTable
                    colleges={sortedColleges}
                    sortConfig={sortConfig}
                    onSort={handleSort}
                    onToggleBookmark={handleToggleBookmark}
                  />
                  <MobileShortlistCardsContainer
                    colleges={sortedColleges}
                    onToggleBookmark={handleToggleBookmark}
                  />
                </div>
              ) }
          </DashboardLayout>
        </div>
    </>
  )
}
