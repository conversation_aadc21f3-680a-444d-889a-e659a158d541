# General
# ------------------------------------------------------------------------------
USE_DOCKER=yes
IPYTHONDIR=/app/.ipython

# Frontend
# ------------------------------------------------------------------------------
FRONTEND_BASE_URL=http://localhost:3002

#Mapbox API Key
MAPBOX_API_KEY = pk.eyJ1IjoidHJhaWxibGF6ZXJnbyIsImEiOiJjbTR1MnQ2dHUwaGJjMnFvcGJhbHBmY2ppIn0.Ge5XvrCTjh1uUfx1YKwgDQ

# Google Calendar
# ------------------------------------------------------------------------------
GOOGLE_CLIENT_ID=532478308970-9q6esda77m5nsj4nv4onruvg5kn7bp4i.apps.googleusercontent.com
GOOGLE_CLIENT_SECRET=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
GOOGLE_REFRESH_TOKEN=1//012rK-bOeN0y4CgYIARAAGAESNwF-L9IrDGqQBO3ghpeF7z2bTusvG6RvleQiJIUCzCmV9t3OLeN4K-SBmDkMHQcxRrtm_s4Hgls
GOOGLE_CALENDAR_ID=<EMAIL>
# Email
# ------------------------------------------------------------------------------
EMAIL_BACKEND=django.core.mail.backends.console.EmailBackend
EMAIL_HOST=smtp.sendgrid.net
EMAIL_HOST_USER=apikey
EMAIL_HOST_PASSWORD=*********************************************************************
EMAIL_PORT=587
EMAIL_USE_TLS=True
DEFAULT_FROM_EMAIL=<EMAIL>

#Trailblazer API Key 
API_KEY=cf33aed888a282cee2cafd75fae80ef26cacffe6cdf3871f0a759834ca4cf014

# OpenAI API Key for embeddings
OPENAI_API_KEY=********************************************************************************************************************************************************************