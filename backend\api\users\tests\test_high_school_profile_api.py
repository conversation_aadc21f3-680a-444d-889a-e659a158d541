import json
import requests
from unittest.mock import patch
from django.urls import reverse
from rest_framework import status
from rest_framework.test import APITestCase
from api.users.models import User, HighSchoolStudentProfile
from api.users.tests.factories import UserFactory, HighSchoolStudentProfileFactory
from api.organizations.tests.factories import OrganizationFactory

class HighSchoolStudentProfileCreateTests(APITestCase):
    """
    Test cases for the high school student profile creation API endpoint.
    """
    
    def setUp(self):
        """Set up test data."""
        self.organization = OrganizationFactory()
        
        # Create a high school student user
        self.user = UserFactory(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
        self.profile = self.user.high_school_student_profile
        self.profile.organization = self.organization
        self.profile.save()
        
        # Create a college student user (for permission tests)
        self.college_student = UserFactory(user_type=User.COLLEGE_STUDENT_TYPE)
        
        # URL for the API endpoint
        self.url = reverse('high-school-student-profile-create')
        
        # Valid data for testing
        self.valid_data = {
            'unweighted_gpa': 3.8,
            'weighted_gpa': 4.2,
            'sat_math_score': 750,
            'sat_reading_score': 720,
            'act_score': 32,
            'high_school_name': 'Lincoln High School',
            'graduation_year': 2025,
            'intended_majors': ['Computer Science', 'Mathematics'],
            'extracurriculars': "Debate and Chess Club",
            'interests': "Programming and reading",
            'college_type_preferences': ['Public', 'Private'],
            'location_type_preferences': ['Urban', 'Suburban'],
            'geographic_preference_type': 'states',
            'preferred_states': ['CA', 'NY', 'MA'],
            'preferred_radius': '0-20',
            'gender': 'female',
            'ethnicity': 'black',
            'household_income': '48k_75k',
            'financial_aid_need': 'medium'
        }
        
        # Log in as the high school student
        self.client.force_authenticate(user=self.user)
    
    def test_create_profile_success(self):
        """Test successfully creating a high school student profile."""
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['unweighted_gpa'], 3.8)
        self.assertEqual(response.data['weighted_gpa'], 4.2)
        
        # Verify the data was saved to the database
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.unweighted_gpa, 3.8)
        self.assertEqual(self.profile.act_score, 32)
        self.assertEqual(self.profile.intended_majors, ['Computer Science', 'Mathematics'])
    
    def test_create_profile_invalid_unweighted_gpa(self):
        """Test validation for unweighted GPA."""
        # GPA too high
        data = self.valid_data.copy()
        data['unweighted_gpa'] = 4.1
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('unweighted_gpa', response.data)
        
        # GPA too low
        data['unweighted_gpa'] = -0.1
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('unweighted_gpa', response.data)
    
    def test_create_profile_invalid_weighted_gpa(self):
        """Test validation for weighted GPA."""
        # GPA too high
        data = self.valid_data.copy()
        data['weighted_gpa'] = 5.1
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('weighted_gpa', response.data)
        
        # GPA too low
        data['weighted_gpa'] = -0.1
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('weighted_gpa', response.data)
    
    def test_create_profile_invalid_sat_scores(self):
        """Test validation for SAT scores."""
        # Math score too high
        data = self.valid_data.copy()
        data['sat_math_score'] = 801
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('sat_math_score', response.data)
        
        # Math score too low
        data['sat_math_score'] = 199
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('sat_math_score', response.data)
        
        # Reading score too high
        data = self.valid_data.copy()
        data['sat_reading_score'] = 801
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('sat_reading_score', response.data)
        
        # Reading score too low
        data['sat_reading_score'] = 199
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('sat_reading_score', response.data)
    
    def test_create_profile_invalid_act_score(self):
        """Test validation for ACT score."""
        # Score too high
        data = self.valid_data.copy()
        data['act_score'] = 37
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('act_score', response.data)
        
        # Score too low
        data['act_score'] = 0
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('act_score', response.data)
    
    def test_create_profile_invalid_states(self):
        """Test validation for preferred states."""
        data = self.valid_data.copy()
        data['geographic_preference_type'] = 'states'
        data['preferred_states'] = ['CA', 'XX', 'ZZ']  # Invalid state codes
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('preferred_states', response.data)
    
    def test_create_profile_invalid_zip(self):
        """Test validation for preferred ZIP code."""
        data = self.valid_data.copy()
        data['geographic_preference_type'] = 'zip'
        data['preferred_zip'] = 'invalid'  # Invalid ZIP format
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('preferred_zip', response.data)
        
        # Missing ZIP code
        data['preferred_zip'] = None
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('preferred_zip', response.data)
    
    @patch('api.users.views.HighSchoolStudentProfileCreateView.validate_zip_with_mapbox')
    def test_create_profile_zip_validation(self, mock_validate_zip):
        """Test validation of ZIP code with Mapbox API."""
        # ZIP code validation fails
        mock_validate_zip.return_value = False
        
        data = self.valid_data.copy()
        data['geographic_preference_type'] = 'zip'
        data['preferred_zip'] = '12345'
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('preferred_zip', response.data)
        
        # ZIP code validation succeeds
        mock_validate_zip.return_value = True
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
    
    def test_create_profile_invalid_radius(self):
        """Test validation for preferred radius."""
        data = self.valid_data.copy()
        data['preferred_radius'] = 'Invalid radius'
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('preferred_radius', response.data)
    
    def test_create_profile_missing_optional_fields(self):
        """Test that optional fields can be omitted."""
        data = {
            'unweighted_gpa': 3.8,
            'weighted_gpa': 4.2,
            # Missing other fields
        }
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(response.data['unweighted_gpa'], 3.8)
        self.assertEqual(response.data['weighted_gpa'], 4.2)
    
    def test_create_profile_invalid_user_type(self):
        """Test that only high school students can access the endpoint."""
        # Log in as a college student
        self.client.force_authenticate(user=self.college_student)
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    def test_create_profile_unauthenticated(self):
        """Test that unauthenticated users cannot access the endpoint."""
        # Log out
        self.client.force_authenticate(user=None)
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    @patch('requests.get')
    def test_mapbox_api_failure(self, mock_get):
        """Test handling of Mapbox API failures."""
        # Simulate API failure
        mock_get.side_effect = requests.RequestException("API error")
        
        data = self.valid_data.copy()
        data['geographic_preference_type'] = 'zip'
        data['preferred_zip'] = '12345'
        
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_500_INTERNAL_SERVER_ERROR)
        self.assertIn('error', response.data)