"use client"

import React, { useState } from 'react'
import Link from 'next/link'
import Image from 'next/image'
import { usePathname } from 'next/navigation'
import { useRouter } from 'next/navigation'
import {
    Menu, RefreshCw, LayoutGrid, Bookmark, Search, LogOut
} from 'lucide-react'

import { Button } from "@/components/ui/button"
import {
    She<PERSON>,
    SheetContent,
    SheetHeader,
    SheetTitle,
} from "@/components/ui/sheet"
import { TooltipProvider, Tooltip, TooltipTrigger, TooltipContent } from "@/components/ui/tooltip"
import { useAuth } from '@/context/AuthProvider';

/**
 * Header component containing logo and refresh button
 * Responsive design with mobile menu toggle
 */
const PageHeader = ({ onRefresh, onMobileMenuToggle, remainingRequests }) => {
  const pathname = usePathname()
  const isRecommendationsPage = pathname === "/recommendations"
  const router = useRouter()

  return (
      <header className="bg-white shadow-sm sticky top-0 z-40 h-16" role="banner">
          <div className="mx-auto px-4 h-full flex items-center justify-between">
              <div className="flex items-center">
                  {/* Mobile menu toggle button - only visible on mobile */}
                  <Button 
                      variant="ghost" 
                      size="icon" 
                      className="md:hidden mr-3 text-gray-600 hover:text-primary" 
                      onClick={onMobileMenuToggle}
                      aria-label="Toggle mobile menu"
                  >
                      <Menu className="w-6 h-6" />
                  </Button>
                  {/* Logo with proper alt text for accessibility */}
                  <Link href="/recommendations" className="flex items-center" aria-label="Trailblazer Home">
                      <Image 
                          src="/logo.svg"
                          alt="Trailblazer Logo" 
                          width={150} 
                          height={24} 
                          className="h-6 w-auto px-4" 
                          priority
                      />
                  </Link>
              </div>
              {/* Get New Recommendations button */}
              {isRecommendationsPage && (
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <span>
                        <Button 
                          variant="outline" 
                          className="text-primary border-primary hover:bg-primary/10"
                          disabled={remainingRequests === 0}
                          onClick={() => {
                            if (remainingRequests === 0) {
                              // Optionally show a toast or message here
                              return
                            }
                            router.push("/profile-update") // Redirect to profile update
                          }}
                          aria-label="Get new college recommendations"
                        >
                          <RefreshCw className="w-4 h-4 md:mr-2" />
                          <span className="hidden md:inline">
                            Get New Recommendations ({remainingRequests !== null ? remainingRequests : "..."})
                          </span>
                        </Button>
                      </span>
                    </TooltipTrigger>
                    {remainingRequests === 0 && (
                      <TooltipContent side="top">
                        You can generate up to 3 recommendations every 6 hours.
                      </TooltipContent>
                    )}
                  </Tooltip>
                </TooltipProvider>
              )}
          </div>
      </header>
  )
}

/**
* Individual navigation link component with active state styling
*/
const SidebarNavLink = ({ href, icon: Icon, children, isActive, onClick }) => {
  return (
      <Link 
          href={href} 
          onClick={onClick}
          className={`flex items-center px-4 py-3 rounded-default font-medium transition-colors duration-200 ${
              isActive 
                  ? 'bg-primary/15 text-primary font-semibold' 
                  : 'text-gray-700 hover:bg-primary/10 hover:text-primary'
          }`}
          aria-current={isActive ? 'page' : undefined}
      >
          <Icon className="mr-3 w-5 h-5" aria-hidden="true" />
          <span>{children}</span>
      </Link>
  )
}

/**
* Desktop sidebar navigation component
* Fixed positioning with proper navigation structure
*/
const SidebarNav = () => {
  const pathname = usePathname()
  const isActive = (href) => pathname === href
  const { logout } = useAuth()

  const handleLogout = (e) => {
    e.preventDefault()
    console.log("Logging out...")
    logout("/profile-setup")
  }

  return (
      <aside 
          className="sidebar w-64 bg-white shadow-lg p-4 space-y-4 fixed top-16 left-0 h-[calc(100vh-4rem)] border-r border-gray-200 hidden md:block"
          role="navigation"
          aria-label="Main navigation"
      >
          <nav>
              <ul className="space-y-2" role="list">
                  <SidebarNavLink href="/recommendations" icon={LayoutGrid} isActive={isActive("/recommendations")}>
                      My Recommendations
                  </SidebarNavLink>
                  <SidebarNavLink href="/shortlist" icon={Bookmark} isActive={isActive("/shortlist")}>
                      My Shortlist
                  </SidebarNavLink>
                  <SidebarNavLink href="/explore" icon={Search} isActive={isActive("/explore")}>
                      Explore Colleges
                  </SidebarNavLink>
              </ul>
          </nav>
          {/* Logout button positioned at bottom */}
          <div className="absolute bottom-4 left-0 right-0 px-4 space-y-1">
              <SidebarNavLink href="#" icon={LogOut} onClick={handleLogout}>
                  Log out
              </SidebarNavLink>
          </div>
      </aside>
  )
}

/**
* Mobile sidebar using Sheet component for slide-out navigation
*/
const MobileSidebar = ({ isOpen, onOpenChange }) => {
  const pathname = usePathname()
  const isActive = (href) => pathname === href

  const { logout } = useAuth()

  const handleLogout = (e) => {
    e.preventDefault()
    console.log("Logging out...")
    logout("/profile-setup")
  }

  return (
      <Sheet open={isOpen} onOpenChange={onOpenChange}>
          <SheetContent side="left" className="w-64 p-0 pt-16 bg-white">
              <SheetHeader className="sr-only">
                  <SheetTitle>Navigation Menu</SheetTitle>
              </SheetHeader>
              <nav className="p-4" role="navigation" aria-label="Mobile navigation">
                  <ul className="space-y-2" role="list">
                      <SidebarNavLink href="/recommendations" icon={LayoutGrid} isActive={isActive("/recommendations")}>
                          My Recommendations
                      </SidebarNavLink>
                      <SidebarNavLink href="/shortlist" icon={Bookmark} isActive={isActive("/shortlist")}>
                          My Shortlist
                      </SidebarNavLink>
                      <SidebarNavLink href="/explore" icon={Search} isActive={isActive("/explore")}>
                          Explore Colleges
                      </SidebarNavLink>
                  </ul>
              </nav>
              <div className="absolute bottom-4 left-0 right-0 px-4 space-y-1">
                  <SidebarNavLink href="#" icon={LogOut} onClick={handleLogout}>
                      Log out
                  </SidebarNavLink>
              </div>
          </SheetContent>
      </Sheet>
  )
}

const DashboardLayout = ({ children, onRefresh }) => {
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
  const [remainingRequests, setRemainingRequests] = React.useState(null)
  React.useEffect(() => {
    async function fetchRateLimit() {
      const token = localStorage.getItem("trailblazers.auth.token")
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/highschoolstudentprofile/recommendation-throttle-status/`, {
          headers: { Authorization: "Token " + token }
        })
        if (!response.ok) {
          throw new Error("Failed to fetch rate limit status")
        }
        const data = await response.json()
        setRemainingRequests(data.remaining_requests)
      } catch (error) {
        console.error("Error fetching rate limit:", error)
      }
    }
    fetchRateLimit()
  }, [])

  return (
    <div className="bg-background min-h-screen text-foreground font-body text-sm">
      {/* Header */}
      <PageHeader 
        onMobileMenuToggle={() => setIsMobileMenuOpen(true)} 
        onRefresh={onRefresh} // optional
        remainingRequests={remainingRequests}
      />

      <div className="flex">
        {/* Desktop sidebar */}
        <SidebarNav />

        {/* Mobile sidebar */}
        <MobileSidebar 
          isOpen={isMobileMenuOpen} 
          onOpenChange={setIsMobileMenuOpen} 
        />

        {/* Page content */}
        <main className="flex-1 md:ml-64 mt-6 p-6 overflow-y-auto h-[calc(100vh-4rem)]">
          {children}
        </main>
      </div>
    </div>
  )
}

export default DashboardLayout