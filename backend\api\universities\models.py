from django.db import models
from pgvector.django import <PERSON><PERSON><PERSON><PERSON>
from django.conf import settings

class University(models.Model):
    """
    University model representing educational institutions with their detailed information
    and vector embeddings for similarity search.
    """
    # Basic information
    unitid = models.Char<PERSON>ield(max_length=50, primary_key=True, help_text="Unique identifier for the institution")
    institution = models.CharField(max_length=255, help_text="Name of the institution")
    city = models.CharField(max_length=100, help_text="City where the institution is located")
    state_territory = models.CharField(max_length=50, help_text="State or territory where the institution is located")
    zip_code = models.Char<PERSON>ield(max_length=20, help_text="Zip code of the institution")
    institution_website = models.URLField(max_length=255, null=True, blank=True, help_text="Website URL of the institution")
    
    # Degree information
    predominant_degree_awarded = models.Char<PERSON>ield(max_length=100, null=True, blank=True, help_text="The most commonly awarded degree at the institution")
    highest_degree_awarded = models.Cha<PERSON><PERSON><PERSON>(max_length=100, null=True, blank=True, help_text="The highest degree that can be earned at the institution")
    
    # Classification
    public_private = models.Char<PERSON>ield(max_length=50, null=True, blank=True, help_text="Indicates whether the institution is public or private")
    region = models.CharField(max_length=100, null=True, blank=True, help_text="Geographical region of the institution")
    locale = models.CharField(max_length=100, null=True, blank=True, help_text="Locale type of the institution (e.g., urban, rural)")
    special_focus = models.CharField(max_length=255, null=True, blank=True, help_text="Special focus of the institution, if any")
    
    # Institution type indicators
    is_hbcu = models.BooleanField(null=True, blank=True, help_text="Indicates if the institution is an HBCU")
    is_tribal = models.BooleanField(null=True, blank=True, help_text="Indicates if the institution is a tribal college or native-serving institution")
    is_aanapisi = models.BooleanField(null=True, blank=True, help_text="Indicates if the institution serves Asian American & Native American Pacific Islander populations")
    is_hsi = models.BooleanField(null=True, blank=True, help_text="Indicates if the institution is Hispanic-serving")
    is_single_gender = models.BooleanField(null=True, blank=True, help_text="Indicates if the institution is single-gender")
    religious_affiliation = models.CharField(max_length=255, null=True, blank=True, help_text="Religious affiliation of the institution, if any")
    
    # Test scores
    sat_reading_25th = models.IntegerField(null=True, blank=True, help_text="25th percentile SAT Reading/Writing score")
    sat_reading_75th = models.IntegerField(null=True, blank=True, help_text="75th percentile SAT Reading/Writing score")
    sat_math_25th = models.IntegerField(null=True, blank=True, help_text="25th percentile SAT Math score")
    sat_math_75th = models.IntegerField(null=True, blank=True, help_text="75th percentile SAT Math score")
    act_25th = models.IntegerField(null=True, blank=True, help_text="25th percentile ACT score")
    act_75th = models.IntegerField(null=True, blank=True, help_text="75th percentile ACT score")
    
    # Admissions
    has_open_admissions = models.BooleanField(null=True, blank=True, help_text="Indicates if the institution has an open admissions policy")
    acceptance_rate = models.FloatField(null=True, blank=True, help_text="Percentage of applicants accepted")
    
    # Enrollment
    undergrad_count = models.IntegerField(null=True, blank=True, help_text="Number of undergraduate students")
    
    # Student demographics (percentages)
    pct_white_students = models.FloatField(null=True, blank=True, help_text="Percentage of white students")
    pct_black_students = models.FloatField(null=True, blank=True, help_text="Percentage of Black students")
    pct_hispanic_students = models.FloatField(null=True, blank=True, help_text="Percentage of Hispanic students")
    pct_asian_students = models.FloatField(null=True, blank=True, help_text="Percentage of Asian students")
    pct_aian_students = models.FloatField(null=True, blank=True, help_text="Percentage of American Indian or Alaska Native students")
    pct_nhpi_students = models.FloatField(null=True, blank=True, help_text="Percentage of Native Hawaiian or Pacific Islander students")
    pct_multiracial_students = models.FloatField(null=True, blank=True, help_text="Percentage of students identifying as two or more races")
    pct_unknown_race_students = models.FloatField(null=True, blank=True, help_text="Percentage of students whose race is unknown")
    pct_men_students = models.FloatField(null=True, blank=True, help_text="Percentage of male students")
    pct_women_students = models.FloatField(null=True, blank=True, help_text="Percentage of female students")
    
    # Faculty information
    student_faculty_ratio = models.FloatField(null=True, blank=True, help_text="Ratio of students to faculty")
    pct_white_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of white faculty")
    pct_black_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of Black faculty")
    pct_hispanic_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of Hispanic faculty")
    pct_asian_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of Asian faculty")
    pct_aian_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of American Indian or Alaska Native faculty")
    pct_nhpi_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of Native Hawaiian or Pacific Islander faculty")
    pct_multiracial_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of faculty identifying as two or more races")
    pct_unknown_race_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of faculty whose race is unknown")
    pct_men_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of male faculty")
    pct_women_faculty = models.FloatField(null=True, blank=True, help_text="Percentage of female faculty")
    
    # Degree associate or bachelor's by field
    degrees_agriculture = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Agriculture")
    degrees_resources = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Natural Resources & Conservation")
    degrees_architecture = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Architecture")
    degrees_ethnic_cultural_gender = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Area, Ethnic, Cultural, Gender, & Group Studies")
    degrees_communication = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Communication")
    degrees_communications_tech = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Communications Technologies")
    degrees_computer_science = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Computer & Information Sciences")
    degrees_culinary = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Personal & Culinary Services")
    degrees_education = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Education")
    degrees_engineering = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Engineering")
    degrees_engineering_tech = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Engineering Technologies")
    degrees_language = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Foreign Languages")
    degrees_family_consumer_science = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Family & Consumer Sciences")
    degrees_legal = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Legal Professions")
    degrees_english = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in English Language & Literature")
    degrees_humanities = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Liberal Arts & Sciences")
    degrees_library = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Library Science")
    degrees_biological = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Biological & Biomedical Sciences")
    degrees_mathematics = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Mathematics & Statistics")
    degrees_military = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Military Technologies")
    degrees_multidiscipline = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Multi/Interdisciplinary Studies")
    degrees_parks_recreation_fitness = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Parks, Recreation, Leisure, & Fitness Studies")
    degrees_philosophy = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Philosophy & Religious Studies")
    degrees_theology = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Theology & Religious Vocations")
    degrees_physical_science = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Physical Sciences")
    degrees_science_tech = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Science Technologies")
    degrees_psychology = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Psychology")
    degrees_security_law_enforcement = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Homeland Security, Law Enforcement, etc.")
    degrees_public_admin = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Public Administration & Social Service")
    degrees_social_science = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Social Sciences")
    degrees_construction = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Construction Trades")
    degrees_mechanic_repair_tech = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Mechanic & Repair Technologies")
    degrees_precision_production = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Precision Production")
    degrees_transportation = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Transportation & Materials Moving")
    degrees_visual_performing = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Visual & Performing Arts")
    degrees_health = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Health Professions")
    degrees_business = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Business")
    degrees_history = models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in History")
    
    # Degree percentages by field
    pct_degrees_agriculture = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Agriculture")
    pct_degrees_resources = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Natural Resources & Conservation")
    pct_degrees_architecture = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Architecture")
    pct_degrees_ethnic_cultural_gender = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Area, Ethnic, Cultural, Gender, & Group Studies")
    pct_degrees_communication = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Communication")
    pct_degrees_communications_tech = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Communications Technologies")
    pct_degrees_computer_science = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Computer & Information Sciences")
    pct_degrees_culinary = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Personal & Culinary Services")
    pct_degrees_education = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Education")
    pct_degrees_engineering = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Engineering")
    pct_degrees_engineering_tech = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Engineering Technologies")
    pct_degrees_language = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Foreign Languages")
    pct_degrees_family_consumer_science = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Family & Consumer Sciences")
    pct_degrees_legal = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Legal Professions")
    pct_degrees_english = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in English Language & Literature")
    pct_degrees_humanities = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Liberal Arts & Sciences")
    pct_degrees_library = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Library Science")
    pct_degrees_biological = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Biological & Biomedical Sciences")
    pct_degrees_mathematics = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Mathematics & Statistics")
    pct_degrees_military = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Military Technologies")
    pct_degrees_multidiscipline = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Multi/Interdisciplinary Studies")
    pct_degrees_parks_recreation_fitness = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Parks, Recreation, Leisure, & Fitness Studies")
    pct_degrees_philosophy = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Philosophy & Religious Studies")
    pct_degrees_theology = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Theology & Religious Vocations")
    pct_degrees_physical_science = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Physical Sciences")
    pct_degrees_science_tech = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Science Technologies")
    pct_degrees_psychology = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Psychology")
    pct_degrees_security_law_enforcement = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Homeland Security, Law Enforcement, Firefighting & Related Protective Services")
    pct_degrees_public_admin = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Public Administration & Social Service")
    pct_degrees_social_science = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Social Sciences")
    pct_degrees_construction = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Construction Trades")
    pct_degrees_mechanic_repair_tech = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Mechanic & Repair Technologies")
    pct_degrees_precision_production = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Precision Production")
    pct_degrees_transportation = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Transportation & Materials Moving")
    pct_degrees_visual_performing = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Visual & Performing Arts")
    pct_degrees_health = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Health Professions")
    pct_degrees_business = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in Business")
    pct_degrees_history = models.FloatField(null=True, blank=True, help_text="Percentage of degrees awarded in History")
    
    # Retention and completion
    retention_rate = models.FloatField(null=True, blank=True, help_text="Percentage of students retained after their first year")
    graduation_rate = models.FloatField(null=True, blank=True, help_text="Percentage of students who graduate within a standard timeframe")
    completion_rate_150_pooled = models.FloatField(null=True, blank=True, help_text="Completion rate within 150% of expected time (pooled 2-year avg)")
    completion_rate_150 = models.FloatField(null=True, blank=True, help_text="Completion rate within 150% of expected time")
    completion_rate_150_white = models.FloatField(null=True, blank=True, help_text="Completion rate for White students within 150% of expected time")
    completion_rate_150_black = models.FloatField(null=True, blank=True, help_text="Completion rate for Black students within 150% of expected time")
    completion_rate_150_hispanic = models.FloatField(null=True, blank=True, help_text="Completion rate for Hispanic students within 150% of expected time")
    completion_rate_150_asian = models.FloatField(null=True, blank=True, help_text="Completion rate for Asian students within 150% of expected time")
    completion_rate_150_aian = models.FloatField(null=True, blank=True, help_text="Completion rate for American Indian or Alaska Native students within 150% of expected time")
    completion_rate_150_nhpi = models.FloatField(null=True, blank=True, help_text="Completion rate for Native Hawaiian or Pacific Islander students within 150% of expected time")
    completion_rate_150_multiracial = models.FloatField(null=True, blank=True, help_text="Completion rate for students of two or more races within 150% of expected time")
    completion_rate_150_unknown_race = models.FloatField(null=True, blank=True, help_text="Completion rate for students with unknown race within 150% of expected time")
    
    # Financial information
    avg_annual_cost = models.FloatField(null=True, blank=True, help_text="Average annual cost of attendance")
    avg_net_price_0_30k = models.FloatField(null=True, blank=True, help_text="Average net price for families with income $0 - $30,000")
    avg_net_price_30k_48k = models.FloatField(null=True, blank=True, help_text="Average net price for families with income $30,001 - $48,000")
    avg_net_price_48k_75k = models.FloatField(null=True, blank=True, help_text="Average net price for families with income $48,001 - $75,000")
    avg_net_price_75k_110k = models.FloatField(null=True, blank=True, help_text="Average net price for families with income $75,001 - $110,000")
    avg_net_price_110k_plus = models.FloatField(null=True, blank=True, help_text="Average net price for families with income above $110,000")
    
    # Financial aid
    pct_students_pell = models.FloatField(null=True, blank=True, help_text="Percentage of students awarded Pell grants")
    pct_students_federal_loan = models.FloatField(null=True, blank=True, help_text="Percentage of students with federal loans")
    
    # Debt and earnings
    median_debt_pell = models.FloatField(null=True, blank=True, help_text="Median debt amount for Pell grant recipients")
    median_debt_firstgen = models.FloatField(null=True, blank=True, help_text="Median debt amount for first-generation college students")
    median_earnings_10yrs = models.FloatField(null=True, blank=True, help_text="Median earnings 10 years after entry into institution")
    earnings_25th_pctl_10yrs = models.FloatField(null=True, blank=True, help_text="25th percentile earnings 10 years after entry")
    earnings_75th_pctl_10yrs = models.FloatField(null=True, blank=True, help_text="75th percentile earnings 10 years after entry")
    
    # Embedding vector for similarity search
    embedding = VectorField(
        dimensions=1536,  # Dimension for text-embedding-3-small
        null=True,
        blank=True,
        help_text="Vector embedding generated from university information for similarity search"
    )
    
    def __str__(self):
        return self.institution
        
    class Meta:
        verbose_name = "University"
        verbose_name_plural = "Universities"
        ordering = ['institution']

class ShortlistItem(models.Model):
    """
    Model representing a university added to a high school student's shortlist.
    """
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='shortlist_items'
    )
    university = models.ForeignKey(
        'University',
        on_delete=models.CASCADE,
        related_name='shortlisted_by',
        to_field='unitid'
    )
    created_at = models.DateTimeField(auto_now_add=True)
    
    class Meta:
        unique_together = ('user', 'university')
        ordering = ['-created_at']
        verbose_name = "Shortlist Item"
        verbose_name_plural = "Shortlist Items"
    
    def __str__(self):
        return f"{self.user.email} - {self.university.institution}"