# Generated by Django 4.2.13 on 2025-06-06 17:24

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0026_alter_highschoolstudentprofile_grade_level_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='ethnicity',
            field=models.CharField(blank=True, choices=[('native_american', 'Native American or Alaska Native'), ('asian', 'Asian'), ('black', 'Black or African American'), ('hispanic', 'Hispanic or Latine'), ('middle_eastern', 'Middle Eastern or North African'), ('native_hawaiian', 'Native Hawaiian or Pacific Islander'), ('white', 'White'), ('multiracial', 'Multiracial'), ('other', 'Other')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='financial_aid_need',
            field=models.Char<PERSON>ield(blank=True, choices=[('high', 'High need'), ('medium', 'Medium need'), ('low', 'Low need'), ('none', 'No financial aid need')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='gender',
            field=models.CharField(blank=True, choices=[('female', 'Female'), ('male', 'Male'), ('non-binary', 'Non-binary'), ('other', 'Other'), ('prefer_not_to_say', 'Prefer not to say')], max_length=20, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='geographic_preference_type',
            field=models.CharField(choices=[('states', 'States'), ('zip', 'ZIP Code')], default='states', max_length=20),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='household_income',
            field=models.CharField(blank=True, choices=[('under_30k', 'Under $30,000'), ('30k_48k', '$30,001-$48,000'), ('48k_75k', '$48,001-$75,000'), ('75k_100k', '$75,001-$100,000'), ('100k_plus', '$100,001+')], max_length=50, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='preferred_radius',
            field=models.CharField(blank=True, choices=[('0-20', '0-20 miles (short drive)'), ('21-50', '21-50 miles (medium drive)'), ('51-200', '51-200 miles (long drive)'), ('201-500', '201-500 miles (short flight)'), ('501+', '501+ miles (long flight)')], help_text='Preferred radius from ZIP code', max_length=50, null=True),
        ),
    ]
