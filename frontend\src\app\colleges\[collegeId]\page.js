"use client"

import React, { useState, useEffect } from 'react'
import { useAuth } from '@/context/AuthProvider';
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import Image from 'next/image'
import {
  ArrowLeft, 
  Bookmark, 
  Users, 
  BarChart3, 
  HelpCircle, 
  ExternalLink, 
  Info, 
  BookOpen, 
  CheckCircle2, 
  DollarSign, 
  Award, 
  Users2, 
  MessageCircle, 
  Check, 
  AlertTriangle, 
  RefreshCw
} from 'lucide-react'

// Import actual shadcn/ui components
import { Button } from "@/components/ui/button"
import { Card, CardHeader, CardTitle, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip"
import { Badge } from "@/components/ui/badge"
import { Progress } from "@/components/ui/progress"
import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>ertTitle } from "@/components/ui/alert"
import { useShortlist } from '@/hooks/useShortlist'

/**
 * Page Header Component
 * Displays the main navigation header with logo and back button
 */
function PageHeader() {
  const router = useRouter()
  return (
    <header className="sticky top-0 z-40 bg-white shadow-sm border-b">
      <div className="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-16">
          <div className="flex items-center">
            <Link href="/recommendations" className="flex items-center">
              <Image
                alt="Trailblazer Logo"
                className="h-6 w-auto"
                src="/logo.svg"
                width={150}
                height={24}
                priority
              />
            </Link>
          </div>
          <div className="flex items-center">
            <button
              onClick={() => router.back()}
              className="flex items-center text-sm text-primary font-medium hover:-translate-x-1 transition-transform duration-200 hover:text-primary"
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              <span>Back</span>
            </button>
          </div>
        </div>
      </div>
    </header>
  )
}

/**
 * College Summary Header Component
 * Displays college name, location, type and action buttons
 */
function CollegeSummaryHeader({ name, location, type, collegeId, isBookmarked }) {
  const [isShortlisted, setIsShortlisted] = useState(isBookmarked)
  const { addCollege, removeCollege } = useShortlist()
  
  const handleShortlistToggle = async () => {
    if (!isShortlisted) {
      const success = await addCollege(collegeId)
      if (success) {
        setIsShortlisted(true)
      }
    } else {
      const success = await removeCollege(collegeId)
      if (success) {
        setIsShortlisted(false)
      }
    }
  }

  return (
    <div className="mb-8">
      <div className="flex flex-col md:flex-row md:items-center md:justify-between gap-4">
        <div className="flex-1">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">{name}</h1>
          <p className="text-lg text-gray-600">{location} • {type}</p>
        </div>
        <div className="flex flex-col sm:flex-row gap-3">
          <Button
            variant={isShortlisted ? "default" : "outline"}
            onClick={handleShortlistToggle}
            className="flex items-center"
          >
            <Bookmark className={`w-4 h-4 mr-2 ${isShortlisted ? 'fill-current' : ''}`} />
            {isShortlisted ? 'Added to Shortlist' : 'Add to Shortlist'}
          </Button>
          <Button asChild>
            <Link href={`/paywall/`} className="flex items-center hover:text-white hover:bg-green-500">
              <Users className="w-4 h-4 mr-2" />
              Connect with Students
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

/**
 * Reusable Info Card Component
 * Provides consistent styling for all information cards
 */
function InfoCard({ icon: IconComponent, title, children, className = "", contentClassName = "" }) {
  return (
    <Card className={`h-fit ${className}`}>
      <CardHeader className="pb-4">
        <div className="flex items-center space-x-3">
          {IconComponent && (
            <div className="p-2 bg-green-50 rounded-lg">
              <IconComponent className="w-5 h-5 text-green-600" />
            </div>
          )}
          <CardTitle className="text-xl">{title}</CardTitle>
        </div>
      </CardHeader>
      <CardContent className={contentClassName}>
        {children}
      </CardContent>
    </Card>
  )
}

/**
 * Stat Item Component
 * Displays individual statistics with optional tooltips
 */
function StatItem({ label, value, tooltipText }) {
  return (
    <div className="space-y-1">
      <div className="text-2xl font-semibold text-gray-900">{value}</div>
      <div className="text-sm text-gray-600 flex items-center">
        {label}
        {tooltipText && (
          <TooltipProvider>
            <Tooltip>
              <TooltipTrigger asChild>
                <HelpCircle className="w-4 h-4 text-gray-400 cursor-help ml-1" />
              </TooltipTrigger>
              <TooltipContent className="max-w-xs">
                <p>{tooltipText}</p>
              </TooltipContent>
            </Tooltip>
          </TooltipProvider>
        )}
      </div>
    </div>
  )
}

/**
 * Key Statistics Card Component
 * Displays important college statistics and test scores
 */
function KeyStatsCard({ stats, website }) {
  return (
    <InfoCard icon={BarChart3} title="Key Statistics & Test Scores">
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
        <StatItem 
          label="Undergraduate Students" 
          value={stats.undergradStudents} 
        />
        <StatItem 
          label="Student to Faculty Ratio" 
          value={stats.studentFacultyRatio} 
        />
        <StatItem 
          label="Acceptance Rate" 
          value={stats.acceptanceRate} 
        />
        <StatItem 
          label="SAT Reading/Writing (25th-75th)" 
          value={stats.satReadingWriting} 
        />
        <StatItem 
          label="SAT Math (25th-75th)" 
          value={stats.satMath} 
        />
        <StatItem 
          label="ACT Composite (25th-75th)" 
          value={stats.actComposite} 
        />
        <StatItem 
          label="Retention Rate" 
          value={stats.retentionRate}
          tooltipText="Percentage of students who return after their first year"
        />
        <StatItem 
          label="Graduation Rate" 
          value={stats.graduationRate}
          tooltipText="Share of students who graduated within 8 years of entering this school for the first time"
        />
        <StatItem 
          label="Open Admissions" 
          value={stats.openAdmissions}
          tooltipText="College admissions policy where nearly all applicants who have a high school diploma or equivalent are accepted"
        />
      </div>
      <div className="pt-4 border-t border-gray-200">
        <Button variant="outline" asChild className="border-green-500 text-green-500 hover:bg-green-50">
          <Link href={website} target="_blank" rel="noopener noreferrer" className="flex items-center">
            Visit University Website
            <ExternalLink className="w-4 h-4 ml-2" />
          </Link>
        </Button>
      </div>
    </InfoCard>
  )
}

/**
 * Institution Facts Card Component
 * Displays institution type and characteristics as badges
 */
function InstitutionFactsCard({ facts }) {
  return (
    <InfoCard icon={Info} title="Institution Facts">
      <div className="flex flex-wrap gap-2">
        {facts.map((fact, index) => (
          <Badge key={index} variant="secondary" className="px-3 py-1">
            {fact}
          </Badge>
        ))}
      </div>
    </InfoCard>
  )
}

/**
 * Top Programs Card Component
 * Lists the top academic programs offered by the college
 */
function TopProgramsCard({ programs }) {
  return (
    <InfoCard icon={BookOpen} title="Top Programs & Majors">
      <ul className="space-y-3">
        {programs.map((program, index) => (
          <li key={index} className="flex items-center">
            <CheckCircle2 className="w-4 h-4 text-green-600 mr-3 flex-shrink-0" />
            <span className="text-gray-700">{program}</span>
          </li>
        ))}
      </ul>
    </InfoCard>
  )
}

/**
 * Earnings Range Graph Component
 * Visual representation of post-graduation earnings range
 */
function EarningsRangeGraph({ range }) {
  return (
    <div className="mt-4 space-y-2">
      <div className="flex justify-between text-sm text-gray-600">
        <span>{range.min}</span>
        <span className="font-medium">{range.median}</span>
        <span>{range.max}</span>
      </div>
      <div className="relative h-3 bg-gray-100 rounded-full overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-green-200 to-green-400 rounded-full"></div>
        <div className="absolute top-0 left-1/2 transform -translate-x-1/2 w-1 h-full bg-green-600"></div>
      </div>
      <div className="flex justify-between text-xs text-gray-500">
        <span>25th Percentile</span>
        <span>Median</span>
        <span>75th Percentile</span>
      </div>
    </div>
  )
}

/**
 * Financial Information Card Component
 * Displays cost breakdown and post-graduation earnings
 */
function FinancialInfoCard({ financials }) {
  return (
    <InfoCard icon={DollarSign} title="Financial Information">
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
        {/* Cost Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900">Costs</h4>
          <div>
            <div className="text-3xl font-bold text-gray-900">
              {financials.costAfterAid}
            </div>
            <div className="text-sm text-gray-600">
              Average cost after aid
            </div>
          </div>
          
          <div className="overflow-hidden rounded-lg border">
            <table className="w-full text-sm">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-4 py-2 text-left font-medium text-gray-900">
                    Family Income
                  </th>
                  <th className="px-4 py-2 text-right font-medium text-gray-900">
                    Avg. Cost
                  </th>
                </tr>
              </thead>
              <tbody className="divide-y divide-gray-200">
                {financials.costByIncome.map((item, index) => (
                  <tr key={index} className="hover:bg-gray-50">
                    <td className="px-4 py-2 text-gray-700">{item.income}</td>
                    <td className="px-4 py-2 text-right font-medium text-gray-900">
                      {item.cost}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>

        {/* Earnings Information */}
        <div className="space-y-4">
          <h4 className="text-lg font-semibold text-gray-900">
            Post-Graduation Outcomes
          </h4>
          <div>
            <div className="text-3xl font-bold text-gray-900">
              {financials.medianAnnualEarnings}
            </div>
            <div className="text-sm text-gray-600 flex items-center">
              Median Annual Earnings
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger asChild>
                    <HelpCircle className="w-4 h-4 text-gray-400 cursor-help ml-1" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p>10 years after entry</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            </div>
          </div>
          
          <EarningsRangeGraph range={financials.earningsRange} />
          
          <div className="text-xs text-gray-500 pt-2 border-t">
            <p>
              Note: Earnings can vary significantly by field of study. For detailed 
              earnings data by field/major, please refer to the{' '}
              <Link 
                href={financials.scorecardLink} 
                target="_blank" 
                rel="noopener noreferrer"
                className="text-green-600 hover:underline font-medium inline-flex items-center"
              >
                U.S. Dept. of Education College Scorecard
                <ExternalLink className="w-3 h-3 ml-1" />
              </Link>
            </p>
          </div>
        </div>
      </div>
    </InfoCard>
  )
}

/**
 * Pell Grant and Debt Card Component
 * Shows financial aid and debt information
 */
function PellGrantDebtCard({ pell }) {
  return (
    <InfoCard icon={Award} title="Pell Grant Awards & Debt">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <StatItem 
          label="Students Awarded Pell Grant" 
          value={pell.studentsAwarded} 
        />
        <StatItem 
          label="Median Debt (Pell Recipients)" 
          value={pell.medianDebt} 
        />
      </div>
    </InfoCard>
  )
}

/**
 * Demographics Bar Component
 * Individual progress bar for demographic data
 */
function DemographicsBar({ label, percentage }) {
  return (
    <div className="space-y-2">
      <div className="flex justify-between items-center text-sm">
        <span className="text-gray-700">{label}</span>
        <span className="font-medium text-gray-900">{percentage}%</span>
      </div>
      <Progress value={percentage} className="h-2" />
    </div>
  )
}

/**
 * Demographics Card Component
 * Displays student body demographics with progress bars
 */
function DemographicsCard({ demographics }) {
  return (
    <InfoCard icon={Users2} title="Student Demographics">
      <div className="space-y-8">
        {/* Gender Breakdown */}
        <div>
          <h4 className="text-lg font-semibold text-gray-900 mb-4">
            Gender Breakdown
          </h4>
          <div className="space-y-3">
            {demographics.gender.map((item) => (
              <DemographicsBar 
                key={item.label} 
                label={item.label} 
                percentage={item.value} 
              />
            ))}
          </div>
        </div>

        {/* Ethnicity Breakdown */}
        <div>
          <h4 className="text-lg font-semibold text-gray-900 mb-4">
            Ethnicity Breakdown
          </h4>
          <div className="space-y-3">
            {demographics.ethnicity.map((item) => (
              <DemographicsBar 
                key={item.label} 
                label={item.label} 
                percentage={item.value} 
              />
            ))}
          </div>
        </div>
      </div>
    </InfoCard>
  )
}

/**
 * Connect with Trailblazers Card Component
 * Sidebar component promoting student connections
 */
function ConnectTrailblazersCard({ connectInfo, collegeId }) {
  const displayedAvatars = connectInfo.avatars.slice(0, 4)
  const remainingCount = Math.max(0, connectInfo.studentCount - displayedAvatars.length)

  return (
    <div className="sticky top-24">
      <InfoCard
        icon={MessageCircle}
        title="Connect with Trailblazers"
        className="bg-green-50 border-green-200"
      >
        <div className="space-y-4">
          <p className="text-gray-700">
            Get insights from{' '}
            <span className="font-semibold text-green-700">
              {connectInfo.studentCount} current students
            </span>{' '}
            who can share their experiences.
          </p>

          {/* Student Avatars */}
          <div className="flex items-center">
            {displayedAvatars.map((src, index) => (
              <Avatar 
                key={index} 
                className="w-10 h-10 border-2 border-white -ml-2 first:ml-0"
              >
                <AvatarImage src={src} alt={`Student ${index + 1}`} />
                <AvatarFallback>S{index + 1}</AvatarFallback>
              </Avatar>
            ))}
            {remainingCount > 0 && (
              <div className="w-10 h-10 rounded-full bg-gray-200 border-2 border-white -ml-2 flex items-center justify-center text-xs font-semibold text-gray-600">
                +{remainingCount}
              </div>
            )}
          </div>

          {/* Connection Benefits */}
          <ul className="space-y-2">
            {connectInfo.points.map((point, index) => (
              <li key={index} className="flex items-start text-sm text-gray-600">
                <Check className="w-4 h-4 text-green-600 mr-2 mt-0.5 flex-shrink-0" />
                {point}
              </li>
            ))}
          </ul>

          {/* Call to Action */}
          <Button asChild className="w-full bg-primary hover:bg-green-500 hover:text-white">
            <Link href={`/paywall/`} className="flex items-center justify-center">
              <Users className="w-4 h-4 mr-2" />
              Connect with Students
            </Link>
          </Button>
        </div>
      </InfoCard>
    </div>
  )
}

/**
 * Loading Skeleton Component
 * Displays loading state while data is being fetched
 */
function LoadingSkeleton() {
  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header Skeleton */}
        <div className="mb-8">
          <Skeleton className="h-10 w-3/4 mb-2" />
          <Skeleton className="h-6 w-1/2" />
        </div>

        {/* Content Grid Skeleton */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <div className="lg:col-span-2 space-y-6">
            <Skeleton className="h-80 w-full" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <Skeleton className="h-48 w-full" />
              <Skeleton className="h-48 w-full" />
            </div>
            <Skeleton className="h-96 w-full" />
            <Skeleton className="h-32 w-full" />
            <Skeleton className="h-80 w-full" />
          </div>
          <div className="lg:col-span-1">
            <Skeleton className="h-96 w-full" />
          </div>
        </div>
      </main>
    </div>
  )
}

/**
 * Error Display Component
 * Shows error state with retry functionality
 */
function ErrorDisplay({ onRetry, message }) {
  return (
    <div className="min-h-screen bg-gray-50">
      <PageHeader />
      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="flex items-center justify-center min-h-[60vh]">
          <Alert className="max-w-md border-red-200 bg-red-50">
            <AlertTriangle className="h-4 w-4 text-red-600" />
            <AlertTitle className="text-red-800">Unable to Load College Data</AlertTitle>
            <AlertDescription className="text-red-700 mt-2">
              {message || "We're having trouble retrieving information for this college. Please try again later."}
            </AlertDescription>
            <Button 
              onClick={onRetry} 
              className="mt-4 bg-red-600 hover:bg-red-700"
            >
              <RefreshCw className="w-4 h-4 mr-2" />
              Try Again
            </Button>
          </Alert>
        </div>
      </main>
    </div>
  )
}

/**
 * Main Content Component
 * Renders all college information sections
 */
function LoadedContent({ collegeData }) {
  return (
    <>
      <CollegeSummaryHeader
        name={collegeData.name}
        location={collegeData.location}
        type={collegeData.type}
        collegeId={collegeData.unitId}
        isBookmarked={collegeData.isBookmarked}
      />

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Main Content Column */}
        <div className="lg:col-span-2 space-y-6">
          <KeyStatsCard 
            stats={collegeData.stats} 
            website={collegeData.website} 
          />
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <InstitutionFactsCard facts={collegeData.facts} />
            <TopProgramsCard programs={collegeData.programs} />
          </div>
          
          <FinancialInfoCard financials={collegeData.financials} />
          <PellGrantDebtCard pell={collegeData.pell} />
          <DemographicsCard demographics={collegeData.demographics} />
        </div>

        {/* Sidebar Column */}
        <div className="lg:col-span-1">
          <ConnectTrailblazersCard 
            connectInfo={collegeData.connect}
            collegeId={collegeData.id}
          />
        </div>
      </div>
    </>
  )
}

const getNormalizedWebsite = (url) => {
  if (!url.startsWith('http://') && !url.startsWith('https://')) {
    return `https://${url}`
  }
  return url
}

/**
 * Main College Details Page Component
 * Handles data fetching, loading states, and error handling
 */
export default function CollegeDetailsPage({ params }) {
  // State management for data fetching
  const [isLoading, setIsLoading] = useState(true)
  const [isError, setIsError] = useState(false)
  const [collegeData, setCollegeData] = useState(null)
  const { getToken } = useAuth()

  /**
   * Simulates data fetching from an API
   * In production, this would make actual API calls
   */
  const fetchData = async () => {
    setIsLoading(true)
    setIsError(false)
    try {
      const token = getToken() 
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/universities/details/${params.collegeId}/`, {
        headers: {
          'Authorization': `Token ${token}`,
        },
      })
      if (!response.ok) {
        throw new Error("API response not ok")
      }
      const data = await response.json()
      const formattedData = {
        unitId: data.unitid,
        name: data.name,
        location: data.location,
        type: data.type,
        website: getNormalizedWebsite(data.website),
        stats: {
          undergradStudents: data.stats.undergrad_students,
          studentFacultyRatio: data.stats.student_faculty_ratio,
          acceptanceRate: data.stats.acceptance_rate,
          satReadingWriting: data.stats.sat_reading_writing,
          satMath: data.stats.sat_math,
          actComposite: data.stats.act_composite,
          retentionRate: data.stats.retention_rate,
          graduationRate: data.stats.graduation_rate,
          openAdmissions: data.stats.open_admissions,
        },
        facts: data.facts,
        programs: data.programs,
        financials: {
          costAfterAid: data.financials.cost_after_aid,
          costByIncome: data.financials.cost_by_income.map(item => ({
        income: item.income,
        cost: item.cost,
          })),
          medianAnnualEarnings: data.financials.median_annual_earnings,
          earningsRange: {
        min: data.financials.earnings_range?.min,
        median: data.financials.earnings_range?.median,
        max: data.financials.earnings_range?.max,
          },
          scorecardLink: data.financials.scorecard_url,
        },
        pell: {
          studentsAwarded: data.pell.students_awarded,
          medianDebt: data.pell.median_debt,
        },
        demographics: {
          gender: data.demographics.gender.map(item => ({
        label: item.label,
        value: item.value,
          })),
          ethnicity: data.demographics.ethnicity.map(item => ({
        label: item.label,
        value: item.value,
          })),
        },
        connect: {
          studentCount: data.connect_with_trailblazers,
          avatars: [
            "/avatars/boy_1.png",
            "/avatars/boy_6.png",
            "/avatars/boy_8.png",
            "/avatars/boy_4.png",
        ],
          points: [
            "Ask about campus culture",
            "Get advice on applications",
            "Learn about student life & academics"
          ]
        },
        isBookmarked: data.is_bookmarked,
      }
      setCollegeData(formattedData)
    } catch (error) {
      console.error("Failed to fetch college data:", error)
      setIsError(true)
    } finally {
      setIsLoading(false)
    }
  }

  // Fetch data on component mount
  useEffect(() => {
    fetchData()
  }, [])

  // Render appropriate component based on state
  if (isLoading) return <LoadingSkeleton />
  if (isError) return <ErrorDisplay onRetry={fetchData} />
  if (!collegeData) return <ErrorDisplay onRetry={fetchData} message="College data not found." />

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gray-50">
        <PageHeader />
        <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
          <LoadedContent collegeData={collegeData} />
        </main>
      </div>
    </TooltipProvider>
  )
}