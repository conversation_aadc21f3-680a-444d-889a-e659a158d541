"use client"

import { useState } from 'react'
import { Head<PERSON> } from "@/components/ui/header"
import { IntroBanner } from "@/components/ui/introBanner"
import { AvailabilityForm } from "@/components/ui/availabilityForm"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { useToast } from "@/hooks/use-toast"
import { useRouter } from 'next/navigation'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { formatAvailabilityDataForApi } from "@/lib/availabiltyUtils"
import { getNextOnboardingStep } from "@/lib/utils"
import { Button } from "@/components/ui/button"
import { ArrowRight } from 'lucide-react'
import { Loader2 } from 'lucide-react'

// Main page component
const AvailabilityPage = () => {
  const { toast } = useToast()
  const router = useRouter()
  const { getToken } = useAuth()
  const [isLoading, setIsLoading] = useState(false)

  // Manual testing step, ensure form submission triggers API call and toast notifications
  const onSubmit = async (formData) => {
    setIsLoading(true)
    // Format data for API
    const formattedData = formatAvailabilityDataForApi(formData)
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/onboarding/`, {
        method: 'PATCH',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${getToken()}`, // Ensure auth token is included
        },
        body: JSON.stringify({
          step: 'availability',
          availability: formattedData
        }),
      })
      if (response.ok) {
        toast({
          title: 'Success!', 
          description: 'Availability set successfully' 
        })
        // Navigate to next onboarding step
        router.push(getNextOnboardingStep('CollegeStudent', 'availability'))
      } else {
        console.error('Error Response:', response)
        toast({
          variant: 'destructive',
          description: result.detail || 'Failed to set availability' 
        })
        setIsLoading(false)
      }
    } catch (error) {
      console.error('Error:', error)
      toast({
        variant: 'destructive',
        description: 'An unexpected error occurred' 
      })
      setIsLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex flex-col md:flex-row" id="main-content">
        <div className="p-8 md:w-1/2 lg:w-6/12 flex flex-col mx-8">
          <StepProgressDisplay currentStep={5} totalSteps={6} />
          <h1 className="text-3xl font-semibold mb-8 mt-2">Set your availability</h1>
          <AvailabilityForm
            onSubmit={onSubmit}
            submitButton={(
              <Button type="submit" className="mt-8" disabled={isLoading}>
                  {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Next <ArrowRight className="ml-2 w-6 h-6" />
              </Button>
            )}
          />
        </div>
        <IntroBanner message="" />
      </main>
    </div>
  )
}

export default withAuth(AvailabilityPage)
