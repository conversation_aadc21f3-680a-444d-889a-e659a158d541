# Generated by Django 4.2.13 on 2025-01-22 15:22

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('bookings', '0009_booking_creator_timezone'),
    ]

    operations = [
        migrations.CreateModel(
            name='TrailblazerHours',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('total_hours', models.FloatField(default=0)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('trailblazer', models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Trailblazer Hours',
                'verbose_name_plural': 'Trailblazer Hours',
            },
        ),
    ]
