try:
    import requests_mock
except ImportError:
    import unittest
    raise unittest.SkipTest("Skipping tests in test_views.py because requests_mock is not installed.")

import factory
from unittest.mock import patch
from faker import Faker

from django.urls import reverse
from django.utils import timezone
from django.core.cache import cache
from django.contrib.auth import get_user_model

from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.test import TestCase
from rest_framework.authtoken.models import Token
from django.conf import settings

from api.users.models import (
    User,
    CollegeStudentProfile,
    HighSchoolStudentProfile,
    CounselorAdministratorProfile,
    Availability
)
from api.organizations.models import Organization

from .factories import UserFactory
from api.users.views import CollegeStudentViewSet
from api.organizations.tests.factories import OrganizationFactory


fake = Faker()


class TestUserListTestCase(APITestCase):
    """
    Tests /users list operations.
    """

    def setUp(self):
        self.url = reverse('user-list')
        self.user_data = {
            'first_name': fake.first_name(),
            'last_name': fake.last_name(),
            'email': fake.email(),
            'password': fake.password(),
            'terms_accepted': True,
        }

    def test_post_request_with_no_data_fails(self):
        """"
        Test that a POST request with no data fails.
        """
        response = self.client.post(self.url, {})
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)

    def test_post_request_with_valid_data_succeeds(self):
        """
        Test that a POST request with valid data succeeds.
        """
        response = self.client.post(
            self.url, self.user_data,
            HTTP_ORIGIN='http://new.example.com'
        )
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        user = User.objects.get(pk=response.data.get('id'))
        self.assertEqual(user.email, self.user_data.get('email'))
        self.assertEqual(user.first_name, self.user_data.get('first_name'))
        self.assertEqual(user.last_name, self.user_data.get('last_name'))
        self.assertEqual(str(user.auth_token), response.data.get('auth_token'))

    def test_signup_requires_terms_accepted(self):
        """Test that terms_accepted field is required for signup."""
        data = {
            'email': '<EMAIL>',
            'password': '$@Pasdlkfgh3',
            # terms_accepted is missing
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('terms_accepted', response.data)

    def test_signup_requires_terms_accepted_to_be_true(self):
        """Test that terms_accepted field must be True for signup."""
        data = {
            'email': '<EMAIL>',
            'password': '$@Pasdlkfgh3',
            'terms_accepted': False,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('terms_accepted', response.data)

    def test_signup_with_weak_password_missing_uppercase(self):
        """Test that password must contain at least one uppercase letter."""
        data = {
            'email': '<EMAIL>',
            'password': '$@pasdlkfgh3',  # missing uppercase
            'terms_accepted': True,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_signup_with_weak_password_missing_lowercase(self):
        """Test that password must contain at least one lowercase letter."""
        data = {
            'email': '<EMAIL>',
            'password': '$@PASDLKFGH3',  # missing lowercase
            'terms_accepted': True,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_signup_with_weak_password_missing_digit(self):
        """Test that password must contain at least one digit."""
        data = {
            'email': '<EMAIL>',
            'password': '$@PasdlkfgH',  # missing digit
            'terms_accepted': True,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_signup_with_weak_password_missing_special_char(self):
        """Test that password must contain at least one special character."""
        data = {
            'email': '<EMAIL>',
            'password': 'Pasdlkfgh3',  # missing special character
            'terms_accepted': True,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_signup_with_weak_password_too_short(self):
        """Test that password must be at least 8 characters long."""
        data = {
            'email': '<EMAIL>',
            'password': '$@Pa3',  # too short
            'terms_accepted': True,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_signup_with_auth_token_in_response(self):
        """Test that auth_token is included in the response upon successful registration."""
        data = {
            'email': '<EMAIL>',
            'password': '$@Pasdlkfgh3',
            'terms_accepted': True,
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertIn('auth_token', response.data)
        self.assertTrue(len(response.data['auth_token']) > 0)    


class TestUserDetailTestCase(APITestCase):
    """
    Tests /users detail operations.
    """

    def setUp(self):
        self.user = User.objects.create_user(
            first_name='John',
            last_name='Doe',
            email='<EMAIL>',
            password='Password123!'
        )
        self.profile = self.user.profile
        self.profile.university = 'Test University'
        self.profile.save()

        self.url = reverse('user-detail', kwargs={'pk': self.user.pk})
        self.client.credentials(HTTP_AUTHORIZATION=f'Token {self.user.auth_token}')

    def test_get_request_returns_a_given_user_with_profile(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['user_type'], 'CollegeStudent')
        self.assertIn('profile', response.data)


class CurrentUserViewTest(APITestCase):
    """
    Tests for the /users/me endpoint.
    """

    def test_get_current_user(self):
        """
        Test that a GET request to the /users/me/ endpoint returns the 
        currently authenticated user.
        """
        user = UserFactory()
        self.client.force_authenticate(user=user)
        url = reverse('user-me')
        response = self.client.get(url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], user.email)
        self.assertEqual(response.data['first_name'], user.first_name)
        self.assertEqual(response.data['last_name'], user.last_name)
        self.assertIn('profile', response.data)


class SignUpAPITestCase(APITestCase):

    def setUp(self):
        self.organization = Organization.objects.create(
            name='Test Organization',
            zip_code='12345',
            city='Test City',
            state='Test State',
            registration_number='REG123456'
        )
        self.signup_college_student_url = reverse('signup-college-student')
        self.signup_counselor_admin_url = reverse('signup-counselor-administrator')
        self.signup_high_school_student_url = reverse('signup-high-school-student')

    def test_college_student_signup_success(self):
        data = {
            'email': '<EMAIL>',
            'password': '$@Pasdlkfgh3',
        }
        response = self.client.post(self.signup_college_student_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(User.objects.filter(user_type=User.COLLEGE_STUDENT_TYPE).count(), 1)
        user = User.objects.filter(user_type=User.COLLEGE_STUDENT_TYPE).first()
        self.assertEqual(
            user.email,
            data['email']
        )
        self.assertEqual(response.data['auth_token'], user.auth_token.key)

    def test_college_student_signup_invalid_email(self):
        data = {
            'email': '<EMAIL>',  # Not .edu
            'password': '$@Pasdlkfgh3',
        }
        response = self.client.post(self.signup_college_student_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('email', response.data)

    def test_college_student_signup_weak_password(self):
        data = {
            'email': '<EMAIL>',
            'password': 'weak',  # Does not meet complexity
        }
        response = self.client.post(self.signup_college_student_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('password', response.data)

    def test_counselor_administrator_signup_success(self):
        data = {
            'email': '<EMAIL>',
            'password': '$@Pasdlkfgh3',
        }
        response = self.client.post(self.signup_counselor_admin_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(User.objects.filter(user_type=User.COUNSELOR_ADMINISTRATOR_TYPE).count(), 1)
        user = User.objects.filter(user_type=User.COUNSELOR_ADMINISTRATOR_TYPE).first()
        self.assertEqual(
            user.email,
            data['email']
        )
        self.assertEqual(response.data['auth_token'], user.auth_token.key)

    def test_high_school_student_signup_success(self):
        data = {
            'email': '<EMAIL>',
            'password': '$@Pasdlkfgh3',
            'organization': self.organization.id
        }
        response = self.client.post(self.signup_high_school_student_url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(
            User.objects.filter(user_type=User.HIGH_SCHOOL_STUDENT_TYPE).count(), 1
        )
        user = User.objects.filter(user_type=User.HIGH_SCHOOL_STUDENT_TYPE).first()
        self.assertEqual(
            user.email,
            data['email']
        )
        self.assertEqual(response.data['auth_token'], user.auth_token.key)


class CollegeStudentAPITestCase(APITestCase):
    def setUp(self):
        self.organization = Organization.objects.create(
            name='Test Org',
            zip_code='12345',
            city='Test City',
            state='Test State'
        )

        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        CollegeStudentProfile.objects.filter(
            user=self.user1,
        ).update(
            university='University A',
            college_major='Engineering',
            interests='Coding, Robotics',
            university_tags=['public'],
            background_check_passed_at=timezone.now(),
            high_school_zip_code='12345',
        )

        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        CollegeStudentProfile.objects.filter(
            user=self.user2,
        ).update(
            university='University B',
            college_major='Arts',
            interests='Painting, Music',
            university_tags=['private'],
            background_check_passed_at=None,  # Should be excluded
            high_school_zip_code='12345',
        )

        self.user3 = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        CollegeStudentProfile.objects.filter(
            user=self.user3,
        ).update(
            university='University C',
            college_major='Science',
            interests='Biology, Chemistry',
            university_tags=['liberal arts'],
            background_check_passed_at=timezone.now(),
            high_school_zip_code='99999',
        )
        self.url = reverse('college-student-list')
        self.client.force_authenticate(user=self.user1)

    def test_keyword_search(self):
        response = self.client.get(self.url, {'search': 'Engineering'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['email'], '<EMAIL>')

    def test_filter_by_college_type(self):
        response = self.client.get(self.url, {'university_tags': 'public'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['results'][0]['email'], '<EMAIL>')

    def test_filter_by_major(self):
        response = self.client.get(self.url, {'major': 'Arts'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)  # Excluded due to background_check_passed_at=None

    def test_filter_by_interests(self):
        response = self.client.get(self.url, {'interests': 'Coding'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)

    def test_proximity_sorting(self):
        response = self.client.get(self.url, {'user_zip': '12345' })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check that the results are sorted by proximity
        self.assertEqual(len(response.data['results']), 2)
        self.assertEqual(response.data['results'][0]['email'], '<EMAIL>')
        self.assertEqual(response.data['results'][1]['email'], '<EMAIL>')
        # Uncomment the following lines because there is no distance in the response data
        # distance1 = response.data['results'][0]['distance']
        # distance2 = response.data['results'][1]['distance']
        # self.assertLess(distance1, distance2)

    def test_pagination(self):
        # Create additional users to exceed page size
        for i in range(55):
            user = User.objects.create_user(
                email=f'student{i+4}@university.edu',
                password='Password123!',
                user_type=User.COLLEGE_STUDENT_TYPE
            )
            CollegeStudentProfile.objects.filter(
                user=user,
            ).update(
                university='University D',
                college_major='Mathematics',
                interests=['Algebra', 'Calculus'],
                university_tags=['public'],
                background_check_passed_at=timezone.now(),
            )
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 50)
        self.assertIn('next', response.data)
        self.assertIsNotNone(response.data['next'])

    def test_invalid_filter_criteria(self):
        response = self.client.get(self.url, {'university_tags': 'invalid_type'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)

    def test_no_matching_students(self):
        response = self.client.get(self.url, {'major': 'NonExistentMajor'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 0)


class UserProfileUpdateTestCase(APITestCase):
    def setUp(self):
        self.user_college = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.user_high_school = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.user_counselor = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COUNSELOR_ADMINISTRATOR_TYPE
        )
        self.url = reverse('user-me')
        self.organization = Organization.objects.create(
            name='Test Organization',
            zip_code='12345',
            city='Test City',
            state='Test State',
        )
    
    def test_patch_college_student_update_allowed_fields(self):
        self.client.force_authenticate(user=self.user_college)
        data = {
            'university': 'New University'
        }
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user_college.refresh_from_db()
        self.assertEqual(self.user_college.college_student_profile.university, 'New University')
    
    def test_patch_college_student_update_read_only_field(self):
        self.client.force_authenticate(user=self.user_college)
        data = {
            'first_name': 'NewName'
        }
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('first_name', response.data)
    
    def test_put_high_school_student_update_allowed_fields(self):
        self.client.force_authenticate(user=self.user_high_school)
        data = {
            'grade_level': 11,
        }
        response = self.client.put(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user_high_school.refresh_from_db()
        self.assertEqual(self.user_high_school.high_school_student_profile.grade_level, 11)
    
    def test_put_high_school_student_update_read_only_field(self):
        self.client.force_authenticate(user=self.user_high_school)
        data = {
            'last_name': 'NewLastName'
        }
        response = self.client.put(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('last_name', response.data)
    
    def test_patch_counselor_update_allowed_fields(self):
        self.client.force_authenticate(user=self.user_counselor)
        data = {
            'position': 'Senior Counselor'
        }
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.user_counselor.refresh_from_db()
        self.assertEqual(self.user_counselor.counselor_administrator_profile.position, 'Senior Counselor')
    
    def test_patch_counselor_update_read_only_field(self):
        self.client.force_authenticate(user=self.user_counselor)
        data = {
            'organization': str(self.organization.id)
        }
        response = self.client.patch(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('organization', response.data)


class AvailabilityCurrentViewTest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=self.user)
        self.url = reverse('availability-current')

    def test_retrieve_current_availability_success(self):
        # Create multiple availability records
        Availability.objects.create(user=self.user, time_zone='UTC', updated_at=timezone.now())

        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['time_zone'], 'UTC')  # Latest availability

    def test_retrieve_current_availability_not_found(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        self.assertEqual(response.data['detail'], 'No availability found.')


class AvailabilityViewSetTest(APITestCase):
    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='password123')
        self.client.login(email='<EMAIL>', password='password123')
        self.url = reverse('availability-list')  # Ensure the correct URL name

    def test_create_availability(self):
        data = {
            'time_zone': 'EST',
            'monday_available': True,
            'tuesday_available': False,
            'wednesday_available': True,
            # ... other fields ...
            'monday_time_ranges': [
                {'start_time': '09:00', 'end_time': '12:00'},
                {'start_time': '13:00', 'end_time': '17:00'}
            ],
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Availability.objects.count(), 1)
        self.assertEqual(Availability.objects.get().user, self.user)

    def test_update_existing_availability(self):
        Availability.objects.create(user=self.user, time_zone='EST')
        data = {
            'time_zone': 'PST',
            # ... other fields ...
        }
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertEqual(Availability.objects.count(), 1)
        self.assertEqual(Availability.objects.get().time_zone, 'PST')

    def test_retrieve_availability(self):
        Availability.objects.create(user=self.user, time_zone='EST')
        url = reverse('availability-detail', args=[self.user.availability.id])
        response = self.client.get(url, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['time_zone'], 'EST')

    def test_delete_availability(self):
        availability = Availability.objects.create(user=self.user, time_zone='EST')
        url = reverse('availability-detail', args=[availability.id])
        response = self.client.delete(url, format='json')
        self.assertEqual(response.status_code, status.HTTP_204_NO_CONTENT)
        self.assertEqual(Availability.objects.count(), 0)


class CollegeStudentViewSetTests(APITestCase):

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='password123')
        self.client.login(email='<EMAIL>', password='password123')
        self.url = reverse('college-student-list')

        # Create users with different availability
        # self.user won't be counted since by default background_check_passed_at is False
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        CollegeStudentProfile.objects.filter(
            user=self.user1,
        ).update(
            university='University D',
            college_major='Computer Science',
            interests=['AI', 'Machine Learning'],
            university_tags=['public'],
            background_check_passed_at=timezone.now(),
        )
        Availability.objects.create(user=self.user1, monday_available=True, tuesday_available=False)
        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            password='Password321!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        CollegeStudentProfile.objects.filter(
            user=self.user2,
        ).update(
            university='University D',
            college_major='Mathematics',
            interests=['Algebra', 'Calculus'],
            university_tags=['public'],
            background_check_passed_at=timezone.now(),
        )
        Availability.objects.create(user=self.user2, monday_available=False, tuesday_available=True)

    def test_filter_by_monday(self):
        response = self.client.get(self.url, {'day': 'monday'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['id'], str(self.user1.id))

    def test_filter_by_tuesday(self):
        response = self.client.get(self.url, {'day': 'tuesday'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['id'], str(self.user2.id))

    def test_no_day_parameter(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 2)

    def test_multiple_filters(self):
        response = self.client.get(self.url, {'day': 'monday', 'major': 'Computer Science'})
        self.assertEqual(response.status_code, 200)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['id'], str(self.user1.id))

    def test_invalid_day_parameter(self):
        response = self.client.get(self.url, {'day': 'funday'})
        self.assertEqual(response.status_code, 400)
        self.assertIn('day', str(response.data[0]))
        self.assertEqual(str(response.data[0]), "Invalid day(s): funday. Valid options are: sunday, monday, tuesday, wednesday, thursday, friday, saturday.")

    def test_filter_with_existing_filters(self):
        response = self.client.get(self.url, {'day': 'monday', 'major': 'Computer Science', 'interests': 'AI'})
        self.assertEqual(response.status_code, 200)
        # Adjust the expected number based on the setup
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['id'], str(self.user1.id))

    def test_filter_by_multiple_days(self):
        response = self.client.get(self.url, {'day': 'monday,tuesday'})
        self.assertEqual(response.status_code, 200)
        # Expect both users to be returned, as one is available on Monday and the other on Tuesday
        self.assertEqual(response.data['count'], 2)
        result_ids = {result['id'] for result in response.data['results']}
        self.assertIn(str(self.user1.id), result_ids)
        self.assertIn(str(self.user2.id), result_ids)

class CollegeStudentViewSetTestCase(TestCase):
    def setUp(self):
        self.viewset = CollegeStudentViewSet()

    @requests_mock.Mocker()
    def test_get_location_from_zip(self, mock_request):
        mock_response = {
            "type": "FeatureCollection",
            "features": [
                {
                    "type": "Feature",
                    "geometry": {
                        "type": "Point",
                        "coordinates": [-73.226356, 41.157473]
                    },
                    "properties": {}
                }
            ]
        }
        mock_request.get(
            f"https://api.mapbox.com/search/geocode/v6/forward?postcode=06605&country=united&access_token={settings.MAPBOX_API_KEY}",
            json=mock_response
        )

        # Call the function with the test zip code
        location = self.viewset.get_location_from_zip("06605")

        # Assert that the function returns the correct coordinates
        self.assertEqual(location, (41.157473, -73.226356))


class CollegeStudentDetailTestCase(APITestCase):

    def setUp(self):
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        CollegeStudentProfile.objects.filter(
            user=self.user,
        ).update(
            university='University A',
            college_major='Engineering',
            interests='Coding, Robotics',
            university_tags=['public'],
            background_check_passed_at=timezone.now(),
            high_school_zip_code='12345',
        )
        self.url = reverse('college-student-detail', kwargs={'pk': self.user.pk})
        self.client.force_authenticate(user=self.user)

    def test_retrieve_single_college_student(self):
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['email'], '<EMAIL>')
        self.assertEqual(response.data['user_type'], 'CollegeStudent')
        self.assertEqual(response.data['profile']['university'], 'University A')
        self.assertEqual(response.data['profile']['college_major'], 'Engineering')


class LoginViewTests(APITestCase):

    def setUp(self):
        cache.clear()
        self.user = User.objects.create_user(email='<EMAIL>', password='password123')
        self.login_url = reverse('login')

    def tearDown(self):
        cache.clear() 

    def test_successful_login(self):
        response = self.client.post(self.login_url, {'email': '<EMAIL>', 'password': 'password123'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('token', response.data)
    
    def test_invalid_password(self):
        response = self.client.post(self.login_url, {'email': '<EMAIL>', 'password': '000000'})
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        self.assertEqual(str(response.data['error']), 'Invalid email or password.')


class MajorListViewTests(APITestCase):
    def setUp(self):
        # Create test users and profiles
        self.user1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.profile1 = CollegeStudentProfile.objects.get(user=self.user1)
        self.profile1.college_major = "Computer Science"
        self.profile1.background_check_passed_at = timezone.now()
        self.profile1.save()

        self.user2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.profile2 = CollegeStudentProfile.objects.get(user=self.user2)
        self.profile2.college_major = "Biology"
        self.profile2.background_check_passed_at = timezone.now()
        self.profile2.save()

        self.user3 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.profile3 = CollegeStudentProfile.objects.get(user=self.user3)
        self.profile3.college_major = "Art History"
        self.profile3.save()  # No background check passed

    def test_get_majors_list(self):
        url = reverse('major-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 2)  # Only profiles with passed background check
        self.assertEqual(response.data, ["Biology", "Computer Science"])  # Alphabetically ordered

    def test_get_majors_list_empty(self):
        # Clear all profiles
        CollegeStudentProfile.objects.all().delete()
        
        url = reverse('major-list')
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data, [])


class RecommendationsStatusViewTests(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.url = reverse('recommendations-status')
        
        # Create a high school student user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.profile = self.user.high_school_student_profile
        
        # Create a non-high school student user
        self.other_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        
    def test_unauthenticated_access(self):
        """Test that unauthenticated users cannot access the endpoint."""
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
        
    def test_authenticated_access_with_profile(self):
        """Test that authenticated users with a profile can access the endpoint."""
        self.client.force_authenticate(user=self.user)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['recommendations_status'], 'not_requested')
        
    def test_authenticated_access_without_profile(self):
        """Test that users without a high school profile get a 404."""
        self.client.force_authenticate(user=self.other_user)
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_404_NOT_FOUND)
        
    def test_status_updates_correctly(self):
        """Test that the status is returned correctly after updates."""
        self.client.force_authenticate(user=self.user)
        
        # Check initial status
        response = self.client.get(self.url)
        self.assertEqual(response.data['recommendations_status'], 'not_requested')
        
        # Update status and check again
        self.profile.recommendations_status = 'pending'
        self.profile.save()
        response = self.client.get(self.url)
        self.assertEqual(response.data['recommendations_status'], 'pending')
        
        # Update to final status and check again
        self.profile.recommendations_status = 'ready'
        self.profile.save()
        response = self.client.get(self.url)
        self.assertEqual(response.data['recommendations_status'], 'ready')


class ProfileUpdateRecommendationRegenerationTests(APITestCase):
    """
    Test cases for the profile update and recommendation regeneration API endpoint.
    """
    
    def setUp(self):
        """Set up test data."""
        # Create a high school student user
        self.organization = OrganizationFactory()
        self.user = UserFactory(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
        self.profile = self.user.high_school_student_profile
        self.profile.organization = self.organization
        self.profile.save()
        
        # Create a college student user (for permission tests)
        self.college_student = UserFactory(user_type=User.COLLEGE_STUDENT_TYPE)
        
        # URL for the API endpoint
        self.url = reverse('profile-update-recommendation-regeneration')
        
        # Valid data for testing
        self.valid_data = {
            'unweighted_gpa': 3.8,
            'weighted_gpa': 4.2,
            'sat_math_score': 750,
            'sat_reading_score': 720,
            'act_score': 32,
            'high_school_name': 'Lincoln High School',
            'graduation_year': 2025,
            'intended_majors': ['Computer Science', 'Mathematics'],
            'extracurriculars': "Debate and Chess Club",
            'interests': "Programming and reading",
            'college_type_preferences': ['Public', 'Private'],
            'location_type_preferences': ['Urban', 'Suburban'],
            'geographic_preference_type': 'states',
            'preferred_states': ['CA', 'NY', 'MA'],
            'preferred_radius': '0-20',
            'gender': 'female',
            'ethnicity': 'black',
            'household_income': '48k_75k',
            'financial_aid_need': 'medium'
        }
        
        # Log in as the high school student
        self.client.force_authenticate(user=self.user)
    
    @patch('api.users.views.generate_recommendations_in_background')
    def test_update_profile_success(self, mock_generate):
        """Test successfully updating a high school student profile and triggering recommendation regeneration."""
        response = self.client.post(self.url, self.valid_data, format='json')
        
        # Check response status and data
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unweighted_gpa'], 3.8)
        self.assertEqual(response.data['weighted_gpa'], 4.2)
        self.assertEqual(response.data['recommendations_status'], 'pending')
        
        # Verify the data was saved to the database
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.unweighted_gpa, 3.8)
        self.assertEqual(self.profile.act_score, 32)
        self.assertEqual(self.profile.intended_majors, ['Computer Science', 'Mathematics'])
        self.assertEqual(self.profile.recommendations_status, 'pending')
        
        # Verify the recommendation regeneration function was called
        mock_generate.assert_called_once_with(self.profile)
    
    def test_update_profile_invalid_data(self):
        """Test validation for invalid update data."""
        data = self.valid_data.copy()
        data['unweighted_gpa'] = 4.5  # Invalid GPA
        
        response = self.client.post(self.url, data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('unweighted_gpa', response.data)
        
        # Verify the profile data was not updated
        self.profile.refresh_from_db()
        self.assertNotEqual(self.profile.unweighted_gpa, 4.5)
    
    def test_update_profile_unauthenticated(self):
        """Test that unauthenticated users cannot access the endpoint."""
        # Log out
        self.client.force_authenticate(user=None)
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_update_profile_wrong_user_type(self):
        """Test that only high school students can access the endpoint."""
        # Log in as a college student
        self.client.force_authenticate(user=self.college_student)
        
        response = self.client.post(self.url, self.valid_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)
    
    @patch('api.users.views.generate_recommendations_in_background')
    def test_rate_limiting(self, mock_generate):
        """Test that the endpoint enforces the rate limit."""
        # Make 3 requests (should succeed)
        for _ in range(3):
            response = self.client.post(self.url, self.valid_data, format='json')
            self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Make a 4th request (should be rate limited)
        response = self.client.post(self.url, self.valid_data, format='json')
        self.assertEqual(response.status_code, status.HTTP_429_TOO_MANY_REQUESTS)
        
        # Verify the recommendation generation function was called exactly 3 times
        self.assertEqual(mock_generate.call_count, 3)
    
    @patch('api.users.views.generate_recommendations_in_background')
    def test_partial_update(self, mock_generate):
        """Test that partial updates are supported."""
        # Update only GPA
        partial_data = {'unweighted_gpa': 3.9}
        
        response = self.client.post(self.url, partial_data, format='json')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['unweighted_gpa'], 3.9)
        
        # Verify only the specified field was updated
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.unweighted_gpa, 3.9)
        
        # Verify the recommendation regeneration function was called
        mock_generate.assert_called_once_with(self.profile)
