from rest_framework import permissions


class IsOwnerOrAssociated(permissions.BasePermission):
    """
    Custom permission to only allow users to book sessions for themselves or their associated profiles.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated

    def has_object_permission(self, request, view, obj):
        return obj.booked_by == request.user or obj.booked_by.profile == request.user.profile


class IsTrailblazer(permissions.BasePermission):
    """
    Custom permission to only allow users that has a college student profile.
    """

    def has_permission(self, request, view):
        return request.user and request.user.is_authenticated and request.user.user_type == 'CollegeStudent'
    
    def has_object_permission(self, request, view, obj):
        return request.user and request.user.is_authenticated and request.user.user_type == 'CollegeStudent'


class IsAssociatedTrailblazer(permissions.BasePermission):
    """
    Custom permission to check if the user is a trailblazer associated with the booking.
    """
    def has_object_permission(self, request, view, obj):
        # Ensure the user is authenticated and is a trailblazer
        if not request.user.is_authenticated or not request.user.user_type == 'CollegeStudent':
            return False
        # Check if the user is associated with the booking via trailblazer_statuses
        return obj.trailblazer_statuses.filter(trailblazer=request.user).exists()