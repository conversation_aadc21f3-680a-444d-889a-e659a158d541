"use client"

import { useState } from 'react'
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Check, ChevronsUpDown } from 'lucide-react'
import { cn } from "@/lib/utils"
import {
    Command,
    CommandEmpty,
    CommandGroup,
    CommandList,
    CommandInput,
    CommandItem,
} from "@/components/ui/command"
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover"
import { rankSearchAlphabetically } from "@/lib/utils"


// OrganizationCombobox component
export const OrganizationCombobox = ({ field, form, organizations, loading }) => {
    const [open, setOpen] = useState(false)

    return (
        <Popover open={open} onOpenChange={setOpen} className="w-full">
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    disabled={loading}
                    className="w-full justify-between bg-white text-left py-6 px-4 text-wrap"
                >
                    {field.value
                        ? organizations.find((org) => org.id === field.value)?.name
                        : "Select your organization..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent className="w-full p-0">
                <Command
                    // Workaround until cmdk library fixes the problem of sorting after deleting keys: https://github.com/pacocoursey/cmdk/issues/264
                    filter={rankSearchAlphabetically}
                >
                    <CommandInput placeholder="Search organization..." />
                    <CommandList>
                        <CommandEmpty>No organization found.</CommandEmpty>
                        <CommandGroup>
                            {organizations.map((organization) => (
                                <CommandItem
                                    key={organization.id}
                                    onSelect={() => {
                                        field.onChange(organization.id)
                                        setOpen(false)
                                        form.trigger("organization")
                                    }}
                                >
                                    <Check
                                        className={cn(
                                            "mr-2 h-4 w-4 shrink-0",
                                            field.value === organization.id ? "opacity-100" : "opacity-0"
                                        )}
                                    />
                                    {organization.name}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}

