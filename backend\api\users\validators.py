from django.core.exceptions import ValidationError
import re

class UppercaseValidator:
    """
    Validates that the password contains at least one uppercase letter.
    """
    def validate(self, password, user=None):
        if not re.search(r'[A-Z]', password):
            raise ValidationError(
                "Password must contain at least one uppercase letter.",
                code='password_no_uppercase',
            )

    def get_help_text(self):
        return "Your password must contain at least one uppercase letter."

class LowercaseValidator:
    """
    Validates that the password contains at least one lowercase letter.
    """
    def validate(self, password, user=None):
        if not re.search(r'[a-z]', password):
            raise ValidationError(
                "Password must contain at least one lowercase letter.",
                code='password_no_lowercase',
            )

    def get_help_text(self):
        return "Your password must contain at least one lowercase letter."

class DigitValidator:
    """
    Validates that the password contains at least one digit.
    """
    def validate(self, password, user=None):
        if not re.search(r'\d', password):
            raise ValidationError(
                "Password must contain at least one digit.",
                code='password_no_digit',
            )

    def get_help_text(self):
        return "Your password must contain at least one digit."

class SpecialCharacterValidator:
    """
    Validates that the password contains at least one special character.
    """
    def validate(self, password, user=None):
        if not re.search(r'[!@#$%^&*(),.?":{}|<>]', password):
            raise ValidationError(
                "Password must contain at least one special character.",
                code='password_no_special',
            )

    def get_help_text(self):
        return "Your password must contain at least one special character."