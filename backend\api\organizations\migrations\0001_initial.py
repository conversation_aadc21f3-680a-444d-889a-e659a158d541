# Generated by Django 4.2.13 on 2024-10-07 19:02

from django.conf import settings
from django.db import migrations, models
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='Organization',
            fields=[
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('name', models.Char<PERSON>ield(max_length=255)),
                ('zip_code', models.Char<PERSON>ield(max_length=10)),
                ('city', models.CharField(max_length=100)),
                ('state', models.CharField(max_length=100)),
                ('school_district', models.CharField(blank=True, max_length=255, null=True)),
                ('contact_phone', models.Char<PERSON>ield(blank=True, max_length=20, null=True)),
                ('contact_email', models.EmailField(blank=True, max_length=254, null=True)),
                ('registration_number', models.Char<PERSON><PERSON>(blank=True, max_length=100, null=True, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('administrators', models.ManyToManyField(related_name='organizations_managed', to=settings.AUTH_USER_MODEL)),
            ],
        ),
    ]
