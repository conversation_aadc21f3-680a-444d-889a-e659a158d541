from django.contrib import admin
from .models import Configuration

@admin.register(Configuration)
class ConfigurationAdmin(admin.ModelAdmin):
    list_display = ('use_waitlist',)

    def has_add_permission(self, request):
        # Prevent adding new instances
        return False

    def has_delete_permission(self, request, obj=None):
        # Prevent deletion of the existing instance
        return False