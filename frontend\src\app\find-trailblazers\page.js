"use client"

import { useState, useEffect, use<PERSON>allback, useMemo, useRef } from 'react'
import { useRouter } from 'next/navigation'
import { MainLayout } from "@/components/ui/mainLayout"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Card, CardContent } from "@/components/ui/card"
import {
    Tooltip,
    TooltipContent,
    TooltipProvider,
    TooltipTrigger,
  } from "@/components/ui/tooltip"
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from "@/components/ui/dropdown-menu"
import { Checkbox } from "@/components/ui/checkbox"
import { CalendarIcon, Search, ChevronDown } from 'lucide-react'
import debounce from 'lodash.debounce'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { universityTags } from "@/components/ui/universityPicker"
import { interestTags } from "@/components/ui/interestPicker"
import { majors } from "@/components/ui/majorSelect"
import { Loader2 } from 'lucide-react'
import { UserType } from "@/lib/utils"
import OutreachModal from '@/components/ui/outReach'
import { use } from 'react'


// Enhanced search bar component with debounce
const SearchBar = ({ onSearch }) => {
    const [query, setQuery] = useState('')

    const debouncedSearch = useCallback(
        debounce((value) => onSearch(value), 500),
        [onSearch]
    )

    const handleInputChange = (e) => {
        const value = e.target.value
        setQuery(value)
        debouncedSearch(value)
    }

    return (
        <div className="relative ml-auto flex-1 md:grow-0">
            <Search className="absolute left-4 top-3.5 h-5 w-5 text-muted-foreground" />
            <Input
                className="w-full px-3 border border-gray-300 rounded-md pl-11"
                placeholder="Search by college, major, interest and more"
                value={query}
                onChange={handleInputChange}
            />
        </div>
    )
}

// Enhanced filter button component
const FilterButton = ({ label, options, selectedOptions, filterType, onFilterChange }) => {

    const onSelect = (e, option) => {
        e.preventDefault()
        onFilterChange(filterType, option)
    }

    return (
        <DropdownMenu>
            <DropdownMenuTrigger asChild>
                <Button variant="outline" className="text-left pl-4 pr-3 min-w-42 border border-gray-400 flex justify-between items-center bg-transparent">
                    {label} {selectedOptions?.length > 0 && `(${selectedOptions.length})`}
                    <ChevronDown className="ml-2 h-4 w-4" />
                </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="max-h-[50vh] overflow-y-auto">
                {options.map((option) => (
                    <DropdownMenuItem key={option.value} onSelect={(e) => onSelect(e, option)}>
                        <Checkbox 
                            checked={selectedOptions?.some(selected => selected.value === option.value)} 
                            className="mr-2" 
                        />
                        {option.label ? option.label : option.value}
                    </DropdownMenuItem>
                ))}
            </DropdownMenuContent>
        </DropdownMenu>
    )
}

// Find Time button component
const FindTimeButton = ({ onClick, isGroupSession = false, disabled, loading, selectedTrailblazersCount }) => {

    const groupSessionButton = (
        <div className="flex flex-col items-center justify-center">
            <Button
                variant="default"
                disabled={disabled}
                className="px-5"
                onClick={onClick}
                >
                {loading && <Loader2 className="mr-2 w-5 h-5 animate-spin" />}
                Schedule Time { selectedTrailblazersCount > 0 && `(${selectedTrailblazersCount}/3)`}
                <CalendarIcon className="ml-2 h-5 w-5" />
            </Button>
        </div>
    )

    const individualSessionButton = (
        <Button
            variant="outline"
            disabled={disabled}
            className="border-green-500 text-green-500 px-5"
            onClick={onClick}
        >
            {loading && <Loader2 className="mr-2 w-5 h-5 animate-spin" />}
            Find Time
            <CalendarIcon className="ml-2 h-5 w-5" />
        </Button>
    )

    if (isGroupSession) {
        if (disabled) {
            return (
                <TooltipProvider>
                    <Tooltip>
                        <TooltipTrigger asChild>
                            <div>{groupSessionButton}</div>
                        </TooltipTrigger>
                        <TooltipContent>
                            <p>
                                Select at least two Trailblazers to find a common time slot for a group session
                            </p>
                        </TooltipContent>
                    </Tooltip>
                </TooltipProvider>
            )
        }
        return groupSessionButton
    }
    if (disabled) {
        return (
            <TooltipProvider>
                <Tooltip>
                    <TooltipTrigger asChild>
                        <div>{individualSessionButton}</div>
                    </TooltipTrigger>
                    <TooltipContent>
                        <p>
                            Cannot find time for individual selections when multiple Trailblazers are selected
                        </p>
                    </TooltipContent>
                </Tooltip>
            </TooltipProvider>
        )
    }
    return individualSessionButton
}

// Enhanced Trailblazer card component
const TrailblazerCard = ({ trailblazer, isSelected, onSelect, onFindTime, loading, canSelectMultiple, checkboxDisabled, isVerified }) => {
    const avatarUrl = trailblazer.profile.avatar 
    ? (trailblazer.profile.avatar.startsWith('https') 
        ? trailblazer.profile.avatar 
        : `${process.env.NEXT_PUBLIC_API_BASE_URL}${trailblazer.profile.avatar}`) 
    : null;
    return (
        <Card className={`bg-white p-4 rounded-lg border-gray-300 mr-2 ${isSelected ? 'border-2 border-green-500' : ''}`}>
            <CardContent className="p-0 pb-8 space-y-4 h-full flex flex-col">
                <div className="flex items-center justify-center space-x-4 relative">
                    <Avatar className="h-16 w-16 mt-8">
                        <AvatarImage src={avatarUrl} alt={trailblazer.name} />
                        <AvatarFallback>{trailblazer.name.charAt(0)}</AvatarFallback>
                    </Avatar>
                    {canSelectMultiple && (
                            <TooltipProvider>
                                <Tooltip>
                                    <TooltipTrigger>
                                        <Checkbox 
                                            className="absolute top-1 right-1 rounded-full w-5 h-5"
                                            checked={isSelected}
                                            onCheckedChange={() => onSelect(trailblazer.id)}
                                            disabled={checkboxDisabled}
                                        />
                                    </TooltipTrigger>
                                    {checkboxDisabled && (
                                        <TooltipContent>
                                            <p>
                                            You can only select up to 3 Trailblazers. Please deselect one to choose another.
                                            </p>
                                        </TooltipContent>
                                    )}
                                </Tooltip>
                            </TooltipProvider>
                    )}
                </div>
                <div className="space-y-0.5 pb-3 flex-grow text-center">
                    <h3 className={`font-bold ${!isVerified? 'blur-sm select-none' : ''}`}>{trailblazer.name}</h3>
                    <p className="">{trailblazer.profile?.university}</p>
                    <p className="">{trailblazer.profile?.college_major}</p>
                    <p className="">{trailblazer.location}</p>
                </div>
                <div className="mt-auto mx-auto">
                    <FindTimeButton 
                        onClick={() => onFindTime(trailblazer.id)}
                        disabled={isSelected || loading || !isVerified}
                        loading={loading}
                    />
                </div>
            </CardContent>
        </Card>
    );
};

const FilterTags = ({ filters, handleFilterChange }) => {
    return (
        <div className="flex flex-wrap gap-2">
            {Object.entries(filters).map(([filterType, selectedOptions]) => 
                selectedOptions.map(option => (
                    <span key={`${filterType}-${option.value}`} className="flex items-center bg-gray-200 px-3 py-1 rounded-full text-sm">
                        {option.label ? option.label : option.value}
                        <button onClick={() => handleFilterChange(filterType, option)} className="ml-2">&times;</button>
                    </span>
                ))
            )}
        </div>
    )
}

// Main content component with enhanced functionality
const MainContent = ({ user, userLoading, userError }) => {
    const router = useRouter()

    const userZip = user?.profile?.organization?.zip_code || ''

    const [loading, setLoading] = useState(false)
    const [error, setError] = useState(null)
    const [page, setPage] = useState(1)
    const [hasMore, setHasMore] = useState(true)
    
    const [trailblazers, setTrailblazers] = useState([])
    const [filteredTrailblazers, setFilteredTrailblazers] = useState(trailblazers)
    const [searchQuery, setSearchQuery] = useState('')
    const [filters, setFilters] = useState({
        collegeType: [],
        interests: [],
        major: [],
        availability: [],
        graduationYear: [],
        organizationTag: [],
    })
    const [selectedTrailblazers, setSelectedTrailblazers] = useState([])
    const [loadingCardId, setLoadingCardId] = useState(null)
    const [loadingGroup, setloadingGroup] = useState(false)
    const { getToken } = useAuth()

    const isHighSchoolUser = useMemo(() => user.user_type === UserType.HIGH_SCHOOL_STUDENT_TYPE, [user])
    const isVerifiedCounselor = useMemo(() => !isHighSchoolUser && user.profile.verified_at !=null, [user, isHighSchoolUser])

    // Fetch majors
    const [availableMajors, setAvailableMajors] = useState([])
    const [isMajorsLoading, setIsMajorsLoading] = useState(false)
    const [majorsError, setMajorsError] = useState(null)
    const [isModalOpen, setIsModalOpen] = useState(false);
    const skipPageEffect = useRef(false); // Ref to control page reset

    // Fetch org tags
    const [organizationTags, setOrganizationTags] = useState([]);
    const [isOrgTagsLoading, setIsOrgTagsLoading] = useState(false);

    useEffect(() => {
      const fetchMajors = async () => {
        setIsMajorsLoading(true)
        try {
          const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/majors/`)
          if (!response.ok) {
            throw new Error('Failed to fetch majors')
          }
          const data = await response.json()
          if (data.length > 0) {
            setAvailableMajors(data.map(major => ({ value: major })))
          } else {
            setAvailableMajors([]) // Fallback to default
          }
        } catch (error) {
          setAvailableMajors([]) // Fallback to default
        } finally {
          setIsMajorsLoading(false)
        }
      }
      fetchMajors()
    }, [])


    useEffect(() => {
        const fetchOrganizationTags = async () => {
            setIsOrgTagsLoading(true);
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/tags/list`);
                if (!response.ok) {
                    throw new Error('Failed to fetch organization tags');
                }
                const data = await response.json();

                // Access the `tags` property before mapping
                if (Array.isArray(data.tags)) {
                    setOrganizationTags(data.tags.map(tag => ({ value: tag.id, label: tag.name })));

                } else {
                    setOrganizationTags([]);
                }
            } catch (error) {
                setOrganizationTags([]);
            } finally {
                setIsOrgTagsLoading(false);
            }
        };

        fetchOrganizationTags();
    }, []);


    const filterOptions = useMemo(() => {
        return {
            collegeType: {
                label: "Type of College",
                options: universityTags,
            },
            interests: {
                label: "Professional Interests",
                options: interestTags,
            },
            major: {
                label: "College Major",
                options: availableMajors && availableMajors.length > 0 ? availableMajors : majors
            },
            graduationYear: { 
                label: "Grad Year",
                options: Array.from({ length: 6 }, (_, i) => {
                    const year = new Date().getFullYear() + i;
                    return { value: year.toString(), label: year.toString() };
                })
            },
            organizationTag: {
                label: "Organizations",
                options: organizationTags && organizationTags.length > 0 ? organizationTags : [],
            },
            availability: {
                label: "Availability",
                options: [
                    { value: "monday", label: "Monday" },
                    { value: "tuesday", label: "Tuesday" },
                    { value: "wednesday", label: "Wednesday" },
                    { value: "thursday", label: "Thursday" },
                    { value: "friday", label: "Friday" },
                    { value: "saturday", label: "Saturday" },
                    { value: "sunday", label: "Sunday" },
                ]
            }, 
        }
    }, [availableMajors, organizationTags])

    // Handle search
    const handleSearch = (query) => {
        setSearchQuery(query)
    }

    // Handle filter changes
    const handleFilterChange = (filterType, option) => {
        setFilters(prevFilters => ({
            ...prevFilters,
            [filterType]: prevFilters[filterType].some(item => item.value === option.value)
                ? prevFilters[filterType].filter(item => item.value !== option.value)
                : [...prevFilters[filterType], option]
        }));
    };

    // Handle Trailblazer selection
    const handleTrailblazerSelect = (id) => {
        setSelectedTrailblazers(prevSelected =>
            prevSelected.includes(id)
                ? prevSelected.filter(selectedId => selectedId !== id)
                : [...prevSelected, id]
        )
    }

    // Handle Find Time button click
    const handleFindTime = (id) => {
        window.open(`/trailblazer/${id}`, '_blank');
    }

    // Handle Find Time for group session
    const handleGroupFindTime = () => {
        window.open(`/trailblazer?ids=${selectedTrailblazers.join(',')}`, '_blank');
    }

    const requestTrailblazers = async (params) => {
        return await fetch(
            `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/college-students/?${params.toString()}`, {
                method: 'GET',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${getToken()}`,
                },
            }
        )
    }

    useEffect(() => {
        if (userLoading || userError) return;
    
        const fetchTrailblazers = async (page = 1) => {
            setLoading(true);
            setError(null);
            try {
                const params = new URLSearchParams({
                    search: searchQuery,
                    university_tags: filters.collegeType.map(college => college.value).join(','),
                    major: filters.major.map(major => major.value).join(','),
                    interests: filters.interests.map(interest => interest.value).join(','),
                    user_zip: userZip,
                    day: filters.availability.map(availability => availability.value).join(','),
                    graduation_year: filters.graduationYear.map(graduationYear => graduationYear.value).join(','),
                    organization_tags: filters.organizationTag.map(organizationTag => organizationTag.value).join(','),
                    page: page,
                
                });

                
                const response = await requestTrailblazers(params);
                if (!response.ok) {
                    throw new Error('Failed to fetch Trailblazers');
                }
                const data = await response.json();
                setFilteredTrailblazers(prev => {
                    const newTrailblazers = page === 1 ? data.results : [...prev, ...data.results];
                    return newTrailblazers.map(trailblazer => {
                        // Add a new property to store the full name
                        trailblazer.name = `${trailblazer.first_name} ${trailblazer.last_name}`;
                        // Location text
                        trailblazer.location = `${trailblazer.profile.high_school_city}, ${trailblazer.profile.high_school_state}`;
                        return trailblazer;
                    });
                });
                setHasMore(data.next !== null);
            } catch (err) {
                setError(err.message);
            } finally {
                setLoading(false);
            }
        };
    
          if (!skipPageEffect.current) {
            fetchTrailblazers(page);
        } else {
            skipPageEffect.current = false; // Reset the flag after handling
        }
    }, [searchQuery, filters, userZip, page, userLoading, userError]);

    useEffect(() => {
        if (!hasMore) {
            skipPageEffect.current = true;
            setPage(1);
        }
    }, [hasMore]);
   

    // Handle modal open

    useEffect(() => {
        if (!isHighSchoolUser && !isVerifiedCounselor && !isModalOpen) {
            setIsModalOpen(true)
        }
    }, [isVerifiedCounselor, isHighSchoolUser])

    return (
        <div className="space-y-8 w-full">
            {isModalOpen && <OutreachModal onClose={() => setIsModalOpen(false)} user={user}/>}
            <h1 className="text-2xl font-semibold text-gray-800">Connect with Trailblazers from your community</h1>
            <div className="space-y-4 w-full">
                <SearchBar onSearch={handleSearch} />
                <div>
                    <FilterTags filters={filters} handleFilterChange={handleFilterChange} />
                    <div className="flex flex-wrap gap-2 justify-between py-4">
                        <div className="flex flex-wrap gap-2">
                            {Object.entries(filterOptions).map(([filterType, filterDetail]) => (
                                <FilterButton
                                    key={filterType}
                                    label={filterDetail.label}
                                    options={filterDetail.options}
                                    selectedOptions={filters[filterType]}
                                    onFilterChange={(_, option) => handleFilterChange(filterType, option)}
                                />
                            ))}
                        </div>
                        {!isHighSchoolUser && (
                            <div>
                                <FindTimeButton
                                    onClick={handleGroupFindTime}
                                    isGroupSession={true}
                                    disabled={selectedTrailblazers.length < 2 || loadingGroup || !isVerifiedCounselor}
                                    loading={loadingGroup}
                                    selectedTrailblazersCount={selectedTrailblazers.length}
                                />
                            </div>
                        )}
                    </div>
                </div>
            </div>
            {error && <p className="text-red-500 text-center mt-4">{error}</p>}
            {
                userLoading || loading ? (
                    <div className="grid gap-4 gap-y-6 grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                        {[...Array(10)].map((_, index) => (
                            <div key={index} className="skeleton h-48 bg-gray-200 rounded-lg w-full mr-2 h-[350px]" />
                        ))}
                    </div>
                ) : (
                    <>
                        {filteredTrailblazers.length === 0 ? (
                            <p className="text-center text-gray-500 mt-8">No Trailblazers found matching your criteria.</p>
                        ) : (
                            <div className="grid gap-4 gap-y-6 grid-cols-1 sm:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5">
                                {filteredTrailblazers.map((trailblazer) => (
                                    <TrailblazerCard
                                        key={trailblazer.id}
                                        trailblazer={trailblazer}
                                        isSelected={selectedTrailblazers.includes(trailblazer.id)}
                                        onSelect={handleTrailblazerSelect}
                                        onFindTime={handleFindTime}
                                        isMultipleSelected={selectedTrailblazers.length > 1}
                                        loading={loadingCardId === trailblazer.id}
                                        canSelectMultiple={!isHighSchoolUser}
                                        checkboxDisabled={selectedTrailblazers.length >= 3 && !selectedTrailblazers.includes(trailblazer.id)}
                                        isVerified={isVerifiedCounselor || isHighSchoolUser}
                                    />
                                ))}
                            </div>
                        )}
                        {hasMore && (
                            <div className="flex justify-center mt-8">
                                <Button onClick={() => setPage(prev => prev + 1)}>
                                    Load More
                                </Button>
                            </div>
                        )}
                    </>
                )
            }

        </div>
    )
}

// Page component
const FindTrailblazersPage = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()

    return (
        <MainLayout user={user} displaySidebarMenu={true}>
            <MainContent user={user} userLoading={userLoading} userError={userError} />
        </MainLayout>
    )
}

export default withAuth(FindTrailblazersPage)
