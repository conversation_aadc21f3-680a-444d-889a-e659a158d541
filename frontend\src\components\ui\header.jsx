import Image from "next/image";
import { Menu } from "lucide-react";
import { Button } from "@/components/ui/button"
import { useRouter } from 'next/navigation'
  

// Header component
export const Header = ({ displaySidebarMenu, isMobileSidebarOpen, setMobileSidebarOpen }) => {
    const router = useRouter()
    return (
        <header className="relative w-full p-4 md:p-6 lg:p-8 bg-white border-b border-[#ECECEC] sticky top-0 z-50 flex items-center justify-center md:justify-between">
            {displaySidebarMenu && (
                <Button
                    variant="ghost"
                    onClick={() => setMobileSidebarOpen(!isMobileSidebarOpen)}
                    className="absolute px-2 left-4 md:hidden rounded-md"
                    aria-label="Toggle Sidebar"
                >
                <Menu className="h-6 w-6" />
            </Button> 
            )}

            <div
                onClick={() => router.push("/")}
                className="cursor-pointer"
            >
                <Image
                    src="/logo.svg"
                    alt="Trailblazer logo"
                    className="h-6 w-auto lg:h-8"
                    width={100}
                    height={24}
                    id="logo"
                    unoptimized
                />
            </div>
        </header>
    );
};
