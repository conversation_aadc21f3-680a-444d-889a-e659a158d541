import { Avatar, AvatarImage, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { cn, UserType } from "@/lib/utils"
import { Search, AlarmClock, Calendar, LogOut, CircleDollarSign } from "lucide-react"
import { useRouter } from "next/navigation"
import { usePathname } from 'next/navigation'
import { Skeleton } from "@/components/ui/skeleton"
import { useAuth } from '@/context/AuthProvider';


const getSubtitle = (user) => {
    if (user.user_type === UserType.COLLEGE_STUDENT_TYPE) {
        return user.profile.university
    } else if (user.user_type === UserType.HIGH_SCHOOL_STUDENT_TYPE) {
        return user.profile.organization?.name
    } else {
        return user.profile.organization?.name
    }
}

// Sidebar component
export const Sidebar = ({ user }) => {
    const router = useRouter()
    const pathname = usePathname()
    const { logout } = useAuth()

    const handleAvatarClick = () => {
        router.push("/profile/edit")
    }

    const handleLogout = () => {
        logout()
    }

    if (!user) {
        return (
            <div className="w-80 bg-white border-r border-gray-200 h-dvh p-4 fixed top-0 left-0 pt-10">
                <div className="flex items-center mb-8 mt-0 md:mt-20">
                    <Avatar className="mr-4 w-20 h-20 bg-gray-200" />
                    <div className="w-full space-y-2">
                        <Skeleton className="w-30 h-4 rounded-full bg-gray-200" />
                        <Skeleton className="w-20 h-4 rounded-full bg-gray-200" />
                        <Skeleton className="w-20 h-4 rounded-full bg-gray-200" />
                    </div>
                </div>
            </div>
        )
    }
    
    // Avatar URL
    const avatarUrl = user.profile.avatar 
        ? (user.profile.avatar.startsWith('https') 
            ? user.profile.avatar 
            : `${process.env.NEXT_PUBLIC_API_BASE_URL}${user.profile.avatar}`) 
        : null;
    return (
        <div className="w-80 bg-white border-r border-gray-200 h-dvh p-4 fixed top-0 left-0 pt-10 flex flex-col justify-between">
            <div>
                <div className="flex items-center mb-8 mt-0 md:mt-20 cursor-pointer" onClick={handleAvatarClick}>
                    <Avatar className="mr-4 w-20 h-20">
                        <AvatarImage src={avatarUrl} alt="User avatar" />
                        <AvatarFallback>{user.initials}</AvatarFallback>
                    </Avatar>
                    <div>
                        <h2 className="font-semibold">{user.first_name} {user.last_name}</h2>
                        <p className="text-gray-500">
                            {`${getSubtitle(user)}`}
                        </p>
                    </div>
                </div>
                <nav className="space-y-2">
                    {user.user_type === UserType.COLLEGE_STUDENT_TYPE && (
                        <>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/profile/availability")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/profile/availability",
                                })}
                                >
                                <span className="mr-2">
                                    <AlarmClock className="w-4 h-4" />
                                </span> Availability
                            </Button>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/college-student/bookings")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/college-student/bookings",
                                })}
                                >
                                <span className="mr-2">
                                    <Calendar className="w-4 h-4" />
                                </span> Bookings
                            </Button>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/college-student/payments")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/college-student/payments",
                                })}
                                >
                                <span className="mr-2">
                                    <CircleDollarSign className="w-4 h-4" />
                                </span> Payments
                                
                            </Button>
                        </>
                    )}
                    {user.user_type === UserType.COUNSELOR_ADMINISTRATOR_TYPE && (
                        <>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/find-trailblazers")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/find-trailblazers",
                                })}
                            >
                                <span className="mr-2">
                                    <Search className="w-4 h-4" />
                                </span> Find Trailblazers
                            </Button>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/counselor/bookings")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/counselor/bookings",
                                })}
                                >
                                <span className="mr-2">
                                    <Calendar className="w-4 h-4" />
                                </span> Bookings
                            </Button>
                        </>
                    )}
                    {user.user_type === UserType.HIGH_SCHOOL_STUDENT_TYPE && (
                        <>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/find-trailblazers")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/find-trailblazers",
                                })}
                            >
                                <span className="mr-2">
                                    <Search className="w-4 h-4" />
                                </span> Find Trailblazers
                            </Button>
                            <Button
                                variant="ghost"
                                onClick={() => router.push("/high-schooler/bookings")}
                                className={cn("w-full justify-start px-4", {
                                    "bg-gray-100": pathname === "/high-schooler/bookings",
                                })}
                                >
                                <span className="mr-2">
                                    <Calendar className="w-4 h-4" />
                                </span> Bookings
                            </Button>
                        </>
                    )}
                </nav>
            </div>
            <Button
                variant="ghost"
                onClick={handleLogout}
                className="w-full justify-start px-4 mt-auto md:mb-4"
            >
                <span className="mr-2">
                    <LogOut className="w-4 h-4" />
                </span> Logout
            </Button>
        </div>
    )
}
