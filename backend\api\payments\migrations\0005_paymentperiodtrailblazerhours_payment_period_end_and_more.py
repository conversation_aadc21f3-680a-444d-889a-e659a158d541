# Generated by Django 4.2.13 on 2025-02-23 16:30

from django.db import migrations, models
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0004_rename_trailblazerhours_paymentperiodtrailblazerhours_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentperiodtrailblazerhours',
            name='payment_period_end',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
        migrations.AddField(
            model_name='paymentperiodtrailblazerhours',
            name='payment_period_start',
            field=models.DateTimeField(default=django.utils.timezone.now),
        ),
    ]
