# Generated by Django 4.2.13 on 2025-06-07 03:04

import django.contrib.postgres.fields
from django.db import migrations, models


class Migration(migrations.Migration):

    initial = True

    dependencies = [
    ]

    operations = [
        migrations.CreateModel(
            name='University',
            fields=[
                ('unitid', models.CharField(help_text='Unique identifier for the institution', max_length=50, primary_key=True, serialize=False)),
                ('institution', models.CharField(help_text='Name of the institution', max_length=255)),
                ('city', models.Char<PERSON>ield(help_text='City where the institution is located', max_length=100)),
                ('state_territory', models.Char<PERSON>ield(help_text='State or territory where the institution is located', max_length=50)),
                ('zip_code', models.CharField(help_text='Zip code of the institution', max_length=20)),
                ('institution_website', models.URLField(blank=True, help_text='Website URL of the institution', max_length=255, null=True)),
                ('predominant_degree_awarded', models.Char<PERSON>ield(blank=True, help_text='The most commonly awarded degree at the institution', max_length=100, null=True)),
                ('highest_degree_awarded', models.CharField(blank=True, help_text='The highest degree that can be earned at the institution', max_length=100, null=True)),
                ('public_private', models.CharField(blank=True, help_text='Indicates whether the institution is public or private', max_length=50, null=True)),
                ('region', models.CharField(blank=True, help_text='Geographical region of the institution', max_length=100, null=True)),
                ('locale', models.CharField(blank=True, help_text='Locale type of the institution (e.g., urban, rural)', max_length=100, null=True)),
                ('special_focus', models.CharField(blank=True, help_text='Special focus of the institution, if any', max_length=255, null=True)),
                ('is_hbcu', models.BooleanField(blank=True, help_text='Indicates if the institution is an HBCU', null=True)),
                ('is_tribal', models.BooleanField(blank=True, help_text='Indicates if the institution is a tribal college or native-serving institution', null=True)),
                ('is_aanapisi', models.BooleanField(blank=True, help_text='Indicates if the institution serves Asian American & Native American Pacific Islander populations', null=True)),
                ('is_hsi', models.BooleanField(blank=True, help_text='Indicates if the institution is Hispanic-serving', null=True)),
                ('is_single_gender', models.BooleanField(blank=True, help_text='Indicates if the institution is single-gender', null=True)),
                ('religious_affiliation', models.CharField(blank=True, help_text='Religious affiliation of the institution, if any', max_length=255, null=True)),
                ('sat_reading_25th', models.IntegerField(blank=True, help_text='25th percentile SAT Reading/Writing score', null=True)),
                ('sat_reading_75th', models.IntegerField(blank=True, help_text='75th percentile SAT Reading/Writing score', null=True)),
                ('sat_math_25th', models.IntegerField(blank=True, help_text='25th percentile SAT Math score', null=True)),
                ('sat_math_75th', models.IntegerField(blank=True, help_text='75th percentile SAT Math score', null=True)),
                ('act_25th', models.IntegerField(blank=True, help_text='25th percentile ACT score', null=True)),
                ('act_75th', models.IntegerField(blank=True, help_text='75th percentile ACT score', null=True)),
                ('has_open_admissions', models.BooleanField(blank=True, help_text='Indicates if the institution has an open admissions policy', null=True)),
                ('acceptance_rate', models.FloatField(blank=True, help_text='Percentage of applicants accepted', null=True)),
                ('undergrad_count', models.IntegerField(blank=True, help_text='Number of undergraduate students', null=True)),
                ('pct_white_students', models.FloatField(blank=True, help_text='Percentage of white students', null=True)),
                ('pct_black_students', models.FloatField(blank=True, help_text='Percentage of Black students', null=True)),
                ('pct_hispanic_students', models.FloatField(blank=True, help_text='Percentage of Hispanic students', null=True)),
                ('pct_asian_students', models.FloatField(blank=True, help_text='Percentage of Asian students', null=True)),
                ('pct_aian_students', models.FloatField(blank=True, help_text='Percentage of American Indian or Alaska Native students', null=True)),
                ('pct_nhpi_students', models.FloatField(blank=True, help_text='Percentage of Native Hawaiian or Pacific Islander students', null=True)),
                ('pct_multiracial_students', models.FloatField(blank=True, help_text='Percentage of students identifying as two or more races', null=True)),
                ('pct_unknown_race_students', models.FloatField(blank=True, help_text='Percentage of students whose race is unknown', null=True)),
                ('pct_men_students', models.FloatField(blank=True, help_text='Percentage of male students', null=True)),
                ('pct_women_students', models.FloatField(blank=True, help_text='Percentage of female students', null=True)),
                ('student_faculty_ratio', models.FloatField(blank=True, help_text='Ratio of students to faculty', null=True)),
                ('pct_white_faculty', models.FloatField(blank=True, help_text='Percentage of white faculty', null=True)),
                ('pct_black_faculty', models.FloatField(blank=True, help_text='Percentage of Black faculty', null=True)),
                ('pct_hispanic_faculty', models.FloatField(blank=True, help_text='Percentage of Hispanic faculty', null=True)),
                ('pct_asian_faculty', models.FloatField(blank=True, help_text='Percentage of Asian faculty', null=True)),
                ('pct_aian_faculty', models.FloatField(blank=True, help_text='Percentage of American Indian or Alaska Native faculty', null=True)),
                ('pct_nhpi_faculty', models.FloatField(blank=True, help_text='Percentage of Native Hawaiian or Pacific Islander faculty', null=True)),
                ('pct_multiracial_faculty', models.FloatField(blank=True, help_text='Percentage of faculty identifying as two or more races', null=True)),
                ('pct_unknown_race_faculty', models.FloatField(blank=True, help_text='Percentage of faculty whose race is unknown', null=True)),
                ('pct_men_faculty', models.FloatField(blank=True, help_text='Percentage of male faculty', null=True)),
                ('pct_women_faculty', models.FloatField(blank=True, help_text='Percentage of female faculty', null=True)),
                ('degrees_agriculture', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Agriculture")),
                ('degrees_resources', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Natural Resources & Conservation")),
                ('degrees_architecture', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Architecture")),
                ('degrees_ethnic_cultural_gender', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Area, Ethnic, Cultural, Gender, & Group Studies")),
                ('degrees_communication', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Communication")),
                ('degrees_communications_tech', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Communications Technologies")),
                ('degrees_computer_science', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Computer & Information Sciences")),
                ('degrees_culinary', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Personal & Culinary Services")),
                ('degrees_education', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Education")),
                ('degrees_engineering', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Engineering")),
                ('degrees_engineering_tech', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Engineering Technologies")),
                ('degrees_language', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Foreign Languages")),
                ('degrees_family_consumer_science', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Family & Consumer Sciences")),
                ('degrees_legal', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Legal Professions")),
                ('degrees_english', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in English Language & Literature")),
                ('degrees_humanities', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Liberal Arts & Sciences")),
                ('degrees_library', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Library Science")),
                ('degrees_biological', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Biological & Biomedical Sciences")),
                ('degrees_mathematics', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Mathematics & Statistics")),
                ('degrees_military', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Military Technologies")),
                ('degrees_multidiscipline', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Multi/Interdisciplinary Studies")),
                ('degrees_parks_recreation_fitness', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Parks, Recreation, Leisure, & Fitness Studies")),
                ('degrees_philosophy', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Philosophy & Religious Studies")),
                ('degrees_theology', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Theology & Religious Vocations")),
                ('degrees_physical_science', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Physical Sciences")),
                ('degrees_science_tech', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Science Technologies")),
                ('degrees_psychology', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Psychology")),
                ('degrees_security_law_enforcement', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Homeland Security, Law Enforcement, etc.")),
                ('degrees_public_admin', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Public Administration & Social Service")),
                ('degrees_social_science', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Social Sciences")),
                ('degrees_construction', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Construction Trades")),
                ('degrees_mechanic_repair_tech', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Mechanic & Repair Technologies")),
                ('degrees_precision_production', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Precision Production")),
                ('degrees_transportation', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Transportation & Materials Moving")),
                ('degrees_visual_performing', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Visual & Performing Arts")),
                ('degrees_health', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Health Professions")),
                ('degrees_business', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in Business")),
                ('degrees_history', models.CharField(max_length=50, null=True, blank=True, help_text="Associate or Bachelor's in History")),
                ('pct_degrees_agriculture', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Agriculture', null=True)),
                ('pct_degrees_resources', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Natural Resources & Conservation', null=True)),
                ('pct_degrees_architecture', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Architecture', null=True)),
                ('pct_degrees_ethnic_cultural_gender', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Area, Ethnic, Cultural, Gender, & Group Studies', null=True)),
                ('pct_degrees_communication', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Communication', null=True)),
                ('pct_degrees_communications_tech', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Communications Technologies', null=True)),
                ('pct_degrees_computer_science', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Computer & Information Sciences', null=True)),
                ('pct_degrees_culinary', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Personal & Culinary Services', null=True)),
                ('pct_degrees_education', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Education', null=True)),
                ('pct_degrees_engineering', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Engineering', null=True)),
                ('pct_degrees_engineering_tech', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Engineering Technologies', null=True)),
                ('pct_degrees_language', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Foreign Languages', null=True)),
                ('pct_degrees_family_consumer_science', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Family & Consumer Sciences', null=True)),
                ('pct_degrees_legal', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Legal Professions', null=True)),
                ('pct_degrees_english', models.FloatField(blank=True, help_text='Percentage of degrees awarded in English Language & Literature', null=True)),
                ('pct_degrees_humanities', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Liberal Arts & Sciences', null=True)),
                ('pct_degrees_library', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Library Science', null=True)),
                ('pct_degrees_biological', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Biological & Biomedical Sciences', null=True)),
                ('pct_degrees_mathematics', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Mathematics & Statistics', null=True)),
                ('pct_degrees_military', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Military Technologies', null=True)),
                ('pct_degrees_multidiscipline', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Multi/Interdisciplinary Studies', null=True)),
                ('pct_degrees_parks_recreation_fitness', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Parks, Recreation, Leisure, & Fitness Studies', null=True)),
                ('pct_degrees_philosophy', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Philosophy & Religious Studies', null=True)),
                ('pct_degrees_theology', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Theology & Religious Vocations', null=True)),
                ('pct_degrees_physical_science', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Physical Sciences', null=True)),
                ('pct_degrees_science_tech', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Science Technologies', null=True)),
                ('pct_degrees_psychology', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Psychology', null=True)),
                ('pct_degrees_security_law_enforcement', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Homeland Security, Law Enforcement, etc.', null=True)),
                ('pct_degrees_public_admin', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Public Administration & Social Service', null=True)),
                ('pct_degrees_social_science', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Social Sciences', null=True)),
                ('pct_degrees_construction', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Construction Trades', null=True)),
                ('pct_degrees_mechanic_repair_tech', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Mechanic & Repair Technologies', null=True)),
                ('pct_degrees_precision_production', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Precision Production', null=True)),
                ('pct_degrees_transportation', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Transportation & Materials Moving', null=True)),
                ('pct_degrees_visual_performing', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Visual & Performing Arts', null=True)),
                ('pct_degrees_health', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Health Professions', null=True)),
                ('pct_degrees_business', models.FloatField(blank=True, help_text='Percentage of degrees awarded in Business', null=True)),
                ('pct_degrees_history', models.FloatField(blank=True, help_text='Percentage of degrees awarded in History', null=True)),
                ('retention_rate', models.FloatField(blank=True, help_text='Percentage of students retained after their first year', null=True)),
                ('graduation_rate', models.FloatField(blank=True, help_text='Percentage of students who graduate within a standard timeframe', null=True)),
                ('completion_rate_150_pooled', models.FloatField(blank=True, help_text='Completion rate within 150% of expected time (pooled 2-year avg)', null=True)),
                ('completion_rate_150', models.FloatField(blank=True, help_text='Completion rate within 150% of expected time', null=True)),
                ('completion_rate_150_white', models.FloatField(blank=True, help_text='Completion rate for White students within 150% of expected time', null=True)),
                ('completion_rate_150_black', models.FloatField(blank=True, help_text='Completion rate for Black students within 150% of expected time', null=True)),
                ('completion_rate_150_hispanic', models.FloatField(blank=True, help_text='Completion rate for Hispanic students within 150% of expected time', null=True)),
                ('completion_rate_150_asian', models.FloatField(blank=True, help_text='Completion rate for Asian students within 150% of expected time', null=True)),
                ('completion_rate_150_aian', models.FloatField(blank=True, help_text='Completion rate for American Indian or Alaska Native students within 150% of expected time', null=True)),
                ('completion_rate_150_nhpi', models.FloatField(blank=True, help_text='Completion rate for Native Hawaiian or Pacific Islander students within 150% of expected time', null=True)),
                ('completion_rate_150_multiracial', models.FloatField(blank=True, help_text='Completion rate for students of two or more races within 150% of expected time', null=True)),
                ('completion_rate_150_unknown_race', models.FloatField(blank=True, help_text='Completion rate for students with unknown race within 150% of expected time', null=True)),
                ('avg_annual_cost', models.FloatField(blank=True, help_text='Average annual cost of attendance', null=True)),
                ('avg_net_price_0_30k', models.FloatField(blank=True, help_text='Average net price for families with income $0 - $30,000', null=True)),
                ('avg_net_price_30k_48k', models.FloatField(blank=True, help_text='Average net price for families with income $30,001 - $48,000', null=True)),
                ('avg_net_price_48k_75k', models.FloatField(blank=True, help_text='Average net price for families with income $48,001 - $75,000', null=True)),
                ('avg_net_price_75k_110k', models.FloatField(blank=True, help_text='Average net price for families with income $75,001 - $110,000', null=True)),
                ('avg_net_price_110k_plus', models.FloatField(blank=True, help_text='Average net price for families with income above $110,000', null=True)),
                ('pct_students_pell', models.FloatField(blank=True, help_text='Percentage of students awarded Pell grants', null=True)),
                ('pct_students_federal_loan', models.FloatField(blank=True, help_text='Percentage of students with federal loans', null=True)),
                ('median_debt_pell', models.FloatField(blank=True, help_text='Median debt amount for Pell grant recipients', null=True)),
                ('median_debt_firstgen', models.FloatField(blank=True, help_text='Median debt amount for first-generation college students', null=True)),
                ('median_earnings_10yrs', models.FloatField(blank=True, help_text='Median earnings 10 years after entry into institution', null=True)),
                ('earnings_25th_pctl_10yrs', models.FloatField(blank=True, help_text='25th percentile earnings 10 years after entry', null=True)),
                ('earnings_75th_pctl_10yrs', models.FloatField(blank=True, help_text='75th percentile earnings 10 years after entry', null=True)),
                ('embedding', django.contrib.postgres.fields.ArrayField(base_field=models.FloatField(), blank=True, help_text='Vector embedding generated from university information for similarity search', null=True, size=1536)),
            ],
            options={
                'verbose_name': 'University',
                'verbose_name_plural': 'Universities',
                'ordering': ['institution'],
            },
        ),
    ]
