# Generated by Django 4.2.13 on 2025-02-25 21:51

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0011_skippedpaymentlog_payout_batch_id_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentlog',
            name='time_processed',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='time_processed',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='skippedpaymentlog',
            name='paypal_email',
            field=models.CharField(max_length=254),
        ),
    ]
