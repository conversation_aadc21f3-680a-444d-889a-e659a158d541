# Generated by Django 4.2.13 on 2025-06-26 19:14

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0036_alter_highschoolstudentprofile_extracurriculars_and_more'),
    ]

    operations = [
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='household_income',
            field=models.CharField(blank=True, choices=[('under_30k', 'Under $30,000'), ('30k_48k', '$30,001-$48,000'), ('48k_75k', '$48,001-$75,000'), ('75k_100k', '$75,001-$100,000'), ('100k_150k', '$100,001 - $150,000'), ('150k_plus', '$150,001+')], max_length=50, null=True),
        ),
    ]
