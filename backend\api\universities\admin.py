from django.contrib import admin
from api.universities.models import University

@admin.register(University)
class UniversityAdmin(admin.ModelAdmin):
    list_display = ('unitid', 'institution', 'city', 'state_territory', 'public_private')
    search_fields = ('institution', 'city', 'state_territory')
    list_filter = ('public_private', 'region', 'is_hbcu', 'is_hsi')
    readonly_fields = ('embedding',)