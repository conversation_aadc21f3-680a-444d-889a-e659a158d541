## College Students Search and Filter API
- **Endpoint:** `/api/v1/college-students/`
- **Methods:** GET
- **Parameters:**
  - `search`: Keyword search on Trailblazer profiles (e.g., `search=Engineering`)
  - `college_type`: Filter by college type (e.g., `college_type=public`)
  - `major`: Filter by major (e.g., `major=Engineering`)
  - `interests`: Filter by interests (e.g., `interests=Coding`)
  - `availability`: Filter by availability (e.g., `availability=true`)
  - `user_zip`: User's organization zip code for proximity sorting (e.g., `user_zip=12345`)
  - `ordering`: Order results by `distance` (e.g., `ordering=distance`)
- **Responses:**
  - `200 OK`: Returns a paginated list of college students matching the criteria.
  - `400 Bad Request`: Invalid filter criteria.
  - `404 Not Found`: No students match the search criteria.
