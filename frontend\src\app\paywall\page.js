"use client"

import React, { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import {
    ArrowLeft, Award, CreditCard, Video, Search, Users, MailCheck, Lightbulb, FileText, Banknote, Send,
    ShieldCheck, BadgeCheck, ThumbsUp, CheckCircle2, <PERSON><PERSON><PERSON>riangle
} from 'lucide-react'

// Import shadcn/ui components
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"

// Helper component for feature list items
const FeatureItem = ({ icon, children }) => (
    <li className="flex items-start">
        <div className="bg-green-50 text-primary rounded-full w-5 h-5 flex items-center justify-center mr-3 flex-shrink-0 mt-1">
            {React.cloneElement(icon, { size: 12 })}
        </div>
        <div className="text-sm text-gray-600">{children}</div>
    </li>
)

// Header Component
const PageHeader = ({ collegeId }) => {
  const router = useRouter()
  return (
  <header className="bg-white shadow-sm sticky top-0 z-30 border-b">
    <div className="mx-auto px-4 sm:px-6 lg:px-8">
      <div className="flex justify-between items-center h-16">
        <div className="flex items-center">
          <Link href="/recommendations" className="flex items-center">
            <img 
              alt="Trailblazer Logo" 
              className="h-6" 
              src="/logo.svg"
            />
          </Link>
        </div>
        <div className="flex items-center">
          <button
            onClick={() => router.back()}
            className="flex items-center text-sm text-primary font-medium hover:-translate-x-1 transition-transform duration-200 hover:text-primary"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            <span>Back</span>
          </button>
        </div>
      </div>
    </div>
  </header>
)}

// Subscription Card Component
const SubscriptionOptionCard = ({ setViewMode }) => {
    // Stripe payment link for subscription
    const stripeLink = "https://buy.stripe.com/8x2aEX1MkeBQ4k4gpB8Zq06"
    
    const handleSubscribe = (e) => {
        // In a real implementation, this would handle Stripe checkout
        // For demo purposes, we'll simulate the subscription process
        // The actual Stripe integration would happen on the Stripe-hosted page
        console.log('Redirecting to Stripe checkout...')
    }

    return (
        <Card className="w-full md:w-1/2 mb-8 md:mb-0 flex flex-col transition-transform ease-in-out hover:-translate-y-1 hover:shadow-lg duration-300">
            <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <Award className="w-8 h-8 text-primary" />
                </div>
                <div className='h-[120px]'>
                    <CardTitle className="text-2xl font-semibold text-gray-800 mb-1">Premium Access</CardTitle>
                    <div className="text-5xl font-bold mb-2 text-primary bg-clip-text">
                        $40<span className="text-xl font-medium">/mo</span>
                    </div>
                    <CardDescription className="text-gray-600">
                        Directly connect with student advisors.
                    </CardDescription>
                </div>
            </CardHeader>
            
            <CardContent className="flex-grow">
                <div className="bg-gray-50 p-6 rounded-lg">
                    <h3 className="font-semibold text-gray-700 mb-3 text-base">What's included:</h3>
                    <ul className="space-y-3">
                        <FeatureItem icon={<Video />}>
                            <strong>Two 60-minute</strong> Trailblazer advising sessions per month
                        </FeatureItem>
                        <FeatureItem icon={<Search />}>
                            <strong>Customized</strong> advisor search
                        </FeatureItem>
                        <FeatureItem icon={<Users />}>
                            <strong>Access nationwide</strong> Trailblazer network
                        </FeatureItem>
                    </ul>
                </div>
            </CardContent>
            
            <CardFooter className="pt-6">
                <div className="w-full">
                    <Button asChild size="lg" className="w-full bg-primary hover:bg-green-500 hover:text-white">
                        <Link href={stripeLink} onClick={handleSubscribe}>
                            <CreditCard className="w-5 h-5 mr-2.5" />
                            Subscribe for $40/month
                        </Link>
                    </Button>
                    <p className="text-xs text-gray-500 text-center mt-3">
                        Secure payment processing by Stripe.
                    </p>
                </div>
            </CardFooter>
        </Card>
    )
}

// School Contact Card Component
const SchoolContactOptionCard = ({ setViewMode }) => {
    // Pre-filled email template for contacting school/nonprofit
    const mailtoLink = "mailto:?subject=Request%20to%20Connect%20with%20First-Gen%20College%20Trailblazers&body=Hi%2C%0A%0AI%20hope%20you're%20doing%20well!%20As%20I%20explore%20college%20options%2C%20I'd%20love%20to%20connect%20with%20current%20first-generation%20college%20students%20from%20our%20community%20who've%20been%20through%20the%20process%20to%20learn%20from%20their%20experiences.%0A%0AI%20used%20a%20platform%20called%20Trailblazer%20to%20build%20my%20college%20list%20based%20on%20my%20profile%20and%20interests.%20It%20also%20enables%20high%20schoolers%20and%20counselors%20to%20connect%20with%20first-gen%20students%20at%20colleges%20across%20the%20country.%20It's%20an%20affordable%20way%20for%20schools%2Fnonprofits%20to%20get%20their%20students%20relatable%20guidance%20from%20hometown%20peers%20(including%20alums!).%20You%20can%20learn%20more%20here%3A%20https%3A%2F%2Fwelcome.trailblazergo.com%2F%0A%0AWould%20it%20be%20possible%20for%20us%20to%20get%20access%3F%20I've%20CC'd%20the%20Trailblazer%20team%20in%20case%20you%20have%20any%20questions!%0A%0AWarmly%2C%0A%5BYour%20Name%5D&cc=jeff%40trailblazergo.com%2Ccontact%40trailblazergo.com"

    const handleContact = (e) => {
        // For demo purposes, prevent actual email client opening and show contacted state
        e.preventDefault()
        // setViewMode('contacted')
        // In production, you might want to track this action or allow the email to open
        window.location.href = mailtoLink
    }

    return (
        <Card className="w-full md:w-1/2 flex flex-col transition-transform ease-in-out hover:-translate-y-1 hover:shadow-lg duration-300">
            <CardHeader className="text-center pb-4">
                <div className="w-16 h-16 flex items-center justify-center mx-auto mb-4">
                    <MailCheck className="w-8 h-8 text-primary" />
                </div>
                <div className='h-[120px]'>
                    <CardTitle className="text-2xl font-semibold text-gray-800 mb-1">
                        Contact Your School or Nonprofit
                    </CardTitle>
                    <CardDescription className="text-gray-600">
                        Let your school/program leaders know you want free access to Trailblazer
                    </CardDescription>
                </div>
            </CardHeader>
            
            <CardContent className="flex-grow">
                <div className="bg-gray-50 p-5 rounded-lg mb-6">
                    <h3 className="font-semibold text-gray-700 mb-3 text-base">How it works:</h3>
                    <ul className="space-y-2.5">
                        <FeatureItem icon={<Users />}>
                            Talk to your counselor, teacher, or principal about how Trailblazer can help you reach your college goals.
                        </FeatureItem>
                        <FeatureItem icon={<Lightbulb />}>
                            Explain how Trailblazer connects local students with first-gen college students from the community.
                        </FeatureItem>
                        <FeatureItem icon={<FileText />}>
                            Use our email template below to take the first step: copy, personalize, and send!
                        </FeatureItem>
                    </ul>
                </div>
                
                {/* Referral Incentive */}
                <div className="bg-green-50 p-4 rounded-lg">
                    <div className="flex flex-col items-start">
                        <div className="flex items-center mb-2">
                            <Banknote className="w-8 h-8 text-primary mr-2" />
                            <h4 className="font-semibold text-primary text-lg">Referral Bonus!</h4>
                        </div>
                        <p className="text-sm text-gray-600">
                            Refer your school or education nonprofit and when they partner with us, we'll pay you $300 cash!
                        </p>
                    </div>
                </div>
            </CardContent>
            
            <CardFooter className="pt-6">
                <div className="w-full">
                    <Button asChild variant="outline" size="lg" className="w-full border-green-600 text-primary hover:bg-green-50">
                        <a href={mailtoLink} onClick={handleContact}>
                            <Send className="w-5 h-5 mr-2.5" />
                            Contact Your Counselor
                        </a>
                    </Button>
                    <p className="text-xs text-gray-500 text-center mt-3">
                        We're actively partnering with schools and nonprofits nationwide.
                    </p>
                </div>
            </CardFooter>
        </Card>
    )
}

// Trust Indicator Item Component
const TrustIndicatorItem = ({ icon, title, description }) => (
    <div className="flex flex-col items-center text-center">
        {React.cloneElement(icon, { className: "w-10 h-10 text-primary mb-2" })}
        <h4 className="font-medium text-gray-800 mb-1 text-base">{title}</h4>
        <p className="text-sm text-gray-600">{description}</p>
    </div>
)

// Initial State View - Main landing page with both options
const InitialView = ({ setViewMode, collegeId }) => (
    <div>
        <div className="text-center mb-10">
            <h1 className="text-3xl md:text-4xl font-bold text-gray-900 mb-3">
                Connect with First-gen College Students
            </h1>
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
                Unlock personalized guidance from current first-generation college students. Choose your path to connect.
            </p>
        </div>

        {/* Main subscription and contact options */}
        <div className="flex flex-col md:flex-row md:gap-8 mb-16">
            <SubscriptionOptionCard setViewMode={setViewMode} />
            <SchoolContactOptionCard setViewMode={setViewMode} />
        </div>

        {/* Trust indicators section */}
        <div className="text-center">
            <h3 className="text-lg font-semibold text-gray-700 mb-6">
                Why Choose Trailblazer?
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8 max-w-3xl mx-auto">
                <TrustIndicatorItem 
                    icon={<ShieldCheck />} 
                    title="Secure & Private" 
                    description="Your data is protected and confidential." 
                />
                <TrustIndicatorItem 
                    icon={<BadgeCheck />} 
                    title="Verified Students" 
                    description="All Trailblazer advisors are verified current college students." 
                />
                <TrustIndicatorItem 
                    icon={<ThumbsUp />} 
                    title="Impactful Guidance" 
                    description="Gain relatable advice from those who've walked the path." 
                />
            </div>
        </div>
    </div>
)

// Subscribed State View - Success state after subscription
const SubscribedView = ({ setViewMode }) => (
    <div className="text-center py-16">
        <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <CheckCircle2 className="w-12 h-12 text-primary" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-3">
            Subscription Successful!
        </h2>
        <p className="text-lg text-gray-600 mb-8 max-w-md mx-auto">
            Welcome to Trailblazer Premium! You now have full access to connect with student advisors and schedule your sessions.
        </p>
        <div className="space-x-0 sm:space-x-4 flex flex-col sm:flex-row justify-center items-center space-y-4 sm:space-y-0">
            <Button asChild className="w-full sm:w-auto bg-green-600 hover:bg-green-700">
                <Link href="/recommendations.html">Explore Advisors</Link>
            </Button>
            <Button 
                variant="outline" 
                onClick={() => setViewMode('initial')} 
                className="w-full sm:w-auto"
            >
                Back to Options
            </Button>
        </div>
    </div>
)

// Contacted State View - Shows email template after contact attempt
const ContactedView = ({ setViewMode }) => (
    <div className="py-12">
        <div className="text-center mb-8">
            <div className="w-20 h-20 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <MailCheck className="w-12 h-12 text-primary" />
            </div>
            <h2 className="text-3xl font-bold text-gray-900 mb-3">
                School Contact Initiated
            </h2>
            <p className="text-lg text-gray-600 mb-6 max-w-xl mx-auto">
                We've prepared an email template for you. Please review and send it to your counselor, teacher, or principal.
            </p>
        </div>
        
        <Card className="max-w-2xl mx-auto">
            <CardHeader>
                <CardTitle className="text-xl font-semibold text-gray-800">
                    Email to Your School
                </CardTitle>
                <CardDescription>
                    Copy and paste the following into your email client.
                </CardDescription>
            </CardHeader>
            
            <CardContent>
                <div className="bg-gray-50 p-4 rounded-md border border-gray-200 text-sm">
                    <p className="mb-1"><strong className="text-gray-700">To:</strong> [Counselor's Email Address]</p>
                    <p className="mb-1"><strong className="text-gray-700">Cc:</strong> <EMAIL>, <EMAIL></p>
                    <p className="mb-3"><strong className="text-gray-700">Subject:</strong> Request to Connect with First-Gen College Trailblazers</p>
                    <div className="text-gray-700 leading-relaxed space-y-2">
                        <p>Hi [Counselor's Name],</p>
                        <p>I hope you're doing well! As I explore college options, I'd love to connect with current first-generation college students from our community who've been through the process to learn from their experiences.</p>
                        <p>I used a platform called Trailblazer to build my college list based on my profile and interests. It also enables high schoolers and counselors to connect with first-gen students at colleges across the country. It's an affordable way for schools/nonprofits to get their students relatable guidance from hometown peers (including alums!). You can learn more here: https://welcome.trailblazergo.com/</p>
                        <p>Would it be possible for us to get access? I've CC'd the Trailblazer team in case you have any questions!</p>
                        <p>Warmly,<br />[Your Name]</p>
                    </div>
                </div>
            </CardContent>
            
            <CardFooter className="text-center">
                <Button 
                    variant="outline" 
                    onClick={() => setViewMode('initial')}
                    className="mx-auto"
                >
                    Back to Options
                </Button>
            </CardFooter>
        </Card>
    </div>
)

// Error State View - Handles any errors that might occur
const ErrorView = ({ setViewMode }) => (
    <div className="text-center py-16">
        <div className="w-20 h-20 bg-red-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <AlertTriangle className="w-12 h-12 text-red-600" />
        </div>
        <h2 className="text-3xl font-bold text-gray-900 mb-3">
            An Error Occurred
        </h2>
        <p className="text-lg text-gray-600 mb-8">
            Something went wrong with your request. Please try again.
        </p>
        <Button 
            onClick={() => setViewMode('initial')}
            className="bg-green-600 hover:bg-green-700"
        >
            Try Again
        </Button>
    </div>
)

// Main Page Component
export default function ConnectPage({ params }) {
    const { collegeId } = params
    // State management for different page views
    const [viewMode, setViewMode] = useState('initial') // 'initial', 'subscribed', 'contacted', 'error'

    // Scroll to top when view mode changes for better UX
    useEffect(() => {
        window.scrollTo(0, 0)
    }, [viewMode])

    return (
        <div className="bg-gray-50 min-h-screen">
            {/* Sticky header with navigation */}
            <PageHeader collegeId={collegeId} />
            
            {/* Main content area */}
            <main className="max-w-5xl mx-auto px-4 sm:px-6 lg:px-8 py-12">
                {/* Conditional rendering based on current state */}
                {viewMode === 'initial' && (
                    <InitialView setViewMode={setViewMode} collegeId={collegeId} />
                )}
                {viewMode === 'subscribed' && (
                    <SubscribedView setViewMode={setViewMode} />
                )}
                {viewMode === 'contacted' && (
                    <ContactedView setViewMode={setViewMode} />
                )}
                {viewMode === 'error' && (
                    <ErrorView setViewMode={setViewMode} />
                )}
            </main>
        </div>
    )
}
