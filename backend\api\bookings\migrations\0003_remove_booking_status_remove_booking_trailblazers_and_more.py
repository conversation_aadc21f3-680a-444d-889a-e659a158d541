# Generated by Django 4.2.13 on 2024-10-29 17:52

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('bookings', '0002_remove_booking_trailblazer_booking_trailblazers_and_more'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='booking',
            name='status',
        ),
        migrations.RemoveField(
            model_name='booking',
            name='trailblazers',
        ),
        migrations.AddField(
            model_name='booking',
            name='creator_status',
            field=models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled')], default='confirmed', max_length=20),
        ),
        migrations.AddField(
            model_name='booking',
            name='creator_status_updated_at',
            field=models.DateTimeField(auto_now=True),
        ),
        migrations.CreateModel(
            name='TrailblazerBookingStatus',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('confirmed', 'Confirmed'), ('cancelled', 'Cancelled')], default='pending', max_length=20)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('booking', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='trailblazer_statuses', to='bookings.booking')),
                ('trailblazer', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='booking_statuses', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'unique_together': {('booking', 'trailblazer')},
            },
        ),
    ]
