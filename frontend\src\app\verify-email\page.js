"use client"

import { useRouter, useSearchParams } from 'next/navigation'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { useState } from 'react'
import { getNextOnboardingStep } from '@/lib/utils'
import { Button } from "@/components/ui/button"
import { Header } from "@/components/ui/header"
import { IntroBanner } from "@/components/ui/introBanner"
import {
  InputOTP,
  InputOTPGroup,
  InputOTPSlot,
} from "@/components/ui/input-otp"
import { Loader2 } from 'lucide-react'


// VerificationForm component
const VerificationForm = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const email = searchParams.get('email') || ''
  const [code, setCode] = useState('')
  const [error, setError] = useState('')
  const [success, setSuccess] = useState(false)
  const [loading, setLoading] = useState(false)
  const [isReSendingCode, setIsReSendingCode] = useState(false)
  const { getToken } = useAuth()

  const resendVerificationCode = async () => {
    setIsReSendingCode(true)
    setError('')
    const authToken = getToken()
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/email-verification/send/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${authToken}`,
        },
        body: JSON.stringify({ email }),
      })
      const result = await response.json()
      if (response.ok) {
        setIsReSendingCode(false)
        setError('')
      }
    } catch (err) {
      console.error('An unexpected error occurred:', err)
      setError('Failed to resend verification code')
      setIsReSendingCode(false)
    }
  }

  const handleSubmit = async (e) => {
    e.preventDefault()
    setError('')
    setLoading(true)
    const authToken = getToken()
    try {
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/email-verification/verify/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Token ${authToken}`,
        },
        body: JSON.stringify({ email, code }),
      })
      const result = await response.json()
      if (response.ok) {
        setSuccess(true)
        handleEmailVerificationSuccess(result.user)
      } else {
        console.error(result)
        setError(result.detail || 'Invalid or expired code')
        setLoading(false)
      }
    } catch (err) {
      console.error('An unexpected error occurred:', err)
      setError('Failed to verify email')
      setLoading(false)
    }
  }

  const handleEmailVerificationSuccess = (user) => {
    router.push(getNextOnboardingStep(user.user_type, null))
  }

  return (
    <div className="p-8 md:w-1/2 lg:w-6/12 flex flex-col justify-center items-center" id="verification-form">
      <div className="w-full max-w-md">
        <h1 className="text-3xl font-bold mb-8" id="form-title">
          Verify your email
        </h1>
        <p className="mb-4" id="form-description">
          We just sent you a 6-digit code to your email. Enter it below:
        </p>
        {success && <p className="text-green-500 mb-4">Email verified successfully. Redirecting...</p>}
        {error && <p className="text-red-500 mb-4">{error}</p>}
        <form onSubmit={handleSubmit}>
          <InputOTP className="mb-4" maxLength={6} value={code} onChange={setCode}>
            <InputOTPGroup className="mx-auto mb-6">
              <InputOTPSlot index={0} className="w-14 h-14 text-lg" />
              <InputOTPSlot index={1} className="w-14 h-14 text-lg" />
              <InputOTPSlot index={2} className="w-14 h-14 text-lg" />
              <InputOTPSlot index={3} className="w-14 h-14 text-lg" />
              <InputOTPSlot index={4} className="w-14 h-14 text-lg" />
              <InputOTPSlot index={5} className="w-14 h-14 text-lg" />
            </InputOTPGroup>
          </InputOTP>
          <Button
            type="submit"
            className="w-full p-6 hover:bg-green-500 text-md"
            id="verify-button"
            disabled={loading}
          >
            {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Verify
          </Button>
        </form>
        <p className="mt-4 text-sm text-center text-gray-600 flex items-center justify-center">
          Didn&apos;t receive the code?
          <Button
            variant="ghost"
            onClick={resendVerificationCode}
            type="button"
            className="text-primary py-0 px-2 ml-2"
            disabled={isReSendingCode}
          >
            {isReSendingCode ? <Loader2 className="animate-spin inline-block" /> : 'Resend code'}
          </Button>
        </p>
      </div>
    </div>
  )
}

// Main VerifyEmailPage component
const VerifyEmailPage = () => {
  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex flex-col md:flex-row" id="main-content">
        <IntroBanner />
        <VerificationForm />
      </main>
    </div>
  )
}

export default withAuth(VerifyEmailPage)