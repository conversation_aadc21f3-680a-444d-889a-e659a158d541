from django.contrib.admin.sites import AdminSite
from api.users.admin import CollegeStudentProfileAdmin
from api.users.models import CollegeStudentProfile, User
from django.test import TestCase
from django.utils import timezone


class MockRequest:
    pass


class CollegeStudentProfileAdminTest(TestCase):
    def setUp(self):
        self.site = AdminSite()
        self.admin = CollegeStudentProfileAdmin(CollegeStudentProfile, self.site)
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='Password123!',
            user_type=User.COLLEGE_STUDENT_TYPE
        )
        self.profile = self.user.profile

    def test_background_check_passed_at_displayed_in_admin(self):
        # Check if the new field is in list_display
        self.assertIn('background_check_passed_at', self.admin.list_display)

    def test_background_check_passed_at_editable_in_admin(self):
        # Simulate setting the background_check_passed_at via admin
        now = timezone.now()
        self.profile.background_check_passed_at = now
        self.profile.save()
        self.assertEqual(self.profile.background_check_passed_at, now)
