"use client"

import Link from 'next/link'
import { useRout<PERSON>, use<PERSON><PERSON><PERSON> } from 'next/navigation'
import { useState, useEffect } from 'react'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { useForm } from "react-hook-form"
import { z } from "zod"
import { zodResolver } from "@hookform/resolvers/zod"
import { Eye, EyeOff } from 'lucide-react'
import { Header } from "@/components/ui/header"
import { useAuth } from '@/context/AuthProvider';
import { IntroBanner } from "@/components/ui/introBanner"
import { Loader2 } from 'lucide-react'


// Define the form schema with Zod
const formSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(8, "Password must be at least 8 characters")
    .regex(/^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[@$!%*?&])[A-Za-z\d@$!%*?&]{8,}$/, 
      "Password must contain at least one uppercase letter, one lowercase letter, one number, and one special character")
})

// SignUpForm component
function SignUpForm() {
  const router = useRouter()
  const [apiError, setApiError] = useState('')
  const [showPassword, setShowPassword] = useState(false)
  const { user, loginWithToken } = useAuth()
  const [ isLoading, setIsLoading ] = useState(false)

  const organizationId = useParams().organizationId

  const form = useForm({
    resolver: zodResolver(formSchema),
    mode: "onChange",
    defaultValues: {
      email: "",
      password: ""
    }
  })

  const onSubmit = async (data) => {
    setIsLoading(true)
    setApiError('')
    try {
      // Add organizationId to the data object
      data.organization = organizationId
      // Send the signup request
      const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/high-school-students/signup/`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(data),
      })
      const result = await response.json()
      if (response.ok) {
        loginWithToken(result.auth_token)

        await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/email-verification/send/`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'Authorization': `Token ${result.auth_token}`,
          },
          body: JSON.stringify({  
            email: data.email,
          }),
        })
        router.push('/verify-email?email=' + encodeURIComponent(data.email))
      } else {
        if (result && result.email) {
          setApiError(result.email[0])
          setIsLoading(false)
          return
        }
        setApiError(result.error || 'An unexpected error occurred')
        setIsLoading(false)
        return
      }
    } catch (error) {
      console.error('An unexpected error occurred:', error)
      setApiError('An unexpected error occurred')
      setIsLoading(false)
    }
  }

  return (
    <div className="p-8 md:w-1/2 lg:w-6/12 flex flex-col justify-center items-center">
      <div className="w-full max-w-md">

        <h1 className="text-4xl font-extrabold mb-8">Welcome Students!</h1>
        {apiError && <p className="text-red-500 mb-4">{apiError}</p>}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="email"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-md font-medium text-gray-700 mb-1">Email</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Enter your email"
                      className="p-6 text-md"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <FormField
              control={form.control}
              name="password"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="block text-md font-medium text-gray-700 mb-1">Password</FormLabel>
                  <FormControl>
                    <div className="relative">
                      <Input 
                        type={showPassword ? "text" : "password"} 
                        placeholder="Enter your password"
                        className="p-6 text-md"
                        {...field} 
                      />
                      <button
                        type="button"
                        className="absolute inset-y-0 right-0 pr-3 flex items-center"
                        onClick={() => setShowPassword(!showPassword)}
                      >
                        {showPassword ? <EyeOff className="h-4 w-4 text-gray-500" /> : <Eye className="h-4 w-4 text-gray-500" />}
                      </button>
                    </div>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            <Button
              type="submit"
              className="w-full p-6 hover:bg-green-500 text-md"
              disabled={isLoading}
            >
              {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Sign Up
            </Button>
          </form>
        </Form>
        <p className="mt-4 text-center text-sm text-gray-600">
          Already have an account?
          <Link href="/login" className="font-medium text-green-600 hover:text-green-500 ml-1">
            Log In
          </Link>
        </p>
        <p className="mt-4 text-center text-sm text-gray-600">
            <Link href="/" className="text-sm text-gray-600 hover:underline block">
                I&apos;m not a high school student
            </Link>
        </p>
      </div>
    </div>
  )
}

// Main SignUpPage component
const SignUpPage = () => {
  const [schoolName, setSchoolName] = useState('')
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState('')
  const organizationId = useParams().organizationId

  useEffect(() => {
    const fetchOrganization = async () => {
      try {
        const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/${organizationId}/name/`, {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json'
          },
        })
        if (response.ok) {
          const data = await response.json()
          setSchoolName(data.name)
          setIsLoading(false)
        } else if (response.status === 404) {
          setIsLoading(false)
        } else if (response.status === 401) {
          // Redirect handled by auth context
        } else {
          setError('An unexpected error occurred while fetching organization details')
          setIsLoading(false)
        }
      } catch (error) {
        console.error('Network error:', error)
        setError('Network error, please try again later')
        setIsLoading(false)
      }
    }
    fetchOrganization()
  }, [organizationId])

  return (
    <div className="min-h-screen flex flex-col">
      <Header />
      <main className="flex-grow flex flex-col md:flex-row" id="main-content">
        <IntroBanner 
            message={schoolName && (
                <span className="font-bold text-3xl md:text-4xl lg:text-5xl">
                    {schoolName}
                </span>
            )}
        />
        {error && <p className="text-red-500 mb-4">{error}</p>}
        <SignUpForm />
      </main>
    </div>
  )
}

export default SignUpPage