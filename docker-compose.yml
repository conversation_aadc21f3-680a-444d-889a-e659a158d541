volumes:
  909ba300__trailblazer_v1.1_updated_backend_local_postgres_data: {}
  909ba300__trailblazer_v1.1_updated_backend_local_postgres_data_backups: {}

services:
  backend:
    build:
      context: ./backend
      dockerfile: ./compose/Dockerfile
    image: 909ba300__trailblazer_v1.1_updated_backend_local_django
    container_name: 909ba300__trailblazer_v1.1_updated_backend_local_django
    depends_on:
      - postgres
    volumes:
      - ./backend:/app
    env_file:
      - ./backend/.envs/.local/.django
      - ./backend/.envs/.local/.postgres
    ports:
      - '${BACKEND_PORT:-8002}:8000'
    command: /start

  frontend:
    build:
      context: ./frontend
      dockerfile: ./compose/Dockerfile
    image: 909ba300__trailblazer_v1.1_updated_frontend_local_nextjs
    container_name: 909ba300__trailblazer_v1.1_updated_frontend_local_nextjs
    depends_on:
      - backend
    volumes:
      - ./frontend:/app
      # Don't mount node_modules and .next to avoid conflicts
      - /app/node_modules
      - /app/.next
    ports:
      - '${FRONTEND_PORT:-3002}:3000'
    command: "npm run dev"

  postgres:
    image: pgvector/pgvector:pg16
    container_name: 909ba300__trailblazer_v1.1_updated_backend_local_postgres
    volumes:
      - 909ba300__trailblazer_v1.1_updated_backend_local_postgres_data:/var/lib/postgresql/data
      - 909ba300__trailblazer_v1.1_updated_backend_local_postgres_data_backups:/backups
      # - ./init.sql:/docker-entrypoint-initdb.d/init.sql #CHANGE FOR DIFF ENVIRONEMENTS
      # Commented out init.sql because of an error on build
    env_file:
      - ./backend/.envs/.local/.postgres
    ports:
      - '5432:5432'