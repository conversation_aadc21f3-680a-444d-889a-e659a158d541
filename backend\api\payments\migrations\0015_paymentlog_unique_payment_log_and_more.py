# Generated by Django 4.2.13 on 2025-03-01 23:55

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('payments', '0014_remove_paymentperiodtrailblazerhours_payments_pa_trailbl_7790e6_idx_and_more'),
    ]

    operations = [
        migrations.AddConstraint(
            model_name='paymentlog',
            constraint=models.UniqueConstraint(fields=('payout_item_id', 'payout_batch_id'), name='unique_payment_log'),
        ),
        migrations.AddConstraint(
            model_name='paymentperiodtrailblazerhours',
            constraint=models.UniqueConstraint(fields=('user', 'paypal_email', 'payment_period_start', 'payment_period_end'), name='unique_trailblazer_payment_period_timesheet'),
        ),
        migrations.AddConstraint(
            model_name='skippedpaymentlog',
            constraint=models.UniqueConstraint(fields=('payout_item_id', 'payout_batch_id'), name='unique_skipped_payment_log'),
        ),
    ]
