from django.shortcuts import get_object_or_404

import logging
import sys
from django.db.models import F, FloatField
from django.db.models.functions import Cast, Abs
from django.db.models import Case, When
from django_filters.rest_framework import DjangoFilterBackend
from django.conf import settings

from rest_framework.views import APIView
from rest_framework import viewsets, mixins, generics, permissions, filters, serializers
from rest_framework.permissions import AllowAny, IsAuthenticated
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework import status
from rest_framework.authtoken.models import Token
from rest_framework.generics import CreateAPIView
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import NotAuthenticated, NotFound
import requests 
from math import radians, sin, cos, sqrt, atan2
from api.coordinate_storage.models import SharedAPIResult
from api.users.serializers import HighSchoolStudentProfileCreateSerializer, RecommendationsStatusSerializer, UserSerializer
from api.users.tasks import generate_recommendations_in_background
from api.users.models import HighSchoolStudentProfile
from api.users.permissions import IsHighSchoolStudent
from api.users.throttles import RecommendationRegenerationRateThrottle

from .filters import CollegeStudentFilter
from .permissions import IsUserOrReadOnly, IsAuthenticatedAndOwner, IsHighSchoolStudent
from .throttles import LoginRateThrottle, SignupRateThrottle, RecommendationRegenerationRateThrottle
from .models import (
    User,
    CollegeStudentProfile,
    HighSchoolStudentProfile,
    CounselorAdministratorProfile,
    Availability,
    OnboardingProgress
)
from .serializers import (
    CreateUserSerializer,
    UserSerializer,
    UserUpdateSerializer,
    AvailabilitySerializer,
    CollegeStudentSignUpSerializer,
    CounselorAdministratorSignUpSerializer,
    HighSchoolStudentSignUpSerializer,
    CollegeStudentOnboardingSerializer,
    HighSchoolStudentOnboardingSerializer,
    CounselorAdministratorOnboardingSerializer,
    LoginSerializer,
    HighSchoolStudentProfileDetailSerializer
)


class UserViewSet(mixins.RetrieveModelMixin,
                  mixins.UpdateModelMixin,
                  mixins.CreateModelMixin,
                  viewsets.GenericViewSet):
    """
    User view set
    """
    queryset = User.objects.all()
    serializer_class = UserSerializer
    permission_classes = (IsAuthenticatedAndOwner,)
    throttle_classes = [SignupRateThrottle]

    def get_serializer_class(self):
        """
        Return the serializer class based on the action.
        """
        if self.action == "create":
            return CreateUserSerializer
        elif self.action in ["update", "partial_update", "me"]:
            return UserUpdateSerializer
        return self.serializer_class

    def get_permissions(self):
        """
        Instantiates and returns the list of permissions that this view requires.
        """
        if self.action == "create":
            permission_classes = [AllowAny]
        else:
            permission_classes = self.permission_classes
        return [permission() for permission in permission_classes]

    @action(detail=False, methods=['get', 'put', 'patch'], url_path='me')
    def me(self, request):
        if request.method in ['PUT', 'PATCH']:
            serializer = self.get_serializer(request.user, data=request.data, partial=(request.method == 'PATCH'), context={'request': request})
            serializer.is_valid(raise_exception=True)
            serializer.save()
            return Response(status=status.HTTP_200_OK, data=UserSerializer(request.user).data)
        else:
            serializer = self.get_serializer(request.user, context={"request": request})
            return Response(status=status.HTTP_200_OK, data=UserSerializer(request.user).data)


class CollegeStudentViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing and editing college student instances with search, filter, and ordering by proximity.
    """
    queryset = User.objects.filter(
        user_type=User.COLLEGE_STUDENT_TYPE,
        college_student_profile__background_check_passed_at__isnull=False,
    ).select_related('availability')
    serializer_class = UserSerializer
    filterset_class = CollegeStudentFilter
    search_fields = [
        'first_name',
        'last_name',
        'college_student_profile__bio',
        'college_student_profile__interests',
        'college_student_profile__college_major',
        'college_student_profile__university',
        'college_student_profile__graduation_year',
        'college_student_profile__high_school_name',
        'college_student_profile__high_school_city',
        'college_student_profile__high_school_state',
        'college_student_profile__organization_tags__id',
        'college_student_profile__organization_tags__name',
    ]

    def get_queryset(self):
        queryset = super().get_queryset()
        user_zip = self.request.query_params.get('user_zip', None)
        if user_zip:
            user_location = self.get_location_from_zip(user_zip)
            if user_location:
                queryset = self.sort_by_distance(queryset, user_location)
        return queryset
    
    def get_location_from_zip(self, zip_code):

        # # Check if the result is already stored in the database
        try:
            stored_result = SharedAPIResult.objects.get(query=zip_code)
            return stored_result.result
        except SharedAPIResult.DoesNotExist:
            pass

        geocode_url = f"https://api.mapbox.com/search/geocode/v6/forward?postcode={zip_code}&country=united&access_token={settings.MAPBOX_API_KEY}"
        response = requests.get(geocode_url)
        if response.status_code == 200:
            data = response.json()
            if data['features']:
                coordinates = data['features'][0]['geometry']['coordinates']
                location = (coordinates[1], coordinates[0])
                SharedAPIResult.objects.create(query=zip_code, result=location)
                return location
        return None
    
    def sort_by_distance(self, queryset, user_location):
        student_locations = [
            (student.id, student.college_student_profile.high_school_zip_code, student.first_name, student.last_name)
            for student in queryset
        ]
        distances = self.get_distances(user_location, student_locations)
        sorted_students = sorted(distances, key=lambda x: x[1])
        sorted_ids = [student[0] for student in sorted_students]
        preserved_order = Case(*[When(pk=pk, then=pos) for pos, pk in enumerate(sorted_ids)])
        return queryset.filter(id__in=sorted_ids).order_by(preserved_order)
    
    def get_distances(self, user_location, student_locations):

        def haversine(lat1, lon1, lat2, lon2):
            R = 6371  # Earth radius in km
            dlat = radians(lat2 - lat1)
            dlon = radians(lon2 - lon1)
            a = sin(dlat/2)**2 + cos(radians(lat1)) * cos(radians(lat2)) * sin(dlon/2)**2
            c = 2 * atan2(sqrt(a), sqrt(1 - a))
            return R * c
    
        distances = []
        for student_id, zip_code, first_name, last_name in student_locations:
            if not zip_code or zip_code == "":
                print(f"Skipping student {first_name} {last_name} ({student_id}) due to missing ZIP code.")
                continue
            student_location = self.get_location_from_zip(zip_code)
            if student_location:
                student_lat, student_long = student_location
                distance = haversine(user_location[0], user_location[1], student_lat, student_long)
                distances.append((student_id, distance))
        return distances
    

class CounselorAdministratorViewSet(viewsets.ModelViewSet):
    queryset = User.objects.filter(user_type=User.COUNSELOR_ADMINISTRATOR_TYPE)
    serializer_class = UserSerializer


class HighSchoolStudentViewSet(viewsets.ModelViewSet):
    queryset = User.objects.filter(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
    serializer_class = UserSerializer


class CollegeStudentSignUpView(generics.CreateAPIView):
    queryset = User.objects.filter(user_type=User.COLLEGE_STUDENT_TYPE)
    serializer_class = CollegeStudentSignUpSerializer
    permission_classes = (AllowAny,)


class CounselorAdministratorSignUpView(generics.CreateAPIView):
    queryset = User.objects.filter(user_type=User.COUNSELOR_ADMINISTRATOR_TYPE)
    serializer_class = CounselorAdministratorSignUpSerializer
    permission_classes = (AllowAny,)


class HighSchoolStudentSignUpView(generics.CreateAPIView):
    queryset = User.objects.filter(user_type=User.HIGH_SCHOOL_STUDENT_TYPE)
    serializer_class = HighSchoolStudentSignUpSerializer
    permission_classes = (AllowAny,)


class IsOwner(permissions.BasePermission):
    """
    Custom permission to only allow owners of an object to access it.
    """
    def has_object_permission(self, request, view, obj):
        return obj.user == request.user


class AvailabilityViewSet(viewsets.ModelViewSet):
    """
    A viewset for viewing and editing user availability.
    """
    serializer_class = AvailabilitySerializer
    permission_classes = [permissions.IsAuthenticated, IsOwner]

    def get_queryset(self):
        return Availability.objects.filter(user=self.request.user)

    def perform_create(self, serializer):
        # If Availability exists, update it instead of creating a new one
        availability, created = Availability.objects.update_or_create(user=self.request.user, defaults=serializer.validated_data)
        self.update_user_profile_timezone(availability)

    def perform_update(self, serializer):
        availability = serializer.save()
        self.update_user_profile_timezone(availability)

    def get_object(self):
        # Retrieve the Availability instance for the current user or create one if it doesn't exist
        obj, created = Availability.objects.get_or_create(user=self.request.user)
        self.check_object_permissions(self.request, obj)
        return obj
    
    def update_user_profile_timezone(self, availability):
        user = availability.user
        if hasattr(user, 'profile'):
            user.profile.time_zone = availability.time_zone
            user.profile.save()

    @action(detail=False, methods=['get'])
    def current(self, request):
        """
        Retrieve the most recent availability for the authenticated user.
        """
        latest_availability = Availability.objects.filter(user=request.user).order_by('-created_at').first()
        if latest_availability:
            serializer = self.get_serializer(latest_availability)
            return Response(serializer.data, status=status.HTTP_200_OK)
        return Response({"detail": "No availability found."}, status=status.HTTP_404_NOT_FOUND)


class GenericOnboardingView(generics.UpdateAPIView, generics.RetrieveAPIView):
    """
    Generic onboarding view for all user types.
    """
    queryset = User.objects.all()

    def get_object(self):
        return self.request.user

    def get(self, request, *args, **kwargs):
        user = self.get_object()
        progress, created = OnboardingProgress.objects.get_or_create(user=user)
        return Response({"steps_completed": progress.steps_completed}, status=status.HTTP_200_OK) 

    def update(self, request, *args, **kwargs):
        raise NotImplementedError


class HighSchoolStudentOnboardingView(GenericOnboardingView):
    serializer_class = HighSchoolStudentOnboardingSerializer

    def update(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        onboarding_data = serializer.validated_data
        # Step info
        step = onboarding_data.pop('step', None)
        # Update user data
        first_name = onboarding_data.pop('first_name', None)
        last_name = onboarding_data.pop('last_name', None)
        user.first_name = first_name
        user.last_name = last_name
        user.save()
        # Update profile data
        profile = user.high_school_student_profile
        for key, value in onboarding_data.items():
            setattr(profile, key, value)
        profile.save()
        # Save progress
        update_onboarding_progress(user, step)
        return Response(status=status.HTTP_200_OK)


class CollegeStudentOnboardingView(GenericOnboardingView):
    serializer_class = CollegeStudentOnboardingSerializer

    def update(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        onboarding_data = serializer.validated_data

        # Step info
        step = onboarding_data.pop('step', None)

        # Update user data
        first_name = onboarding_data.pop('first_name', None)
        last_name = onboarding_data.pop('last_name', None)
        if first_name or last_name:
            user.first_name = first_name
            user.last_name = last_name
            user.save()
        # Availability
        availability_data = onboarding_data.pop('availability', None)
        if availability_data:
            availability, created = Availability.objects.update_or_create(user=user, defaults=availability_data)
            self.update_user_profile_timezone(availability)

        # Update profile data
        profile = user.college_student_profile
        organization_tags = onboarding_data.pop('organization_tags', None)  # Extract organization_tags
        for key, value in onboarding_data.items():
            setattr(profile, key, value)
        profile.save()

        # Update ManyToManyField for organization_tags
        if organization_tags is not None:
            profile.organization_tags.set(organization_tags)  # Use .set() to update the ManyToManyField

        # Save progress
        update_onboarding_progress(user, step)
        return Response(status=status.HTTP_200_OK)

    def update_user_profile_timezone(self, availability):
        user = availability.user
        if hasattr(user, 'profile'):
            user.profile.time_zone = availability.time_zone
            user.profile.save()


class CounselorAdministratorOnboardingView(GenericOnboardingView):
    serializer_class = CounselorAdministratorOnboardingSerializer

    def update(self, request, *args, **kwargs):
        user = self.get_object()
        serializer = self.get_serializer(data=request.data, partial=True)
        serializer.is_valid(raise_exception=True)
        onboarding_data = serializer.validated_data
        # Step info
        step = onboarding_data.pop('step', None)
        # Update user data
        first_name = onboarding_data.pop('first_name', None)
        last_name = onboarding_data.pop('last_name', None)
        if first_name or last_name:
            user.first_name = first_name
            user.last_name = last_name
            user.save()
        # Update profile data
        profile = user.counselor_administrator_profile
        for key, value in onboarding_data.items():
            setattr(profile, key, value)
        profile.save()
        # Save progress
        update_onboarding_progress(user, step)
        return Response(status=status.HTTP_200_OK)
        

def update_onboarding_progress(user, step):
    progress, created = OnboardingProgress.objects.get_or_create(user=user)
    progress.steps_completed.append(step)
    progress.save()


class LoginView(APIView):
    permission_classes = [AllowAny]
    throttle_classes = [LoginRateThrottle]
    
    def post(self, request):
        serializer = LoginSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            token, created = Token.objects.get_or_create(user=user)
            user_onboarding_progress = getattr(user, 'onboarding_progress', None)
            return Response({
                "token": token.key,
                "user_type": user.user_type,
                "onboarding_steps_completed": None if user_onboarding_progress is None else user_onboarding_progress.steps_completed
            }, status=status.HTTP_200_OK)
        else:
            # Extract error messages from the serializer
            error_messages = serializer.errors.get('non_field_errors', [])
            if error_messages:
                error_message = error_messages[0]
            else:
                error_message = "Invalid email or password."
            return Response({"error": error_message}, status=status.HTTP_401_UNAUTHORIZED)


class MajorListView(APIView):
    permission_classes = [AllowAny]
    
    def get(self, request):
        try:
            # Get distinct majors from profiles with passed background check
            majors = CollegeStudentProfile.objects.filter(
                background_check_passed_at__isnull=False
            ).values_list('college_major', flat=True).distinct().order_by('college_major')
            
            return Response(list(majors))
        except Exception as e:
            return Response(
                {"error": "Internal server error"},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class HighSchoolStudentProfileCreateView(CreateAPIView):
    """
    API endpoint for creating high school student profile data.
    """
    serializer_class = HighSchoolStudentProfileCreateSerializer
    # Remove IsAuthenticated to let our own check control unauthenticated access
    permission_classes = []
    
    def post(self, request, *args, **kwargs):
        # Check if the user is authenticated
        if not request.user or not request.user.is_authenticated:
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
        
        # Get the user and ensure they are a high school student
        user = self.request.user
        if getattr(user, 'user_type', None) != user.HIGH_SCHOOL_STUDENT_TYPE:
            return Response(
                {"error": "Only high school students can update their profile data."},
                status=status.HTTP_403_FORBIDDEN
            )
        
        # Get the user's high school student profile
        try:
            profile = user.high_school_student_profile
        except Exception:
            return Response(
                {"error": "High school student profile not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Initialize serializer with the profile instance and request data
        serializer = self.get_serializer(profile, data=request.data, partial=True)
        
        try:
            # Validate the data
            serializer.is_valid(raise_exception=True)
            
            # Validate ZIP code with Mapbox API if applicable
            if (
                serializer.validated_data.get('geographic_preference_type') == 'zip' and
                serializer.validated_data.get('preferred_zip')
            ):
                zip_code = serializer.validated_data.get('preferred_zip')
                valid_zip = self.validate_zip_with_mapbox(zip_code)
                if not valid_zip:
                    return Response(
                        {"preferred_zip": "Invalid ZIP code. Please enter a valid US ZIP code."},
                        status=status.HTTP_400_BAD_REQUEST
                    )
            
            # Save the profile data
            profile = serializer.save()

            # Run thread only if not in test mode
            if 'test' not in sys.argv:
                # Generate recommendations in the background
                generate_recommendations_in_background(profile)

            # Return the saved data
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except serializers.ValidationError as e:
            return Response(e.detail, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logging.error(f"Unexpected error: {str(e)}")
            return Response(
                {"error": "An unexpected error occurred. Please try again later."},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )
    
    def validate_zip_with_mapbox(self, zip_code):
        """
        Validate a ZIP code using the Mapbox API.
        Returns True if the ZIP code is valid, False otherwise.
        """
        try:
            # First check if we've already validated this ZIP code
            from api.coordinate_storage.models import SharedAPIResult
            try:
                SharedAPIResult.objects.get(query=zip_code)
                return True  # ZIP code exists in our database
            except SharedAPIResult.DoesNotExist:
                pass
            
            # If not, call the Mapbox API
            geocode_url = f"https://api.mapbox.com/search/geocode/v6/forward?postcode={zip_code}&country=united&access_token={settings.MAPBOX_API_KEY}"
            response = requests.get(geocode_url)
            
            if response.status_code == 200:
                data = response.json()
                if data.get('features') and len(data['features']) > 0:
                    # Save the result in the database for future use
                    coordinates = data['features'][0]['geometry']['coordinates']
                    location = (coordinates[1], coordinates[0])
                    SharedAPIResult.objects.create(query=zip_code, result=location)
                    return True
            return False
        except Exception as e:
            logging.error(f"Error validating ZIP code: {str(e)}")
            raise e

class RecommendationsStatusView(APIView):
    """
    API endpoint to check the status of university recommendations for the authenticated user.
    
    GET /api/recommendations/status/
    
    Returns:
        recommendations_status: Current status of recommendations (not_requested, pending, ready)
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]
    
    def get(self, request):
        profile = get_object_or_404(HighSchoolStudentProfile, user=request.user)
        serializer = RecommendationsStatusSerializer({"recommendations_status": profile.recommendations_status})
        return Response(serializer.data)

class ProfileUpdateRecommendationRegenerationView(APIView):
    """
    API endpoint for updating high school student profiles and triggering recommendation regeneration.
    
    This endpoint:
    1. Validates and updates the profile data
    2. Sets the recommendations_status to "pending"
    3. Triggers the recommendation regeneration process
    4. Returns the updated profile with the "pending" status
    
    Rate limited to 3 requests per 6-hour window.
    """
    permission_classes = [IsAuthenticated, IsHighSchoolStudent]
    authentication_classes = [TokenAuthentication]
    throttle_classes = [RecommendationRegenerationRateThrottle]
    
    def initial(self, request, *args, **kwargs):
        if not request.user or not request.user.is_authenticated:
            raise NotAuthenticated(detail="Authentication credentials were not provided.")
        super().initial(request, *args, **kwargs)

    def permission_denied(self, request, message=None, code=None):
        if not request.user or not request.user.is_authenticated:
            raise NotAuthenticated(detail="Authentication credentials were not provided.")
        return super().permission_denied(request, message, code)
    
    def post(self, request, *args, **kwargs):
        # If we reach here, the user is authenticated due to initial()
        # Get the current user's high school student profile
        try:
            profile = request.user.high_school_student_profile
        except HighSchoolStudentProfile.DoesNotExist:
            return Response(
                {"detail": "High school student profile not found."},
                status=status.HTTP_404_NOT_FOUND
            )
        
        # Validate the update data
        serializer = HighSchoolStudentProfileCreateSerializer(
            profile, 
            data=request.data,
            partial=True
        )
        
        if serializer.is_valid():
            # Save the updated profile
            serializer.save()
            
            # Mark the recommendations_status as pending
            profile.recommendations_status = "pending"
            profile.save(update_fields=['recommendations_status'])

            # Generate recommendations in the background
            generate_recommendations_in_background(profile)
            
            # Get the updated data to return in the response
            updated_serializer = HighSchoolStudentProfileCreateSerializer(profile)
            response_data = updated_serializer.data
            response_data['recommendations_status'] = profile.recommendations_status
            
            return Response(
                response_data,
                status=status.HTTP_200_OK
            )
        
        return Response(
            serializer.errors,
            status=status.HTTP_400_BAD_REQUEST
        )

class HighSchoolStudentProfileDetailView(generics.RetrieveAPIView):
    """
    API endpoint for retrieving the authenticated user's high school student profile.
    """
    serializer_class = HighSchoolStudentProfileDetailSerializer
    permission_classes = [IsAuthenticated, IsHighSchoolStudent]
    authentication_classes = [TokenAuthentication]

    def get_object(self):
        """
        Return the profile for the current authenticated user.
        """

        try:
            return HighSchoolStudentProfile.objects.get(user=self.request.user)
        except HighSchoolStudentProfile.DoesNotExist:
            raise NotFound("Profile not found for the authenticated user.")


class RecommendationThrottleStatusView(APIView):
    permission_classes = [IsAuthenticated, IsHighSchoolStudent]
    authentication_classes = [TokenAuthentication]

    def get(self, request):
        throttle = RecommendationRegenerationRateThrottle()
        throttle.view = self
        remaining = throttle.remaining_requests(request)

        return Response({
            "can_trigger": remaining > 0,
            "remaining_requests": remaining
        })