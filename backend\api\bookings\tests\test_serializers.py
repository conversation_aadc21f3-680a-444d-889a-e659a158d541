def test_booking_serializer_with_multiple_trailblazers(self):
    trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    trailblazer3 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    data = {
        'trailblazers': [self.trailblazer.id, trailblazer2.id, trailblazer3.id],
        'start_time': self.start_time,
        'message': "Group session.",
        'number_of_students': 10
    }
    serializer = BookingSerializer(data=data)
    self.assertTrue(serializer.is_valid())
    booking = serializer.save(booked_by=self.booked_by)
    self.assertEqual(booking.trailblazers.count(), 3)

def test_booking_serializer_with_too_many_trailblazers(self):
    trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    trailblazer3 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    trailblazer4 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    data = {
        'trailblazers': [self.trailblazer.id, trailblazer2.id, trailblazer3.id, trailblazer4.id],
        'start_time': self.start_time,
        'message': "Too many trailblazers.",
        'number_of_students': 15
    }
    serializer = BookingSerializer(data=data)
    self.assertFalse(serializer.is_valid())
    self.assertIn('trailblazers', serializer.errors)

def test_booking_serializer_overlapping_with_multiple_trailblazers(self):
    trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    self.booking.save()
    self.booking.trailblazers.add(self.trailblazer, trailblazer2)
    data = {
        'trailblazers': [self.trailblazer.id],
        'start_time': self.start_time,
        'message': "Overlapping booking.",
        'number_of_students': 3
    }
    serializer = BookingSerializer(data=data)
    self.assertFalse(serializer.is_valid())
    self.assertIn('non_field_errors', serializer.errors)
