"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { ArrowRight } from 'lucide-react'
import { getNextOnboardingStep } from '@/lib/utils'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { useToast } from '@/hooks/use-toast'
import { ZipCodeInput } from '@/components/ui/zipcodeInput'
import { Loader2 } from 'lucide-react'
import { OrganizationTagPicker } from '@/components/ui/organizationTagPicker';

// Form schema
const formSchema = z.object({
    schoolName: z.string().min(1, { message: "School name is required" }),
    zipCode: z.string().regex(/^\d{5}$/, { message: "Valid 5-digit zip code is required" }),
    city: z.string().min(1, { message: "City is required" }),
    state: z.string().min(1, { message: "State is required" }),
    organizationTags: z
        .array(
            z.object({
                value: z.number(), // Validate that `value` is a number
                label: z.string(), // Validate that `label` is a string
            })
        )
        .max(3, { message: "You can select up to 3 organizations" }), // Limit to 3 organizations
})

// HighSchoolForm component
const HighSchoolForm = () => {
    const router = useRouter()
    const { toast } = useToast()
    const [isLoading, setIsLoading] = useState(false)
    const { getToken } = useAuth()
    const [pendingCustomTags, setPendingCustomTags] = useState([])

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            schoolName: "",
            zipCode: "",
            city: "",
            state: "",
            organizationTags: []
        }
    })

    const onZipMatch = (result) => {
        form.setValue("city", result.city)
        form.setValue("state", result.state)
    }

    const onZipError = (error) => {
        if (!error) {
            // reset errors
            delete form.formState.errors.zipCode
        } else {
            form.formState.errors.zipCode = { message: error }
        }
    }

    const onSubmit = async (data) => {
        setIsLoading(true);
        const authToken = getToken();

        try {
            // Create custom tags in the backend
            const customTagIds = [];
            for (const tag of pendingCustomTags) {

                // Remove the custom tag ID from the main tags list
                data.organizationTags = data.organizationTags.filter((orgTag) => orgTag.value !== tag.value);

                try {
                    const customTagResponse = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/tags/create/`, {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Token ${authToken}`,
                        },
                        body: JSON.stringify({ label: tag.label }),
                    });

                    if (customTagResponse.ok) {
                        const createdTag = await customTagResponse.json();

                        customTagIds.push(createdTag.id); 
                    } else if (customTagResponse.status === 400) {
                        const errorData = await customTagResponse.json();
                        console.error(`Failed to create custom tag: ${tag.label}`, errorData.error);

                        toast({ variant: 'destructive', description: `${tag.label}, ${errorData.error} `  || 'Failed to create custom tag.' });
                    } else {
                        console.error(`Unexpected error creating custom tag: ${tag.label}`);
                        toast({ variant: 'destructive', description: 'An unexpected error occurred while creating the custom tag.' });
                    }
                } catch (error) {
                    console.error(`Error creating custom tag: ${tag.label}`, error);
                    toast({ variant: 'destructive', description: 'A network error occurred while creating the custom tag.' });
                }
            }


            // Combine existing and custom tag IDs
            const allTagIds = [...data.organizationTags.map((tag) => tag.value), ...customTagIds];

            // Submit the main form data
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`,
                },
                body: JSON.stringify({
                    step: "high-school",
                    high_school_name: data.schoolName,
                    high_school_zip_code: data.zipCode,
                    high_school_city: data.city,
                    high_school_state: data.state,
                    organization_tags: allTagIds, // Use the combined list of tag IDs
                }),
            });

            if (response.ok) {
                toast({ title: 'Success', description: 'High school information uploaded successfully' });
                router.push(getNextOnboardingStep('CollegeStudent', 'high-school'));
            } else {
                const errorData = await response.json();
                toast({ variant: 'destructive', description: errorData.error || 'Failed to upload high school information' });
            }
        } catch (error) {
            toast({ variant: 'destructive', description: 'An unexpected error occurred' });
        } finally {
            setIsLoading(false);
        }
    };

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                <h1 className="text-4xl font-bold mb-14">Where did you attend high school?</h1>
                
                <FormField
                    control={form.control}
                    name="schoolName"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>High School Name</FormLabel>
                            <FormControl>
                                <Input placeholder="Enter school name" {...field} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <FormField
                    control={form.control}
                    name="zipCode"
                    render={({ field }) => (
                        <FormItem>
                            <FormLabel>Zip Code</FormLabel>
                            <FormControl>
                                <ZipCodeInput field={field} form={form} onZipMatch={onZipMatch} onZipError={onZipError} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <div className="flex flex-col sm:flex-row sm:space-x-4">
                    <FormField
                        control={form.control}
                        name="city"
                        render={({ field }) => (
                            <FormItem className="w-full sm:w-1/2">
                                <FormLabel>City</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter city" disabled {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />

                    <FormField
                        control={form.control}
                        name="state"
                        render={({ field }) => (
                            <FormItem className="w-full sm:w-1/2 mt-4 sm:mt-0">
                                <FormLabel>State</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter state" disabled {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>

                <FormField
                    control={form.control}
                    name="organizationTags"
                    render={({ field }) => (
                        <FormItem>
                            <h5 className="font-bold">What organizations have you been part of?</h5>
                            <FormControl>
                                <OrganizationTagPicker field={field} setPendingCustomTags={setPendingCustomTags} />
                            </FormControl>
                            <FormMessage />
                        </FormItem>
                    )}
                />

                <div>
                    <Button 
                        type="submit" 
                        className="w-full sm:w-auto mt-8"
                        disabled={isLoading}
                    >
                        {isLoading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Next <ArrowRight className="ml-2 w-6 h-6" />
                    </Button>
                </div>
            </form>
        </Form>
    )
}

// Main HighSchoolDetails component
const HighSchoolDetails = () => {
    return (
        <OnboardingLayout>
            <div className="space-y-8">
                <div className="w-full">
                    <div className="py-10">
                        <StepProgressDisplay currentStep={1} totalSteps={6} />
                    </div>
                    <div className="w-full md:w-5/6 lg:w-5/6">
                        <HighSchoolForm />
                    </div>
                    <div className="flex flex-col lg:flex-row">
                    </div>
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(HighSchoolDetails)
