from django.db import models
from django.conf import settings
import uuid


class Organization(models.Model):
    """
    Model representing an organization.
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    name = models.CharField(max_length=255)
    total_subscribed_hours =  models.PositiveBigIntegerField(default = 0,  help_text="Total hours the organization has signed up for." )
    per_student_hour_cap = models.PositiveIntegerField(default=0, help_text="Maximum hours each student can book.")
    total_hours_used = models.PositiveIntegerField(default=0, help_text="Total hours used by the organization.")
    zip_code = models.CharField(max_length=10)
    city = models.CharField(max_length=100)
    state = models.Char<PERSON>ield(max_length=100)
    school_district = models.CharField(max_length=255, null=True, blank=True)
    contact_phone = models.Char<PERSON>ield(max_length=20, null=True, blank=True)
    contact_email = models.EmailField(null=True, blank=True)
    registration_number = models.Char<PERSON>ield(max_length=100, unique=True, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    
    administrators = models.ManyToManyField(
        settings.AUTH_USER_MODEL,
        related_name='organizations_managed'
    )

    def save(self, *args, **kwargs):
    # Standardize the font of the organization name
        self.name = self.name.title()

        # Keywords to exclude from tag creation
        exclude_keywords = [
            "academy", "school district", "high school", "middle school", "elementary school",
            "charter", "public school", "private school", "primary school", "secondary school",
            "junior high", "senior high", "technical school", "vocational school", "trade school",
            "prep school", "boarding school", "district", "board of education", "institute", "academies"
        ]

        # Check if the organization's name contains any excluded keywords
        if not any(keyword in self.name.lower() for keyword in exclude_keywords):
            # Ensure a corresponding tag is created for the organization
            organization_tag, created = OrganizationTag.objects.get_or_create(name=self.name)
            if not organization_tag.verified:
                organization_tag.verified = True  # Set the tag as verified
                organization_tag.save()

        # Save the organization
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name


class OrganizationTag(models.Model):
    """
    Model representing a company tag for college student profiles.
    """
    name = models.CharField(max_length=255, unique=True)
    verified = models.BooleanField(default=False)  # Indicates if the tag is verified by an admin

    def save(self, *args, **kwargs):
        # Standardize the font of the tag name
        self.name = self.name.title()  # Capitalize each word
        super().save(*args, **kwargs)

    def __str__(self):
        return self.name
