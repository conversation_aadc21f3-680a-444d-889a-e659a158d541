from rest_framework import serializers

from django.conf import settings
from django.contrib.auth.password_validation import validate_password
from django.core.exceptions import ValidationError
from django.contrib.auth import authenticate
from rest_framework.exceptions import ValidationError as DRFValidationError

from api.organizations.models import Organization, OrganizationTag
from api.organizations.serializers import OrganizationSerializer
from api.users.models import User, CollegeStudentProfile, HighSchoolStudentProfile, CounselorAdministratorProfile, Availability


class UserSerializer(serializers.ModelSerializer):
    """
    User serializer
    """
    profile = serializers.SerializerMethodField()
    distance = serializers.FloatField(read_only=True)
    onboarding_steps_completed = serializers.SerializerMethodField()

    class Meta:
        model = User
        fields = (
            'id',
            'first_name',
            'last_name',
            'email',
            'user_type',
            'profile',
            'distance',
            'onboarding_steps_completed',
        )
        read_only_fields = ('id', 'email', 'user_type', 'onboarding_steps_completed')

    def get_profile(self, obj):
        if obj.user_type == User.COLLEGE_STUDENT_TYPE:
            return CollegeStudentProfileSerializer(obj.profile).data
        elif obj.user_type == User.HIGH_SCHOOL_STUDENT_TYPE:
            return HighSchoolStudentProfileSerializer(obj.profile).data
        elif obj.user_type == User.COUNSELOR_ADMINISTRATOR_TYPE:
            return CounselorAdministratorProfileSerializer(obj.profile).data
        return None

    def get_onboarding_steps_completed(self, obj):
        onboarding_progress = getattr(obj, 'onboarding_progress', None)
        if onboarding_progress is None:
            return None
        return onboarding_progress.steps_completed

    def to_representation(self, instance):
        """
        Modify the representation of the object to send first_name and last_name in title case.
        """
        representation = super().to_representation(instance)
        representation['first_name'] = representation['first_name'].title() if representation['first_name'] else ''
        representation['last_name'] = representation['last_name'].title() if representation['last_name'] else ''
        return representation


class CreateUserSerializer(serializers.ModelSerializer):
    user_type = serializers.ChoiceField(choices=['CollegeStudent', 'HighSchoolStudent', 'CounselorAdministrator'], required=False)
    auth_token = serializers.SerializerMethodField()
    terms_accepted = serializers.BooleanField(required=True, write_only=True)

    class Meta:
        model = User
        fields = ('id', 'password', 'first_name', 'last_name', 'email', 'user_type', 'auth_token', 'terms_accepted')
        read_only_fields = ('id', 'auth_token')

    def validate_terms_accepted(self, value):
        """
        Validate that terms and conditions are accepted.
        """
        if not value:
            raise serializers.ValidationError("You must accept the terms and conditions to register.")
        return value

    def create(self, validated_data):
        password = validated_data.pop('password', None)
        # Remove terms_accepted as it's not a model field
        validated_data.pop('terms_accepted', None)
        
        validated_data['username'] = validated_data['email']

        user = User.objects.create(**validated_data)

        try:
            validate_password(password, user=user)
        except ValidationError as e:
            raise DRFValidationError({'password': e.messages})

        user.set_password(password)
        user.save()
        
        return user

    def get_auth_token(self, obj):
        return obj.auth_token.key


class CollegeStudentProfileSerializer(serializers.ModelSerializer):
    organization_tags = serializers.PrimaryKeyRelatedField(
        many=True,  # Indicates this is a ManyToManyField
        queryset=OrganizationTag.objects.all()  # Queryset for validation
    )

    class Meta:
        model = CollegeStudentProfile
        fields = [
            'bio',
            'university',
            'graduation_year',
            'paypal_email',
            'interests',
            'high_school_name',
            'high_school_zip_code',
            'high_school_city',
            'high_school_state',
            'college_major',
            'university_tags',
            'is_email_verified',
            'is_waitlisted',
            'organization_tags',
            'avatar',
        ]


class CounselorAdministratorProfileSerializer(serializers.ModelSerializer):
    organization = OrganizationSerializer()

    class Meta:
        model = CounselorAdministratorProfile
        fields = ['position', 'organization', 'is_email_verified', 'avatar', 'verified_at']


class HighSchoolStudentProfileSerializer(serializers.ModelSerializer):
    organization = OrganizationSerializer()
    
    class Meta:
        model = HighSchoolStudentProfile
        fields = [
            'grade_level', 'organization', 'is_email_verified', 'avatar', 
            'unweighted_gpa', 'weighted_gpa', 'sat_math_score', 'sat_reading_score', 
            'act_score', 'high_school_name', 'graduation_year', 'intended_majors', 
            'extracurriculars', 'interests', 'college_type_preferences', 
            'location_type_preferences', 'geographic_preference_type', 'preferred_states', 
            'preferred_zip', 'preferred_radius', 'gender', 'ethnicity', 'household_income', 
            'financial_aid_need'
        ]


class CollegeStudentProfileUpdateSerializer(serializers.ModelSerializer):
    organization_tags = serializers.PrimaryKeyRelatedField(
        many=True,  # Indicates this is a ManyToManyField
        queryset=OrganizationTag.objects.all()  # Queryset for validation
    )

    class Meta:
        model = CollegeStudentProfile
        fields = [
            'avatar',
            'university',
            'graduation_year',
            'interests',
            'college_major',
            'university_tags',
            'bio',
            'paypal_email',
            'is_waitlisted',
            'organization_tags',
        ]


class HighSchoolStudentProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = HighSchoolStudentProfile
        fields = [
            'avatar', 'grade_level', 'unweighted_gpa', 'weighted_gpa', 
            'sat_math_score', 'sat_reading_score', 'act_score', 'high_school_name', 
            'graduation_year', 'intended_majors', 'extracurriculars', 'interests', 
            'college_type_preferences', 'location_type_preferences', 
            'geographic_preference_type', 'preferred_states', 'preferred_zip', 
            'preferred_radius', 'gender', 'ethnicity', 'household_income', 
            'financial_aid_need'
        ]


class CounselorAdministratorProfileUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = CounselorAdministratorProfile
        fields = ['avatar', 'position']


class BaseSignUpSerializer(serializers.ModelSerializer):
    password = serializers.CharField(write_only=True, required=True, validators=[validate_password])
    auth_token = serializers.SerializerMethodField()
    
    class Meta:
        model = User
        fields = ['email', 'password', 'auth_token']

    def get_auth_token(self, obj):
        return obj.auth_token.key


class CollegeStudentSignUpSerializer(BaseSignUpSerializer):
    
    def validate_email(self, value):
        if not value.endswith('.edu'):
            raise serializers.ValidationError("College students must sign up with a .edu email address.")
        return value
    
    def create(self, validated_data):
        user = User.objects.create(
            username=validated_data['email'],
            email=validated_data['email'],
            user_type=User.COLLEGE_STUDENT_TYPE,
        )
        user.set_password(validated_data['password'])
        user.save()
        return user


class CounselorAdministratorSignUpSerializer(BaseSignUpSerializer):
    
    def create(self, validated_data):
        user = User.objects.create(
            username=validated_data['email'],
            email=validated_data['email'],
            user_type=User.COUNSELOR_ADMINISTRATOR_TYPE,
        )
        user.set_password(validated_data['password'])
        user.save()
        return user


class HighSchoolStudentSignUpSerializer(BaseSignUpSerializer):
    organization = serializers.PrimaryKeyRelatedField(queryset=Organization.objects.all(), write_only=True)
    
    class Meta:
        model = User
        fields = ['email', 'password', 'organization', 'auth_token']

    def create(self, validated_data):
        user = User.objects.create(
            username=validated_data['email'],
            email=validated_data['email'],
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE,
        )
        user.set_password(validated_data['password'])
        user.save()
        # Add Organization to HighSchoolStudentProfile
        profile, _ = HighSchoolStudentProfile.objects.get_or_create(
            user=user,
        )
        profile.organization = validated_data['organization']
        profile.save()
        return user


class TimeRangeSerializer(serializers.Serializer):
    start_time = serializers.TimeField()
    end_time = serializers.TimeField()

    def validate(self, attrs):
        if attrs['start_time'] >= attrs['end_time']:
            raise serializers.ValidationError("Start time must be before end time.")
        return attrs


class AvailabilitySerializer(serializers.ModelSerializer):
    monday_time_ranges = TimeRangeSerializer(many=True, required=False)
    tuesday_time_ranges = TimeRangeSerializer(many=True, required=False)
    wednesday_time_ranges = TimeRangeSerializer(many=True, required=False)
    thursday_time_ranges = TimeRangeSerializer(many=True, required=False)
    friday_time_ranges = TimeRangeSerializer(many=True, required=False)
    saturday_time_ranges = TimeRangeSerializer(many=True, required=False)
    sunday_time_ranges = TimeRangeSerializer(many=True, required=False)

    class Meta:
        model = Availability
        fields = [
            'id', 'user', 'time_zone',
            'monday_available', 'tuesday_available', 'wednesday_available',
            'thursday_available', 'friday_available', 'saturday_available', 'sunday_available',
            'monday_time_ranges', 'tuesday_time_ranges', 'wednesday_time_ranges',
            'thursday_time_ranges', 'friday_time_ranges', 'saturday_time_ranges', 'sunday_time_ranges',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['id', 'user', 'created_at', 'updated_at']

    def validate(self, attrs):
        # Invoke model's clean method for overlapping validation
        availability = Availability(**attrs)
        availability.clean()

        # Format time ranges as strings
        for key in attrs.keys():
            if not key.endswith('_time_ranges'):
                continue

            time_ranges = attrs.get(key)
            for time_range in time_ranges:
                time_range['start_time'] = time_range['start_time'].strftime("%H:%M")
                time_range['end_time'] = time_range['end_time'].strftime("%H:%M")

            attrs[key] = time_ranges

        return attrs

    def create(self, validated_data):
        return Availability.objects.create(**validated_data)

    def update(self, instance, validated_data):
        for attr, value in validated_data.items():
            setattr(instance, attr, value)
        instance.save()
        return instance


class HighSchoolStudentOnboardingSerializer(serializers.Serializer):
    step = serializers.CharField(max_length=30)
    first_name = serializers.CharField(max_length=30, required=False)
    last_name = serializers.CharField(max_length=30, required=False)
    grade_level = serializers.IntegerField(required=False)
    avatar = serializers.ImageField(required=False)


class UserUpdateSerializer(serializers.ModelSerializer):
    # Read-only fields based on user type
    first_name = serializers.CharField(read_only=True)
    last_name = serializers.CharField(read_only=True)
    organization = serializers.PrimaryKeyRelatedField(read_only=True)

    class Meta:
        model = User
        fields = ['first_name', 'last_name', 'organization']

    def validate(self, attrs):
        user_type = self.instance.user_type
        read_only_fields = []
        if user_type == User.COLLEGE_STUDENT_TYPE:
            read_only_fields = ['first_name', 'last_name', 'high_school_name', 'high_school_zip_code', 'high_school_city', 'high_school_state']
        elif user_type == User.HIGH_SCHOOL_STUDENT_TYPE:
            read_only_fields = ['first_name', 'last_name']
        elif user_type == User.COUNSELOR_ADMINISTRATOR_TYPE:
            read_only_fields = ['first_name', 'last_name', 'organization']

        for field in read_only_fields:
            if field in self.initial_data:
                raise serializers.ValidationError({field: "This field is read-only and cannot be updated."})

        return attrs

    def update(self, instance, validated_data):
        user_type = instance.user_type
        if user_type == User.COLLEGE_STUDENT_TYPE:
            profile_serializer = CollegeStudentProfileUpdateSerializer(instance.college_student_profile, data=self.context['request'].data, partial=True)
        elif user_type == User.HIGH_SCHOOL_STUDENT_TYPE:
            profile_serializer = HighSchoolStudentProfileUpdateSerializer(instance.high_school_student_profile, data=self.context['request'].data, partial=True)
        elif user_type == User.COUNSELOR_ADMINISTRATOR_TYPE:
            profile_serializer = CounselorAdministratorProfileUpdateSerializer(instance.counselor_administrator_profile, data=self.context['request'].data, partial=True)
        else:
            raise serializers.ValidationError("Invalid user type.")

        if profile_serializer.is_valid():
            profile_serializer.save()
        else:
            raise serializers.ValidationError(profile_serializer.errors)

        return instance


class CollegeStudentOnboardingSerializer(serializers.Serializer):
    step = serializers.CharField(max_length=30)
    first_name = serializers.CharField(max_length=30, required=False)
    last_name = serializers.CharField(max_length=30, required=False)
    paypal_email = serializers.EmailField(required=False)
    bio = serializers.CharField(max_length=280, required=False)
    high_school_name = serializers.CharField(max_length=255, required=False)
    high_school_zip_code = serializers.CharField(max_length=10, required=False)
    high_school_city = serializers.CharField(max_length=100, required=False)
    high_school_state = serializers.CharField(max_length=100, required=False)
    university = serializers.CharField(max_length=255, required=False)
    graduation_year = serializers.CharField(max_length=255, required=False)
    university_tags = serializers.ListField(child=serializers.CharField(max_length=255), required=False)
    college_major = serializers.CharField(max_length=255, required=False)
    organization_tags = serializers.PrimaryKeyRelatedField(many=True, queryset=OrganizationTag.objects.all(), required=False)
    interests = serializers.ListField(child=serializers.CharField(max_length=255), required=False)
    availability = AvailabilitySerializer(required=False)
    avatar = serializers.ImageField(required=False)


class CounselorAdministratorOnboardingSerializer(serializers.Serializer):
    step = serializers.CharField(max_length=30)
    first_name = serializers.CharField(max_length=30, required=False)
    last_name = serializers.CharField(max_length=30, required=False)
    position = serializers.CharField(max_length=255, required=False)
    organization = serializers.PrimaryKeyRelatedField(queryset=Organization.objects.all(), required=False)
    avatar = serializers.ImageField(required=False)


class LoginSerializer(serializers.Serializer):
    email = serializers.EmailField(required=True)
    password = serializers.CharField(required=True, write_only=True)

    def validate(self, data):
        email = data.get("email")
        password = data.get("password")

        if not email or not password:
            raise serializers.ValidationError("Email and password are required.")

        # Authenticate the user
        user = authenticate(username=email, password=password)
        if user is None:
            raise serializers.ValidationError("Invalid email or password.")

        if not user.is_active:
            raise serializers.ValidationError("This account is inactive.")

        # Attach the user to validated_data
        data['user'] = user
        return data


class HighSchoolStudentProfileCreateSerializer(serializers.ModelSerializer):
    """
    Serializer for creating/updating high school student profile data with validation.
    """
    # Make organization field optional for creation
    organization = serializers.PrimaryKeyRelatedField(read_only=True)
    
    class Meta:
        model = HighSchoolStudentProfile
        fields = [
            'unweighted_gpa', 'weighted_gpa', 'sat_math_score', 'sat_reading_score', 
            'act_score', 'high_school_name', 'graduation_year', 'intended_majors', 
            'extracurriculars', 'interests', 'college_type_preferences', 
            'location_type_preferences', 'geographic_preference_type', 'preferred_states', 
            'preferred_zip', 'preferred_radius', 'country', 'current_zip_code', 'gender', 'ethnicity', 'household_income',
            'financial_aid_need', 'organization'
        ]
    
    def validate_unweighted_gpa(self, value):
        """Validate unweighted GPA is between 0.0 and 4.0."""
        if value is not None and (value < 0.0 or value > 4.0):
            raise serializers.ValidationError("Unweighted GPA must be between 0.0 and 4.0.")
        return value
    
    def validate_weighted_gpa(self, value):
        """Validate weighted GPA is between 0.0 and 5.0."""
        if value is not None and (value < 0.0 or value > 5.0):
            raise serializers.ValidationError("Weighted GPA must be between 0.0 and 5.0.")
        return value
    
    def validate_sat_math_score(self, value):
        """Validate SAT math score is between 200 and 800."""
        if value is not None and (value < 200 or value > 800):
            raise serializers.ValidationError("SAT math score must be between 200 and 800.")
        return value
    
    def validate_sat_reading_score(self, value):
        """Validate SAT reading score is between 200 and 800."""
        if value is not None and (value < 200 or value > 800):
            raise serializers.ValidationError("SAT reading score must be between 200 and 800.")
        return value
    
    def validate_act_score(self, value):
        """Validate ACT score is between 1 and 36."""
        if value is not None and (value < 1 or value > 36):
            raise serializers.ValidationError("ACT score must be between 1 and 36.")
        return value
    
    def validate(self, data):
        """Validate related fields together."""
        geographic_preference_type = data.get('geographic_preference_type')
        preferred_states = data.get('preferred_states')
        preferred_zip = data.get('preferred_zip')
        
        # Validate geographic preferences
        if geographic_preference_type == 'states':
            # Validate preferred_states if geographic_preference_type is STATES
            if preferred_states:
                # List of valid US state codes
                valid_states = [
                    'AL', 'AK', 'AZ', 'AR', 'CA', 'CO', 'CT', 'DE', 'FL', 'GA', 
                    'HI', 'ID', 'IL', 'IN', 'IA', 'KS', 'KY', 'LA', 'ME', 'MD', 
                    'MA', 'MI', 'MN', 'MS', 'MO', 'MT', 'NE', 'NV', 'NH', 'NJ', 
                    'NM', 'NY', 'NC', 'ND', 'OH', 'OK', 'OR', 'PA', 'RI', 'SC', 
                    'SD', 'TN', 'TX', 'UT', 'VT', 'VA', 'WA', 'WV', 'WI', 'WY',
                    'DC', 'PR', 'VI', 'GU', 'MP', 'AS'
                ]
                invalid_states = [state for state in preferred_states if state not in valid_states]
                if invalid_states:
                    raise serializers.ValidationError({
                        'preferred_states': f"Invalid state codes: {', '.join(invalid_states)}"
                    })
        
        elif geographic_preference_type == 'zip':
            # Validate preferred_zip if geographic_preference_type is ZIP
            if not preferred_zip:
                raise serializers.ValidationError({
                    'preferred_zip': "ZIP code is required when geographic preference type is ZIP."
                })
            
            # Validate ZIP code format (5-digit or 5+4)
            import re
            if not re.match(r'^\d{5}(-\d{4})?$', preferred_zip):
                raise serializers.ValidationError({
                    'preferred_zip': "ZIP code must be in 5-digit (12345) or 5+4 (12345-6789) format."
                })
            
            # Validation of ZIP code existence could be done here using Mapbox API
            # but for simplicity, we'll skip the actual API call in the validation
        
        # Validate preferred_radius is one of the predefined choices
        preferred_radius = data.get('preferred_radius')
        if preferred_radius and preferred_radius not in [choice[0] for choice in HighSchoolStudentProfile.PREFERRED_RADIUS_CHOICES]:
            raise serializers.ValidationError({
                'preferred_radius': f"Invalid radius. Must be one of: {', '.join([choice[0] for choice in HighSchoolStudentProfile.PREFERRED_RADIUS_CHOICES])}"
            })
        
        return data


class RecommendationsStatusSerializer(serializers.Serializer):
    """
    Serializer for returning recommendations status.
    """
    recommendations_status = serializers.CharField(read_only=True)


class HighSchoolStudentProfileDetailSerializer(HighSchoolStudentProfileCreateSerializer):
    """
    Serializer for retrieving high school student profile details.
    Returns data in the same structure as HighSchoolStudentProfileCreateSerializer accepts.
    """
    class Meta(HighSchoolStudentProfileCreateSerializer.Meta):
        # Inherit fields from HighSchoolStudentProfileCreateSerializer
        pass