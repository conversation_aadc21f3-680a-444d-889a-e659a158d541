"use client"

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { Label } from '@radix-ui/react-label'
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { getNextOnboardingStep } from '@/lib/utils'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { ArrowRight } from 'lucide-react'
import { Loader2 } from 'lucide-react'
import { OrganizationCombobox } from "@/components/ui/organizationPicker"
import { useForm } from "react-hook-form"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"

// OrganizationForm component with enhanced dropdown and button behavior
const OrganizationForm = () => {
    const [organizations, setOrganizations] = useState([])
    const [loadingOrganizations, setLoadingOrganizations] = useState(true)
    const [loading, setLoading] = useState(false)
    const [error, setError] = useState("")
    const router = useRouter()
    const { getToken } = useAuth()

    const formSchema = z.object({
        organization: z.string().min(1, "Please select your organization"),
    })
    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            organization: "",
        }
    })


    useEffect(() => {
        const fetchOrganizations = async () => {
            try {
                const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/organizations/`, {
                    headers: {
                        'Authorization': `Token ${getToken()}`
                    }
                })
                if (!response.ok) {
                    throw new Error('Failed to fetch organizations')
                }
                const data = await response.json()
                setOrganizations(data.results)
                setLoadingOrganizations(false)
            } catch (err) {
                console.error(err)
                setError('Unable to load organizations. Please try again later.')
                setLoadingOrganizations(false)
            }
        }
        fetchOrganizations()
    }, [])

    const handleSubmit = (formData) => {
        setLoading(true)
        const organization = formData.organization
        if (organization) {

            // Send request with organization data
            fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/counselors/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${getToken()}`
                },
                body: JSON.stringify({
                    step: "organization",
                    organization: organization,
                })
            })
            .then(async response => {
                if (response.ok) {
                    router.push(getNextOnboardingStep('CounselorAdministrator', 'organization'))
                } else {
                    const errorData = await response.json()
                    setError(errorData.organization ? errorData.organization[0] : 'An unexpected error occurred')
                    setLoading(false)
                }
            })
            .catch(err => {
                console.error('Error submitting organization:', err)
                setError('Unable to submit organization. Please try again later.')
                setLoading(false)
            })
        }
    }

    return (
        <Form {...form}>
            <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
                <h1 className="text-4xl font-bold text-gray-800" id="question">
                    Where do you work?
                </h1>
                <div className="space-y-2 w-full 4xl:w-1/2">
                    <FormField
                        control={form.control}
                        name="organization"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>High School or Organization Name</FormLabel>
                                <FormControl>
                                    <OrganizationCombobox
                                        field={field}
                                        form={form}
                                        organizations={organizations}
                                        loading={loadingOrganizations}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </div>
                <Button
                    type="submit"
                    disabled={loading}
                    className="px-6 text-md mt-8"
                >
                    {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}
                    Next
                    <ArrowRight className="ml-2 w-6 h-6" />
                </Button>
            </form>
        </Form>
    )
}

// Main OrganizationSelection component
const OrganizationSelection = () => {
    return (
        <OnboardingLayout>
            <div className="space-y-8" id="form-section">
                <div className="w-full">
                    <div className="py-10">
                        <StepProgressDisplay currentStep={1} totalSteps={2} />
                    </div>
                    <OrganizationForm />    
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(OrganizationSelection)
