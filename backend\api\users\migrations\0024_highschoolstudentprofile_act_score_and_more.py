# Generated by Django 4.2.13 on 2025-06-03 01:19

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0003_organization_per_student_hour_cap_and_more'),
        ('users', '0023_highschoolstudentprofile_hours_used'),
    ]

    operations = [
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='act_score',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='college_type_preferences',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='ethnicity',
            field=models.CharField(blank=True, choices=[('Hispanic or Latine', 'Hispanic or Latine'), ('Black or African American', 'Black or African American'), ('Middle Eastern or North African', 'Middle Eastern or North African'), ('Multiracial', 'Multiracial'), ('Native American or Alaska Native', 'Native American or Alaska Native'), ('Native Hawaiian or Pacific Islander', 'Native Hawaiian or Pacific Islander'), ('Other', 'Other'), ('White', 'White')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='extracurriculars',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='financial_aid_need',
            field=models.CharField(blank=True, choices=[('High need', 'High need'), ('Medium need', 'Medium need'), ('Low need', 'Low need')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='gender',
            field=models.CharField(blank=True, choices=[('Female', 'Female'), ('Male', 'Male'), ('Non-binary', 'Non-binary'), ('Other', 'Other')], max_length=20, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='geographic_preference_type',
            field=models.CharField(choices=[('STATES', 'States'), ('ZIP', 'ZIP Code')], default='STATES', max_length=20),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='graduation_year',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='high_school_name',
            field=models.CharField(blank=True, max_length=255, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='household_income',
            field=models.CharField(blank=True, choices=[('Under $30,000', 'Under $30,000'), ('$30,001-$48,000', '$30,001-$48,000'), ('$48,001-$75,000', '$48,001-$75,000'), ('$75,001-$100,000', '$75,001-$100,000'), ('$100,001+', '$100,001+')], max_length=50, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='intended_majors',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='interests',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='location_type_preferences',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='preferred_radius',
            field=models.IntegerField(blank=True, help_text='Radius in miles from preferred ZIP code', null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='preferred_states',
            field=models.JSONField(default=list),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='preferred_zip',
            field=models.CharField(blank=True, max_length=10, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='sat_math_score',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='sat_reading_score',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='unweighted_gpa',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='weighted_gpa',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='grade_level',
            field=models.IntegerField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='hours_used',
            field=models.IntegerField(default=0),
        ),
        migrations.AlterField(
            model_name='highschoolstudentprofile',
            name='organization',
            field=models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='high_school_student_profiles', to='organizations.organization'),
        ),
    ]
