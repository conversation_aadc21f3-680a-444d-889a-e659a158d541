<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="utf-8"/>
    <meta content="width=device-width, initial-scale=1.0" name="viewport"/>
    <title>Trailblazer Session Request</title>
    <style>
        body {
            background-color: #f8faf6;
            font-family: Arial, sans-serif;
            padding: 16px;
        }
        .container {
            background-color: #ffffff;
            border-radius: 8px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            max-width: 600px;
            margin: 0 auto;
            padding: 24px;
        }
        .header {
            text-align: center;
            margin-bottom: 24px;
        }
        .logo {
            height: 32px;
            margin-bottom: 12px; /* Ensures the badge is below the image */
        }
        .badge {
            background-color: #dfdede;
            color: #6f6f6f;
            font-size: 14px;
            font-weight: bold;
            padding: 8px 12px;
            border-radius: 16px;
            display: inline-block; /* Prevents stretching */
            margin-top: 8px; /* Space between image and badge */
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
        }
        .details {
            margin-bottom: 16px;
        }
        .details span {
            font-weight: bold;
        }
        .message-label {
            font-weight: bold;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <div class="container">
        <header class="header">
            <img src="{{ logo_path }}" alt="Trailblazer Logo" class="logo"/>
        </header>
        <div>
            <div class="badge">Pending</div>
            <h1 class="title">Trailblazer Session Request</h1>
            <p class="details">
                <span>From:</span> {{ session_with }} <br/>
                <span>Date:</span> {{ session_date }} <br/>
                <span>Time:</span> {{ session_time }} <br/>
                <span>Type:</span> {{ session_type }}
            </p>
            <p class="message-label">Requester said</p>
            <p>{{ message }}</p>
        </div>
    </div>
</body>
</html>