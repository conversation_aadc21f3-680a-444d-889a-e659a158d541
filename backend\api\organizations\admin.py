from django.contrib import admin
from .models import Organization, OrganizationTag


@admin.register(Organization)
class OrganizationAdmin(admin.ModelAdmin):
    """
    Admin interface for Organization model.
    """
    list_display = ('name', 'city', 'state', 'zip_code', 'registration_number', 'created_at', 'updated_at')
    search_fields = ('name', 'city', 'state', 'registration_number', 'zip_code', 'contact_email', 'contact_phone')
    list_filter = ('state', 'city')
    filter_horizontal = ('administrators',)
    ordering = ('name', 'created_at', 'updated_at')  # Enables sorting by name and timestamps

@admin.register(OrganizationTag)
class OrganizationTagAdmin(admin.ModelAdmin):
    list_display = ('name', 'verified')
    list_filter = ('verified',)
    search_fields = ('name',)
    actions = ['verify_tags']

    def verify_tags(self, request, queryset):
        count = queryset.update(verified=True)