from django.db import models
from django.utils import timezone
from datetime import timedelta
from django.conf import settings


class EmailVerificationCode(models.Model):
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='email_verifications')
    code = models.CharField(max_length=6)
    created_at = models.DateTimeField(auto_now_add=True)

    def is_expired(self):
        if timezone.now() > self.created_at + timedelta(minutes=30):
            self.delete()  # Automatically delete the expired code
            return True
        return False

    def __str__(self):
        return f"{self.user.email} - {self.code} - {'Expired' if self.is_expired() else 'Valid'}"
