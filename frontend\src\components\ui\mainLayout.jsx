"use client"

import { useState } from 'react'
import { cn } from "@/lib/utils";
import { Header } from "@/components/ui/header";
import { Sidebar } from "@/components/ui/sidebar";
import {
    Sheet,
    SheetContent,
    SheetTitle,
  } from "@/components/ui/sheet"

// MainLayout component
export const MainLayout = ({ user, displaySidebarMenu, children }) => {
    const [isMobileSidebarOpen, setMobileSidebarOpen] = useState(false);

    return (
        <div className="min-h-screen bg-gray-50">
            {/* Header */}
            <Header
                displaySidebarMenu={displaySidebarMenu}
                isMobileSidebarOpen={isMobileSidebarOpen}
                setMobileSidebarOpen={setMobileSidebarOpen}
            />

            {/* Sidebar for mobile (Sheet) */}
            <div className="block md:hidden">
                <Sheet open={isMobileSidebarOpen} onOpenChange={setMobileSidebarOpen}>
                    <SheetContent side="left" className="bg-white flex flex-col pt-16">
                        <SheetTitle className="hidden">Menu</SheetTitle>
                        {isMobileSidebarOpen && <Sidebar user={user} />}
                    </SheetContent>
                </Sheet>
            </div>

            {/* Sidebar for large screens */}
            {displaySidebarMenu && (
                <div className="hidden md:block">
                    <Sidebar user={user} />
                </div>
            )}

            {/* Main content */}
            <main
                className={cn(
                    "pt-4 px-4 md:pt-8 md:px-12",
                    {
                      "md:ml-80": displaySidebarMenu,
                    }
                  )}
            >
                <div className="mx-auto mt-4">
                    {children}
                </div>
            </main>
        </div>
    );
};
