import { useState } from 'react'
import { Button } from "@/components/ui/button"
import { <PERSON>over, <PERSON>over<PERSON>ontent, PopoverTrigger } from "@/components/ui/popover"
import { Command, CommandEmpty, CommandGroup, CommandInput, CommandItem, CommandList } from "@/components/ui/command"
import { Check, ChevronsUpDown, ArrowRight } from 'lucide-react'
import { cn } from "@/lib/utils"
import { rankSearchAlphabetically } from "@/lib/utils"

// Mock data for majors (replace with actual data or API call)
export const majors = [
    { value: "Accounting" },
    { value: "Actuarial Sciences" },
    { value: "Advertising & Public Relations" },
    { value: "Aerospace & Aeronautical Engineering" },
    { value: "African & African-American Studies" },
    { value: "Agricultural Economics" },
    { value: "Agriculture" },
    { value: "Agriculture Production & Management" },
    { value: "Animal Sciences" },
    { value: "Anthropology" },
    { value: "Applied Mathematics" },
    { value: "Arabic Language & Literature" },
    { value: "Archaeology" },
    { value: "Architectural Engineering" },
    { value: "Architecture" },
    { value: "Art & Art History" },
    { value: "Astronomy & Astrophysics" },
    { value: "Atmospheric Sciences & Meteorology" },
    { value: "Biochemistry" },
    { value: "Biological Engineering" },
    { value: "Biology" },
    { value: "Biomedical Engineering" },
    { value: "Botany" },
    { value: "Business Economics" },
    { value: "Business Management & Administration" },
    { value: "Chemical Engineering" },
    { value: "Chemistry" },
    { value: "Chinese Language & Literature" },
    { value: "Civil Engineering" },
    { value: "Clinical Psychology" },
    { value: "Cognitive Science & Biopsychology" },
    { value: "Commercial Art & Graphic Design" },
    { value: "Communication Technologies" },
    { value: "Communications" },
    { value: "Community & Public Health" },
    { value: "Comparative Language & Literature" },
    { value: "Computer & Information Systems" },
    { value: "Computer Engineering" },
    { value: "Computer Networking & Telecommunications" },
    { value: "Computer Programming & Data Processing" },
    { value: "Computer Science" },
    { value: "Construction Services" },
    { value: "Cosmetology Services" },
    { value: "Counseling Psychology" },
    { value: "Criminal Justice" },
    { value: "Criminology" },
    { value: "Culinary Arts" },
    { value: "Cybersecurity & Network Security" },
    { value: "Drama & Theater Arts" },
    { value: "Early Childhood Education" },
    { value: "East Asian Studies" },
    { value: "Ecology" },
    { value: "Economics" },
    { value: "Education" },
    { value: "Educational Administration & Supervision" },
    { value: "Educational Psychology" },
    { value: "Electrical Engineering" },
    { value: "Elementary Education" },
    { value: "Engineering" },
    { value: "Engineering Technologies" },
    { value: "English Language & Literature" },
    { value: "Environmental Engineering" },
    { value: "Environmental Sciences" },
    { value: "Ethnic & Civilization Studies" },
    { value: "European History" },
    { value: "Film, Video, & Photographic Arts" },
    { value: "Finance" },
    { value: "Fine Arts" },
    { value: "Folklore & Mythology" },
    { value: "Forestry" },
    { value: "French Language & Literature" },
    { value: "General Medical & Health Services" },
    { value: "Genetics" },
    { value: "Geography" },
    { value: "Geological & Geophysical Engineering" },
    { value: "Geology & Earth Sciences" },
    { value: "Geosciences" },
    { value: "German Language & Literature" },
    { value: "Government & Political Sciences" },
    { value: "Health & Medical Administration" },
    { value: "Hispanic & Latin American Studies" },
    { value: "History" },
    { value: "Hospitality Management" },
    { value: "Human Development & Family Studies" },
    { value: "Human Resources & Personnel Management" },
    { value: "Human Services & Community Organization" },
    { value: "Humanities" },
    { value: "Industrial & Manufacturing Engineering" },
    { value: "Industrial & Organizational Psychology" },
    { value: "Intercultural & International Studies" },
    { value: "Interior Design" },
    { value: "International Business" },
    { value: "International Relations" },
    { value: "Italian Language & Literature" },
    { value: "Japanese Language & Literature" },
    { value: "Journalism" },
    { value: "Kinesiology & Exercise Science" },
    { value: "Korean Language & Literature" },
    { value: "Latin Language & Literature" },
    { value: "Liberal Arts" },
    { value: "Linguistics" },
    { value: "Marketing & Marketing Management" },
    { value: "Materials Engineering" },
    { value: "Materials Science" },
    { value: "Mathematics" },
    { value: "Mathematics Teacher Education" },
    { value: "Mechanical Engineering" },
    { value: "Medical Assisting Services" },
    { value: "Medical Technologies" },
    { value: "Metallurgical Engineering" },
    { value: "Microbiology" },
    { value: "Military Technologies" },
    { value: "Mining & Mineral Engineering" },
    { value: "Molecular & Cellular Biology" },
    { value: "Music" },
    { value: "Native American Languages & Linguistics" },
    { value: "Native American Studies" },
    { value: "Natural Resources Management" },
    { value: "Naval Architecture & Marine Engineering" },
    { value: "Near & Middle Eastern Studies" },
    { value: "Neuroscience" },
    { value: "Nuclear Engineering" },
    { value: "Nursing" },
    { value: "Nutrition & Food Sciences" },
    { value: "Oceanography" },
    { value: "Operations, Logistics, & E-Commerce" },
    { value: "Petroleum Engineering" },
    { value: "Pharmacology" },
    { value: "Pharmacy, Pharmaceutical Sciences, & Administration" },
    { value: "Philosophy" },
    { value: "Physical & Health Education Teaching" },
    { value: "Physical Sciences" },
    { value: "Physics" },
    { value: "Physiology" },
    { value: "Plant Science & Agronomy" },
    { value: "Portuguese Language & Literature" },
    { value: "Pre-Law & Legal Studies" },
    { value: "Psychology" },
    { value: "Public Administration" },
    { value: "Public Policy" },
    { value: "Russian Language & Literature" },
    { value: "School Student Counseling" },
    { value: "Science & Computer Teacher Education" },
    { value: "Secondary Teacher Education" },
    { value: "Slavic Language & Literature" },
    { value: "Social Psychology" },
    { value: "Social Science Or History Teacher Education" },
    { value: "Social Sciences" },
    { value: "Social Work & Services" },
    { value: "Sociology" },
    { value: "South Asian Studies" },
    { value: "Spanish Language & Literature" },
    { value: "Special Needs Education" },
    { value: "Statistics" },
    { value: "Studio Arts" },
    { value: "Theology & Religious Studies" },
    { value: "Transportation Sciences & Technologies" },
    { value: "Treatment Therapy Professions" },
    { value: "United States History" },
    { value: "Urban Studies" },
    { value: "Visual & Performing Arts" },
    { value: "Women's, Gender, & Sexuality Studies" },
    { value: "Writing, Composition, & Rhetoric" },
    { value: "Zoology" },
];

// MajorInput component
export const MajorSelect = ({ field, form }) => {
    const [open, setOpen] = useState(false)
    const [filteredMajors, setFilteredMajors] = useState(majors)

    return (
        <Popover open={open} onOpenChange={setOpen} className="w-full">
            <PopoverTrigger asChild>
                <Button
                    variant="outline"
                    role="combobox"
                    aria-expanded={open}
                    className="w-full justify-between bg-white text-left py-6 px-4"
                >
                    {field.value
                        ? majors.find((major) => major.value === field.value)?.value || field.value
                        : "Select or enter your major..."}
                    <ChevronsUpDown className="ml-2 h-4 w-4 shrink-0 opacity-50" />
                </Button>
            </PopoverTrigger>
            <PopoverContent align="start">
                <Command
                    // Workaround until cmdk library fixes the problem of sorting after deleting keys: https://github.com/pacocoursey/cmdk/issues/264
                    filter={rankSearchAlphabetically}
                >
                    <CommandInput placeholder="Search framework..." />
                    <CommandList>
                        <CommandEmpty>No major found.</CommandEmpty>
                        <CommandGroup>
                            {filteredMajors.map((major) => (
                                <CommandItem
                                    key={major.value}
                                    onSelect={() => {
                                        field.onChange(major.value)
                                        setOpen(false)
                                        form.trigger("major")
                                    }}
                                >
                                    <Check
                                        className={cn(
                                            "mr-2 h-4 w-4 shrink-0",
                                            field.value === major.value ? "opacity-100" : "opacity-0"
                                        )}
                                    />
                                    {major.value}
                                </CommandItem>
                            ))}
                        </CommandGroup>
                    </CommandList>
                </Command>
            </PopoverContent>
        </Popover>
    )
}