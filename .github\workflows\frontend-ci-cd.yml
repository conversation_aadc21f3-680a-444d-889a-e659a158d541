name: Frontend CI/CD Pipeline

on:
  push:
    branches:
      - main
      - 'feature/*'  # Runs CI on feature branches
  pull_request:
    branches:
      - main  # Runs CI on PRs to main

jobs:
  ci:
    name: Build Frontend 
    runs-on: ubuntu-latest

    steps:
      - name: Checkout Code
        uses: actions/checkout@v4

      ## 🔹 Setup Node.js (Frontend)
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 18  # Ensure this matches your Next.js version

      - name: Install Frontend Dependencies
        working-directory: ./frontend
        run: npm ci  # Install dependencies

      - name: Build Frontend
        working-directory: ./frontend
        run: npm run build  # Build Next.js app
