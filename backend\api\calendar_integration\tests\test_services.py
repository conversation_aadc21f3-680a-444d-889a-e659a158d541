import json
from datetime import datetime, timedelta
from unittest.mock import patch, MagicMock
from googleapiclient.errors import HttpError
from tenacity import RetryError

from django.test import TestCase
from django.utils import timezone
from django.contrib.auth import get_user_model
from django.conf import settings

from api.calendar_integration.models import MasterGoogleCredentials, CalendarEvent
from api.calendar_integration.services import CalendarService
from api.bookings.models import Booking, TrailblazerBookingStatus

User = get_user_model()


class TestCalendarService(TestCase):
    def setUp(self):
        # Set up the master credentials
        self.master_creds = MasterGoogleCredentials.objects.create(
            token='test_token',
            refresh_token='test_refresh',
            token_uri='https://oauth2.googleapis.com/token',
            client_id='test_client_id',
            client_secret='test_secret',
            scopes=json.dumps(['https://www.googleapis.com/auth/calendar']),
            expiry=timezone.now() + timedelta(hours=1)
        )
        # Create users
        self.booked_by_user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.trailblazer1 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        self.trailblazer2 = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123'
        )
        # Create a Booking instance
        self.booking = Booking.objects.create(
            booked_by=self.booked_by_user,
            start_time=timezone.now() + timedelta(days=3),
            end_time=timezone.now() + timedelta(days=3, hours=1),
            message='Test Booking',
            number_of_students=10,
            is_available=False,
        )
        # Add trailblazers to booking
        self.booking.add_trailblazer(self.trailblazer1, status='pending')
        self.booking.add_trailblazer(self.trailblazer2, status='pending')
        
        self.service = CalendarService()

    @patch('api.calendar_integration.services.build')
    def test_create_individual_event_success(self, mock_build):
        mock_service = MagicMock()
        mock_build.return_value = mock_service

        # Set up the return value for execute()
        mock_service.events.return_value.insert.return_value.execute.return_value = {'id': 'test_event_id'}

        start_time = self.booking.start_time
        end_time = self.booking.end_time
        user = self.trailblazer1

        event = self.service.create_individual_event(
            summary='Test Event',
            description='Test Description',
            start_time=start_time,
            end_time=end_time,
            user=user
        )

        self.assertEqual(event['id'], 'test_event_id')

        # Check that insert was called once with the expected arguments
        mock_service.events.return_value.insert.assert_called_once_with(
            calendarId=settings.GOOGLE_CALENDAR_ID,
            body={
                'summary': 'Test Event',
                'description': 'Test Description',
                'start': {'dateTime': start_time.isoformat(), 'timeZone': settings.TIME_ZONE},
                'end': {'dateTime': end_time.isoformat(), 'timeZone': settings.TIME_ZONE},
                'attendees': [{'email': user.email}],
                'reminders': {'useDefault': True}
            },
            sendUpdates='all',
            conferenceDataVersion=0  # Since include_meet_link is False by default
        )

    @patch('api.calendar_integration.services.CalendarService.create_individual_event')
    def test_create_events_for_booking(self, mock_create_individual_event):
        # Set up the mock to return a fake event with an id and conferenceData
        mock_create_individual_event.return_value = {
            'id': 'test_event_id',
            'conferenceData': {
                'entryPoints': [{'uri': 'https://meet.google.com/test-meeting-link'}]
            }
        }

        # Call the method to create events
        events = self.service.create_events_for_booking(self.booking)

        # Retrieve the Booking instance and check for the meet_link
        self.booking.refresh_from_db()
        self.assertIsNotNone(self.booking.meet_link, "Booking should have a Meet link assigned")
        self.assertEqual(self.booking.meet_link, 'https://meet.google.com/test-meeting-link', "Meet link should match the mocked link")

        # Participants include trailblazers and booked_by user
        participants = list(self.booking.trailblazers) + [self.booking.booked_by]

        # Check that create_individual_event was called for each participant
        self.assertEqual(mock_create_individual_event.call_count, len(participants))

        # Check that events were created in the database
        calendar_events = CalendarEvent.objects.filter(booking=self.booking)
        self.assertEqual(calendar_events.count(), len(participants))

        # Check that each CalendarEvent has the same meet_link as the booking
        for event in calendar_events:
            self.assertEqual(event.booking.meet_link, self.booking.meet_link)

        # Verify that the returned events list has the correct length
        self.assertEqual(len(events), len(participants))

    @patch('api.calendar_integration.services.CalendarService.delete_event')
    def test_delete_events_for_booking(self, mock_delete_event):
        # First, create some CalendarEvent instances
        CalendarEvent.objects.create(
            calendar_event_id='event_id_1',
            booking=self.booking,
            user=self.trailblazer1
        )
        CalendarEvent.objects.create(
            calendar_event_id='event_id_2',
            booking=self.booking,
            user=self.trailblazer2
        )
        CalendarEvent.objects.create(
            calendar_event_id='event_id_3',
            booking=self.booking,
            user=self.booked_by_user
        )

        # Call the method
        self.service.delete_events_for_booking(self.booking)

        # Check that delete_event was called for each event
        self.assertEqual(mock_delete_event.call_count, 3)

        # Check that CalendarEvent instances were deleted from the database
        calendar_events = CalendarEvent.objects.filter(booking=self.booking)
        self.assertEqual(calendar_events.count(), 0)

    @patch('api.calendar_integration.services.build')
    def test_delete_event_success(self, mock_build):
        mock_service = MagicMock()
        mock_build.return_value = mock_service

        result = self.service.delete_event('test_event_id')

        self.assertTrue(result)
        mock_service.events.return_value.delete.assert_called_once_with(
            calendarId=settings.GOOGLE_CALENDAR_ID,
            eventId='test_event_id',
            sendUpdates='all'
        )

    @patch('api.calendar_integration.services.build')
    def test_create_individual_event_failure(self, mock_build):
        mock_service = MagicMock()
        mock_build.return_value = mock_service

        # Set up the mock to raise HttpError with 403 status
        mock_service.events.return_value.insert.return_value.execute.side_effect = HttpError(
            resp=MagicMock(status=403, reason='Forbidden'),
            content=b'Permission denied'
        )

        start_time = self.booking.start_time
        end_time = self.booking.end_time
        user = self.trailblazer1

        with self.assertRaises(HttpError) as context:
            self.service.create_individual_event(
                summary='Test Event',
                description='Test Description',
                start_time=start_time,
                end_time=end_time,
                user=user
            )

        # Assert that the exception raised is indeed a 403 Forbidden
        self.assertEqual(context.exception.resp.status, 403)

    @patch('api.calendar_integration.services.build')
    def test_delete_event_failure(self, mock_build):
        mock_service = MagicMock()
        mock_build.return_value = mock_service

        # Set up the mock to raise HttpError with 404 status
        mock_service.events.return_value.delete.return_value.execute.side_effect = HttpError(
            resp=MagicMock(status=404, reason='Not Found'),
            content=b'Event not found'
        )

        result = self.service.delete_event('nonexistent_event_id')

        # Assert that the method returns False when event is not found (404)
        self.assertFalse(result)
        mock_service.events.return_value.delete.assert_called_once_with(
            calendarId=settings.GOOGLE_CALENDAR_ID,
            eventId='nonexistent_event_id',
            sendUpdates='all'
        )


# class TestMasterCredentialService(TestCase):
#     def setUp(self):
#         self.master_creds = MasterGoogleCredentials.objects.create(
#             token='test_token',
#             refresh_token='test_refresh',
#             token_uri='https://oauth2.googleapis.com/token',
#             client_id='test_client_id',
#             client_secret='test_secret',
#             scopes=json.dumps(['https://www.googleapis.com/auth/calendar']),
#             expiry=timezone.now() + timedelta(hours=1)
#         )

#     @patch('api.calendar_integration.services.Credentials')
#     def test_refresh_token_success(self, mock_credentials):
#         mock_creds = MagicMock()
#         mock_creds.expired = True
#         mock_creds.valid = False
#         mock_creds.token = 'new_token'
#         mock_creds.expiry = timezone.now() + timedelta(hours=1)
        
#         mock_credentials.return_value = mock_creds
#         mock_credentials.return_value.refresh.return_value = None  # refresh doesn't return anything

#         credentials = MasterCredentialService.refresh_token()
        
#         self.assertEqual(credentials, mock_creds)
#         mock_creds.refresh.assert_called_once()

#         # Verify that the token and expiry were updated in the database
#         self.master_creds.refresh_from_db()
#         self.assertEqual(self.master_creds.token, 'new_token')

#     @patch('api.calendar_integration.services.Credentials')
#     def test_refresh_token_failure(self, mock_credentials):
#         mock_creds = MagicMock()
#         mock_creds.expired = True
#         mock_creds.valid = False
#         mock_creds.refresh.side_effect = Exception('Refresh failed')
        
#         mock_credentials.return_value = mock_creds

#         with self.assertRaises(Exception):
#             MasterCredentialService.refresh_token()

#         # Verify that the error was logged in the credentials
#         self.master_creds.refresh_from_db()
#         self.assertEqual(self.master_creds.error, 'Refresh failed')
