from django.core.management.base import BaseCommand
from api.organizations.models import Organization, OrganizationTag
from django.db.models import Q

class Command(BaseCommand):
    help = "Generate tags for organizations already in the database"

    def handle(self, *args, **kwargs):
        exclude_keywords = [
            "academy", "school district", "high school", "middle school", "elementary school",
            "charter", "public school", "private school", "primary school", "secondary school",
            "junior high", "senior high", "technical school", "vocational school", "trade school",
            "prep school", "boarding school", "district", "board of education", "institute", "academies",
        ]

        # Build the exclusion query dynamically
        query = Q()
        for keyword in exclude_keywords:
            query |= Q(name__icontains=keyword)

        # Fetch all organizations excluding those with the keywords
        organizations = Organization.objects.exclude(query).order_by('name')
        
        created_tags = 0
        updated_tags = 0

        for organization in organizations:
            # Standardize the organization name
            if "Unlisted" in organization.name:
                # Skip organizations with "unlisted" in their name
                continue    
            standardized_name = organization.name.title()

            # Create or update the corresponding tag
            tag, created = OrganizationTag.objects.get_or_create(name=standardized_name)
            if created:
                tag.verified = True  # Mark the tag as verified
                tag.save()
                created_tags += 1
            elif not tag.verified:
                tag.verified = True  # Update the tag to verified if not already
                tag.save()
                updated_tags += 1

        # Output the results
        self.stdout.write(self.style.SUCCESS(f"Tags created: {created_tags}"))
        self.stdout.write(self.style.SUCCESS(f"Tags updated: {updated_tags}"))