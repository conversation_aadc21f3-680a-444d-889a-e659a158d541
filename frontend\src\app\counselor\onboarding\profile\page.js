"use client"

import { useState } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from "@hookform/resolvers/zod"
import { z } from "zod"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage, FormDescription } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useToast } from '@/hooks/use-toast'
import { useRouter } from 'next/navigation'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { ArrowRight } from 'lucide-react'
import { getNextOnboardingStep } from '@/lib/utils'
import { AvatarSelection, avatarToFile } from '@/components/ui/avatarSelection'
import { Loader2 } from 'lucide-react'


// Define the form schema with Zod
const formSchema = z.object({
    firstName: z.string().min(2, "First name must be at least 2 characters").max(50, "First name must be less than 50 characters"),
    lastName: z.string().min(2, "Last name must be at least 2 characters").max(50, "Last name must be less than 50 characters"),
    avatar: z.string().min(1, "Please select an avatar"),
})

// Profile Completion Form component
const ProfileCompletionForm = () => {
    const [selectedAvatar, setSelectedAvatar] = useState('')
    const [loading, setLoading] = useState(false)
    const { toast } = useToast()
    const router = useRouter()
    const { getToken, updateAuthenticatedUser } = useAuth()

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: "",
            lastName: "",
            avatar: "",
        },
        mode: 'onBlur'
    })

    const onSubmit = async (data) => {
        setLoading(true)

        const formData = new FormData()
        formData.append('step', 'basic_info')
        formData.append('first_name', data.firstName)
        formData.append('last_name', data.lastName)

        // Fetch the avatar image as a File
        const avatarFile = await avatarToFile(data.avatar)

        // Append the avatar file to the form data
        formData.append('avatar', avatarFile)
        
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/counselors/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Authorization': `Token ${getToken()}`,
                },
                body: formData,
            })
            if (response.ok) {
                toast({ title: 'Success!', description: 'Profile uploaded successfully' })
                await updateAuthenticatedUser()
                router.push(getNextOnboardingStep('CounselorAdministrator', 'profile'))
            } else {
                console.error('Error Response:', response)
                toast({ variant: 'destructive', description: result.detail || 'Failed to upload profile' })
                setLoading(false)
            }
        } catch (error) {
            console.error('Error submitting profile:', error)
            toast({ variant: 'destructive', description: 'An unexpected error occurred' })
            setLoading(false)
        }
    }

    return (
        <div className="">
            <h1 className="text-4xl font-bold mb-8">Complete your profile</h1>
            <Form {...form}>
                <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                    <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>First Name*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your first name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Last Name*</FormLabel>
                                <FormControl>
                                    <Input placeholder="Enter your last name" {...field} />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="avatar"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Pick an avatar</FormLabel>
                                <FormControl>
                                    <AvatarSelection
                                        onSelect={(avatar) => {
                                            setSelectedAvatar(avatar)
                                            field.onChange(avatar)
                                        }}
                                        selectedAvatar={selectedAvatar}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <div>
                        <Button
                            type="submit"
                            className="mt-8"
                            disabled={loading}
                        >
                            {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}Complete Profile <ArrowRight className="ml-2 w-6 h-6" />
                        </Button>
                    </div>
                </form>
            </Form>
        </div>
    )
}

// Main Page component
const ProfileCompletionPage = () => {
    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="py-10">
                    <StepProgressDisplay currentStep={2} totalSteps={2} />
                </div>
                <div className="w-full md:w-5/6 lg:w-5/6">
                    <ProfileCompletionForm />
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(ProfileCompletionPage)
