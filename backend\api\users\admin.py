from django.contrib import admin
from django.contrib.auth.admin import UserAdmin as DefaultUserAdmin
from django.contrib.auth.admin import UserAdmin as DefaultUserAdmin
from .models import User
from .models import Profile
from .models import CollegeStudentProfile, HighSchoolStudentProfile, CounselorAdministratorProfile, Availability, OnboardingProgress
from .views import CollegeStudentViewSet
# Register new profile models

admin.site.register(CounselorAdministratorProfile)
    

@admin.register(CollegeStudentProfile)
class CollegeStudentProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'university', 'college_major', 'background_check_passed_at', 'is_waitlisted')
    search_fields = ('user__email', 'university', 'college_major', 'high_school_zip_code')
    list_filter = ('background_check_passed_at', 'is_waitlisted', 'high_school_zip_code')
    actions = ['add_to_waitlist', 'remove_from_waitlist']

    def add_to_waitlist(self, request, queryset):
        queryset.update(is_waitlisted=True)
        self.message_user(request, "Selected students have been added to the waitlist.")

    def remove_from_waitlist(self, request, queryset):
        queryset.update(is_waitlisted=False)
        self.message_user(request, "Selected students have been removed from the waitlist.")

    add_to_waitlist.short_description = "Add selected students to waitlist"
    remove_from_waitlist.short_description = "Remove selected students from waitlist"


@admin.register(HighSchoolStudentProfile)
class HighSchoolStudentProfileAdmin(admin.ModelAdmin):
    list_display = ('user', 'high_school_name', 'graduation_year', 'current_zip_code')
    search_fields = ('user__email', 'high_school_name', 'graduation_year')
    list_filter = ('high_school_name', 'graduation_year', 'current_zip_code')


@admin.register(User)
class UserAdmin(DefaultUserAdmin):
    """
    User admin
    """
    list_display = ('email', 'first_name', 'last_name', 'is_staff')

@admin.register(Availability)
class AvailabilityAdmin(admin.ModelAdmin):
    list_display = ('user', 'time_zone', 'created_at', 'updated_at')
    search_fields = ('user__email', 'time_zone')

@admin.register(OnboardingProgress)
class OnboardingProgressAdmin(admin.ModelAdmin):
    list_display = ('user', 'steps_completed')
    search_fields = ('user__email',)
