# Generated by Django 4.2.13 on 2025-06-24 13:52

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('users', '0032_highschoolstudentprofile_recommendations_status'),
    ]

    operations = [
        migrations.Alter<PERSON>ield(
            model_name='highschoolstudentprofile',
            name='ethnicity',
            field=models.CharField(blank=True, choices=[('native_american', 'Native American or Alaska Native'), ('south_asian', 'South Asian'), ('east_asian', 'East Asian'), ('black', 'Black or African American'), ('hispanic', 'Hispanic or Latine'), ('middle_eastern', 'Middle Eastern or North African'), ('native_hawaiian', 'Native Hawaiian or Pacific Islander'), ('white', 'White'), ('multiracial', 'Multiracial'), ('other', 'Other')], max_length=50, null=True),
        ),
    ]
