import unittest
from unittest import mock
from django.test import TestCase
from django.contrib.auth import get_user_model
from api.users.models import HighSchoolStudentProfile
from api.users.tasks import run_embedding_and_recommendation

User = get_user_model()

class RunEmbeddingAndRecommendationTaskTests(TestCase):
    def setUp(self):
        # Create a high school student user
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpassword',
            user_type=User.HIGH_SCHOOL_STUDENT_TYPE
        )
        self.profile = self.user.high_school_student_profile
        
        # Set up some profile data needed for recommendations
        self.profile.intended_majors = ["Computer Science"]
        self.profile.interests = ["Programming"]
        self.profile.save()
        
    @mock.patch('api.users.tasks.match_universities')
    @mock.patch('api.users.tasks.generate_ideal_university_description')
    def test_status_transitions_success_case(self, mock_generate, mock_match):
        """Test that the task updates status from 'not_requested' to 'pending' to 'ready' on success."""
        # Configure mocks
        mock_generate.return_value = ("Mock description", [0.1] * 1536)
        mock_match.return_value = {
            "reach_universities": ["1"],
            "target_universities": ["2"],
            "safety_universities": ["3"]
        }
        
        # Verify initial status
        self.assertEqual(self.profile.recommendations_status, 'not_requested')
        
        # Run the task
        run_embedding_and_recommendation(self.profile)
        
        # Verify final status
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.recommendations_status, 'ready')
        
        # Verify the mock functions were called
        mock_generate.assert_called_once()
        mock_match.assert_called_once()
        
    @mock.patch('api.users.tasks.match_universities')
    @mock.patch('api.users.tasks.generate_ideal_university_description')
    def test_status_transitions_failure_case(self, mock_generate, mock_match):
        """Test that the task sets status to 'error' but not 'ready' when an exception occurs."""
        # Configure the first mock to work but the second to raise an exception
        mock_generate.return_value = ("Mock description", [0.1] * 1536)
        mock_match.side_effect = Exception("Mock error")
        
        # Verify initial status
        self.assertEqual(self.profile.recommendations_status, 'not_requested')
        
        # Run the task
        run_embedding_and_recommendation(self.profile)
        
        # Verify status after error
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.recommendations_status, 'error')
        
        # Verify the mocks were called as expected
        self.assertEqual(mock_generate.call_count, 3)
        self.assertEqual(mock_match.call_count, 3)
        
    @mock.patch('api.users.tasks.generate_ideal_university_description')
    def test_status_transitions_early_failure(self, mock_generate):
        """Test that the task sets status to 'error' but not 'ready' when an early exception occurs."""
        # Configure the mock to raise an exception
        mock_generate.side_effect = Exception("Mock error")
        
        # Verify initial status
        self.assertEqual(self.profile.recommendations_status, 'not_requested')
        
        # Run the task
        run_embedding_and_recommendation(self.profile)
        
        # Verify status after error
        self.profile.refresh_from_db()
        self.assertEqual(self.profile.recommendations_status, 'error')
        
        # Verify the mock was called three times
        self.assertEqual(mock_generate.call_count, 3)