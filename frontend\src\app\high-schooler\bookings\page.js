"use client"

import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { MainLayout } from "@/components/ui/mainLayout"
import BookingList from "@/components/ui/BookingList";


// Main content component
const MainContent = ({ user, userLoading, userError }) => {
    return (
        <div className="space-y-12">
            <h1 className="text-2xl md:text-3xl font-bold mb-8 text-left">Bookings</h1>
            <BookingList user={user} userLoading={userLoading} userError={userError}/>
        </div>
    );
};

// Page component
const BookingsPage = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()

    return (
        <MainLayout user={user} displaySidebarMenu={true}>
            <MainContent user={user} userLoading={userLoading} userError={userError} />
        </MainLayout>
    );
};

export default withAuth(BookingsPage);
