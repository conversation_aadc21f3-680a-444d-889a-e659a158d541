import logging
import threading
import time
from api.universities.services import generate_ideal_university_description, match_universities

logger = logging.getLogger(__name__)

MAX_RETRIES = 3
RETRY_DELAY_SECONDS = 2


def run_embedding_and_recommendation(profile_instance):
    """
    Run the embedding and recommendations generation with up to 3 retries on failure.
    Updates the recommendations_status to 'pending' when starting, 'ready' on success, and 'error' on failure.
    """
    attempt = 0

    while attempt < MAX_RETRIES:
        try:
            attempt += 1
            logger.info(f"Attempt {attempt} to generate recommendations for profile {profile_instance.id}")

            # Set status to pending
            profile_instance.recommendations_status = 'pending'
            profile_instance.save(update_fields=['recommendations_status'])

            # Generate description and embedding
            description, embedding = generate_ideal_university_description(profile_instance)

            # Save them
            profile_instance.ideal_university_description = description
            profile_instance.ideal_university_embedding = embedding
            profile_instance.save(update_fields=['ideal_university_description', 'ideal_university_embedding'])

            # Match universities
            recommendations = match_universities(profile_instance)

            # Save recommendations and mark as ready
            profile_instance.university_recommendations = recommendations
            profile_instance.recommendations_status = 'ready'
            profile_instance.save(update_fields=['university_recommendations', 'recommendations_status'])

            logger.info(f"Successfully generated recommendations for profile {profile_instance.id}")
            return

        except Exception as e:
            logger.error(
                f"Error in run_embedding_and_recommendation for profile {profile_instance.id}, attempt {attempt}: {str(e)}"
            )
            if attempt < MAX_RETRIES:
                time.sleep(RETRY_DELAY_SECONDS)
            else:
                logger.error(
                    f"All {MAX_RETRIES} attempts failed for profile {profile_instance.id}. Marking as error."
                )
                profile_instance.recommendations_status = 'error'
                profile_instance.save(update_fields=['recommendations_status'])


def generate_recommendations_in_background(profile_instance):
    """
    Generate university recommendations in a background thread.
    """
    thread = threading.Thread(target=run_embedding_and_recommendation, args=(profile_instance,))
    thread.start()
    return thread
