from django.db import models
from django.conf import settings
from django.utils import timezone
from datetime import datetime, timedelta
import json


class MasterGoogleCredentials(models.Model):
    token = models.TextField(null=True, blank=True)
    refresh_token = models.TextField(null=True, blank=True)
    token_uri = models.TextField(null=True, blank=True)
    client_id = models.TextField(null=True, blank=True)
    client_secret = models.TextField(null=True, blank=True)
    scopes = models.TextField(null=True, blank=True)
    expiry = models.DateTimeField(null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    error = models.TextField(null=True, blank=True)
    singleton_key = models.BooleanField(default=True, unique=True)
    service_account_enabled = models.BooleanField(default=False)

    class Meta:
        verbose_name = 'Master Google Credentials'
        verbose_name_plural = 'Master Google Credentials'

    @classmethod
    def get_credentials(cls):
        try:
            # Retrieve the existing singleton row
            return cls.objects.get()
        except cls.DoesNotExist:
            # Create a new row if none exist
            return cls.objects.create(
                refresh_token=settings.GOOGLE_REFRESH_TOKEN,
                token_uri='https://oauth2.googleapis.com/token',
                client_id=settings.GOOGLE_CLIENT_ID,
                client_secret=settings.GOOGLE_CLIENT_SECRET,
                scopes=json.dumps(settings.GOOGLE_CALENDAR_SCOPES),
                expiry=None,  # Set expiry to None since token is None
            )
        except cls.MultipleObjectsReturned:
            # Handle the case where there are multiple rows
            raise RuntimeError(
                "Multiple MasterGoogleCredentials instances exist! Please clean the database."
            )

    def to_credentials_dict(self):
        """Convert to format needed by google-auth"""
        return {
            'token': self.token,
            'refresh_token': self.refresh_token,
            'token_uri': self.token_uri,
            'client_id': self.client_id,
            'client_secret': self.client_secret,
            'scopes': json.loads(self.scopes),
            'expiry': self.expiry.isoformat() if self.expiry else None,
        }


class CalendarEvent(models.Model):
    calendar_event_id = models.CharField(max_length=1024)
    booking = models.ForeignKey('bookings.Booking', on_delete=models.CASCADE)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name="calendar_events")
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        indexes = [
            models.Index(fields=['calendar_event_id']),
        ]
