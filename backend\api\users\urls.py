from django.urls import path
from api.users.views import (
    CollegeStudentSignUpView,
    CounselorAdministratorSignUpView,
    HighSchoolStudentSignUpView,
    CollegeStudentOnboardingView,
    HighSchoolStudentOnboardingView,
    CounselorAdministratorOnboardingView,
    LoginView,
    MajorListView,
    HighSchoolStudentProfileCreateView,
    HighSchoolStudentProfileDetailView,  # Add import for the new view
    RecommendationsStatusView,
    ProfileUpdateRecommendationRegenerationView,
    RecommendationThrottleStatusView
)

urlpatterns = [
    path('college-students/signup/', CollegeStudentSignUpView.as_view(), name='signup-college-student'),
    path('counselors/signup/', CounselorAdministratorSignUpView.as_view(), name='signup-counselor-administrator'),
    path('high-school-students/signup/', HighSchoolStudentSignUpView.as_view(), name='signup-high-school-student'),
    path('college-students/onboarding/', CollegeStudentOnboardingView.as_view(), name='onboarding-college-student'),
    path('counselors/onboarding/', CounselorAdministratorOnboardingView.as_view(), name='onboarding-counselor-administrator'),
    path('high-school-students/onboarding/', HighSchoolStudentOnboardingView.as_view(), name='onboarding-high-school-student'),
    path('login/', LoginView.as_view(), name='login'),
    path('majors/', MajorListView.as_view(), name='major-list'),
    path('high-school-student-profile/', HighSchoolStudentProfileCreateView.as_view(), name='high-school-student-profile-create'),
    path('highschoolstudentprofile/me/', HighSchoolStudentProfileDetailView.as_view(), name='high-school-student-profile-detail'),
    path(
        'highschoolstudentprofile/recommendation-throttle-status/',
        RecommendationThrottleStatusView.as_view(),
        name='recommendation-throttle-status'
    ),
    path('recommendations/status/', RecommendationsStatusView.as_view(), name='recommendations-status'),
    path('profile/update/', ProfileUpdateRecommendationRegenerationView.as_view(), name='profile-update-recommendation-regeneration'),
]