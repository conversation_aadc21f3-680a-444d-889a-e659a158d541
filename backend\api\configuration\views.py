from rest_framework import generics
from .models import Configuration
from .serializers import ConfigurationSerializer
from rest_framework.permissions import IsAuthenticated

class ConfigurationDetailView(generics.RetrieveAPIView):
    queryset = Configuration.objects.all()
    serializer_class = ConfigurationSerializer
    permission_classes = [IsAuthenticated]


    def get_object(self):
        # Assuming there is only one configuration object
        return Configuration.objects.first()