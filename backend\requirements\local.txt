-r production.txt

Werkzeug[watchdog]==3.0.3 # https://github.com/pallets/werkzeug
ipdb==0.13.13  # https://github.com/gotcha/ipdb
psycopg[c]==3.1.19  # https://github.com/psycopg/psycopg
watchfiles==0.22.0  # https://github.com/samuelcolvin/watchfiles

# Testing
# ------------------------------------------------------------------------------
mypy==1.10.0  # https://github.com/python/mypy
django-stubs[compatible-mypy]==5.0.2  # https://github.com/typeddjango/django-stubs
pytest==8.2.2  # https://github.com/pytest-dev/pytest
pytest-sugar==1.0.0  # https://github.com/Frozenball/pytest-sugar
djangorestframework-stubs[compatible-mypy]==3.15.0  # https://github.com/typeddjango/djangorestframework-stubs
requests-mock==1.12.1

# Code quality
# ------------------------------------------------------------------------------
ruff==0.4.10  # https://github.com/astral-sh/ruff
coverage==7.5.4  # https://github.com/nedbat/coveragepy
djlint==1.34.1  # https://github.com/Riverside-Healthcare/djLint
pre-commit==3.7.1  # https://github.com/pre-commit/pre-commit

# Django
# ------------------------------------------------------------------------------
factory-boy==3.3.0  # https://github.com/FactoryBoy/factory_boy

django-debug-toolbar==4.4.2  # https://github.com/jazzband/django-debug-toolbar
django-extensions==3.2.3  # https://github.com/django-extensions/django-extensions
django-coverage-plugin==3.1.0  # https://github.com/nedbat/django_coverage_plugin
pytest-django==4.8.0  # https://github.com/pytest-dev/pytest-django
