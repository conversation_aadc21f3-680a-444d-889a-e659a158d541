from rest_framework import serializers
from api.organizations.models import Organization, OrganizationTag

class OrganizationSerializer(serializers.ModelSerializer):
    class Meta:
        model = Organization
        fields = [
            "id",
            "name",
            "zip_code",
            "city",
            "state",
            "school_district",
            "created_at",
            "updated_at"
        ]
        read_only_fields = ("id", "created_at", "updated_at")
        
    def validate_name(self, value):
        if len(value) > 255:
            raise serializers.ValidationError("Name must not exceed 255 characters.")
        return value
        
    def validate_zip_code(self, value):
        if len(value) > 10:
            raise serializers.ValidationError("Zip code must not exceed 10 characters.")
        return value
        
    def validate_city(self, value):
        if len(value) > 100:
            raise serializers.ValidationError("City must not exceed 100 characters.")
        return value
        
    def validate_state(self, value):
        if len(value) > 100:
            raise serializers.ValidationError("State must not exceed 100 characters.")
        return value

class OrganizationTagSerializer(serializers.ModelSerializer):
    class Meta:
        model = OrganizationTag
        fields = ['id', 'name', 'verified']
