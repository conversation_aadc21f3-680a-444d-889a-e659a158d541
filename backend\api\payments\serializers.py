from rest_framework import serializers
from .models import PaymentLog, PaymentPeriodTrailblazerHours, SkippedPaymentLog



class PaymentLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = PaymentLog
        fields = ['id', 'user', 'paypal_email', 'hours', 'amount', 'payment_status', 'time_processed', 'payment_period_start', 'payment_period_end', 'payment_date', 'payout_batch_id', 'payout_item_id', 'batch_status', 'time_completed', 'error_message']

class SkippedPaymentLogSerializer(serializers.ModelSerializer):
    class Meta:
        model = SkippedPaymentLog
        fields = ['id', 'paypal_email', 'hours', 'error_message', 'time_processed','payment_status', 'payment_period_start', 'payment_period_end', 'payout_batch_id', 'payout_item_id', 'payment_date', 'batch_status', 'time_completed','created_at', 'user', 'updated_at']


class PaymentPeriodTrailblazerHoursSerializer(serializers.ModelSerializer):
    paypal_email = serializers.SerializerMethodField()
    
    class Meta:
        model = PaymentPeriodTrailblazerHours
        fields = ['id', 'paypal_email', 'user',  'total_hours','payment_period_start', 'payment_period_end', 'created_at']

    def get_paypal_email(self, obj):
        return obj.user.college_student_profile.paypal_email


