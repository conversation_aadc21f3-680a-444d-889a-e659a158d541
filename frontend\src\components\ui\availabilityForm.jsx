import React, { useEffect } from "react"
import { useForm, useFieldArray } from "react-hook-form"
import { zodResolver } from "@hookform/resolvers/zod"
import * as z from "zod"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Switch } from "@/components/ui/switch"
import { Button } from "@/components/ui/button"
import { Trash, Plus, ArrowRight } from 'lucide-react'
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import TimePickerInput from "@/components/ui/TimePicker"


const timeSlotSchema = z.array(
  z.object({
    start: z.string(),
    end: z.string()
  }).refine((slot) => {
    // Check that end time is after start time
    if (slot.start === "" || slot.end === "") return true
    return slot.start < slot.end
  }, "End time must be after start time")
)

// Zod schema for form validation
const formSchema = z.object({
  timeZone: z.string().refine((tz) => tz !== "", "Time zone is required"),
  days: z.array(
    z.object({
      day: z.string(),
      isAvailable: z.boolean(),
      timeSlots: timeSlotSchema
    }).refine((day) => {
      // Check that at least one time slot is available
      if (!day.isAvailable) return true
      // Check that at least one valid time slot is available
      return day.timeSlots.some(slot => slot.start !== "" && slot.end !== "")
    }, "At least one valid time slot must be available").refine((day) => {
      // Check for overlapping slots
      if (!day.isAvailable) return true
      const sorted = day.timeSlots.slice().sort((a, b) => a.start.localeCompare(b.start))
      for (let i = 0; i < sorted.length - 1; i++) {
        if (sorted[i].end > sorted[i + 1].start) return false
      }
      return true
    }, "Time ranges cannot overlap")
  ).refine((days) => {
    // Check that at least one day is available
    return days.filter(day => day.isAvailable).length > 0
  }, "At least one day must be available")
})

// Time zone options
const timeZones = [
  { value: "America/New_York", label: "Eastern Standard Time (EST)" }, // GMT-5
  { value: "America/Indiana/Indianapolis", label: "Eastern Standard Time (EST) – Indiana" }, // GMT-5
  { value: "America/Chicago", label: "Central Standard Time (CST)" }, // GMT-6
  { value: "America/Denver", label: "Mountain Standard Time (MST)" }, // GMT-7
  { value: "America/Phoenix", label: "Mountain Standard Time (MST) – Arizona" }, // GMT-7
  { value: "America/Los_Angeles", label: "Pacific Standard Time (PST)" }, // GMT-8
  { value: "America/Anchorage", label: "Alaska Standard Time (AKST)" }, // GMT-9
  { value: "Pacific/Honolulu", label: "Hawaii-Aleutian Standard Time (HST)" }, // GMT-10
];

// DayAvailability component
export const DayAvailability = ({ day, control, index }) => {
  const { fields, append, remove } = useFieldArray({
    control,
    name: `days.${index}.timeSlots`
  })

  return (
    <div className="flex">
      <FormField
        control={control}
        name={`days.${index}.isAvailable`}
        render={({ field }) => (
          <FormItem className="flex space-x-2 space-y-0 min-w-[150px] mt-2">
            <FormControl>
              <Switch
                checked={field.value}
                onCheckedChange={field.onChange}
              />
            </FormControl>
            <FormLabel className="font-medium pt-1 pl-2">{day}</FormLabel>
          </FormItem>
        )}
      />
      <div className="">
        {fields.map((slot, slotIndex) => (
          <>
            <div key={slot.id} className="flex space-x-2 pb-4 pl-2">
              <FormField
                control={control}
                name={`days.${index}.timeSlots.${slotIndex}.start`}
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <TimePickerInput
                        disabled={!control._formValues.days[index].isAvailable}
                        {...field}
                      />
                    </FormControl>
                    {fieldState.error && <FormMessage>{fieldState.error.message}</FormMessage>}
                  </FormItem>
                )}
              />
              <span className="relative top-1">-</span>
              <FormField
                control={control}
                name={`days.${index}.timeSlots.${slotIndex}.end`}
                render={({ field, fieldState }) => (
                  <FormItem>
                    <FormControl>
                      <TimePickerInput
                        disabled={!control._formValues.days[index].isAvailable}
                        {...field}
                      />
                    </FormControl>
                    {fieldState.error && <FormMessage>{fieldState.error.message}</FormMessage>}
                  </FormItem>
                )}
              />
              {
                slotIndex === 0 ? (
                  <Button
                    type="button"
                    variant="ghost"
                    onClick={() => append({ start: "", end: "" })}
                    disabled={!control._formValues.days[index].isAvailable}
                  >
                    <Plus className="w-4 h-4 text-gray-500" />
                  </Button>
                ) : (
                  <Button type="button" variant="ghost" onClick={() => remove(slotIndex)} disabled={!control._formValues.days[index].isAvailable}>
                    <Trash className="w-4 h-4 text-gray-500" />
                  </Button>
                )
              }
            </div>
            {/* Display error message for each time slot */}
            {control._formState.errors.days?.[index]?.timeSlots?.[slotIndex]?.root && <p className="text-xs pl-2 mb-4 text-red-500">{control._formState.errors.days[index].timeSlots[slotIndex].root.message}</p>}
          </>
        ))}
        {/* Display error message for each day */}
        {control._formState.errors.days?.[index]?.root?.message && <p className="text-xs pl-2 mb-4 text-red-500">{control._formState.errors.days[index].root.message}</p>}
      </div>
    </div>
  )
}

// Main AvailabilityForm component
export const AvailabilityForm = ({ onSubmit, submitButton, submitPosition = "bottom", initialData }) => {
  const form = useForm({
    resolver: zodResolver(formSchema),
    defaultValues: {
      timeZone: "",
      days: [
        { day: "Sunday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
        { day: "Monday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
        { day: "Tuesday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
        { day: "Wednesday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
        { day: "Thursday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
        { day: "Friday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
        { day: "Saturday", isAvailable: false, timeSlots: [{ start: "", end: "" }] },
      ]
    }
  })

  useEffect(() => {
    if (initialData) {
      form.reset(initialData)
    }
  }, [initialData])

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {(submitButton && submitPosition === "top") && submitButton}
        <FormField
          control={form.control}
          name="timeZone"
          render={({ field, fieldState }) => (
            <FormItem className="w-1/2 md:w-80">
              <FormLabel>Time Zone</FormLabel>
              <Select onValueChange={field.onChange} value={field.value} className="">
                <FormControl>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a time zone" />
                  </SelectTrigger>
                </FormControl>
                { fieldState.error && <FormMessage>{fieldState.error.message}</FormMessage> }
                <SelectContent>
                  {timeZones.map((tz) => (
                    <SelectItem key={tz.value} value={tz.value}>{tz.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </FormItem>
          )}
        />
        <div className="space-y-1">
          {form.getValues("days").map((day, index) => (
            <DayAvailability key={day.day} day={day.day} control={form.control} index={index} />
          ))}
          {/* Display error message for days */}
          {form.formState.errors.days?.root && <p className="text-xs mb-4 text-red-500">{form.formState.errors.days.root.message}</p>}
        </div>
        {/* Display general error message */}
        {form.formState.errors.general && <p className="text-xs mb-4 text-red-500">{form.formState.errors.general.message}</p>}
        {form.formState.errors.detail && <p className="text-red-500">{form.formState.errors.detail.message}</p>}
        {
          submitPosition === "bottom" && (
            submitButton ? submitButton : (
              <Button type="submit" className="hover:bg-green-500 text-md">
                Save Changes
              </Button>
            )
          )
        }
      </form>
    </Form>
  )
}
