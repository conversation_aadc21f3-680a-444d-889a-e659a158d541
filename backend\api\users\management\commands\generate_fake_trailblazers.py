from django.core.management.base import BaseCommand, CommandError
from django.contrib.auth import get_user_model
from api.users.models import User, CollegeStudentProfile, Availability
from api.organizations.models import Organization
from faker import Faker
import random
import logging
from datetime import time, timedelta, datetime

fake = Faker()
logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Generate fake Trailblazer profiles and organizations'

    def add_arguments(self, parser):
        parser.add_argument('number_of_profiles', type=int, nargs='?', default=10, help='Number of Trailblazer profiles to create')

    def handle(self, *args, **kwargs):
        number = kwargs['number_of_profiles']
        if number < 0:
            raise CommandError('Number of profiles must be a positive integer.')

        try:
            organizations = self.create_fake_organizations(3)
            self.stdout.write(self.style.SUCCESS(f'Successfully created {len(organizations)} organizations.'))
            
            profiles = self.create_fake_profiles(number, organizations)
            self.stdout.write(self.style.SUCCESS(f'Successfully created {len(profiles)} Trailblazer profiles.'))
        except Exception as e:
            logger.error(f'Error generating fake data: {e}')
            raise CommandError('An error occurred while generating fake data.')

    def create_fake_organizations(self, count):
        organizations = []
        for _ in range(count):
            org = Organization.objects.create(
                name=fake.company(),
                city=fake.city(),
                state=fake.state(),
                zip_code=fake.zipcode(),
            )
            organizations.append(org)
        return organizations

    def create_fake_profiles(self, count, organizations):
        users = []
        for _ in range(count):
            email = fake.unique.email()
            email = self.ensure_edu_email(email)
            user = User.objects.create_user(
                email=email,
                password='Password123!',
                first_name=fake.first_name(),
                last_name=fake.last_name(),
                user_type=User.COLLEGE_STUDENT_TYPE,
            )
            CollegeStudentProfile.objects.filter(
                user=user,
            ).update(
                university=random.choice([
                    "Harvard University",
                    "Stanford University",
                    "MIT",
                    "Yale University",
                    "Princeton University",
                ]),
                interests=random.sample([
                    "arts",
                    "consulting",
                    "entrepreneurship",
                    "engineering",
                    "finance",
                    "government",
                    "medicine",
                    "marketing",
                    "nonprofit",
                    "tech",
                ], k=3),  # Replace with valid interests
                high_school_name=fake.company(),
                high_school_zip_code=fake.zipcode(),
                high_school_city=fake.city(),
                high_school_state=fake.state(),
                college_major=random.choice([
                    "Computer Science",
                    "Business Administration",
                    "Psychology",
                    "Engineering",
                    "Biology",
                ]),
                university_tags=random.sample([
                    "public",
                    "private",
                    "liberal-arts",
                    "ivy-league",
                    "hsi",
                    "tcu",
                    "hbcu",
                    "single-sex",
                    "faith-based",
                    "community",
                    "military",
                    "technical",
                ], k=2),  # Replace with valid tags
                bio=fake.text(),
                is_email_verified=True,
                background_check_passed_at=fake.date_time_this_year(),
            )

            # Generate random availability for the user
            self.create_fake_availability(user)
            users.append(user)
        return users

    def create_fake_availability(self, user):
        # Choose two random days for availability
        available_days = random.sample([
            'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday'
        ], k=2)
        
        # Create random 2-hour time ranges for the available days
        availability_data = {}
        for day in available_days:
            start_time = fake.time_object()
            end_time = (datetime.combine(datetime.today(), start_time) + timedelta(hours=2)).time()
            availability_data[f'{day}_available'] = True
            availability_data[f'{day}_time_ranges'] = [{'start_time': start_time.isoformat(), 'end_time': end_time.isoformat()}]
        
        # Set the rest of the days as not available
        for day in ['monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday', 'sunday']:
            if day not in available_days:
                availability_data[f'{day}_available'] = False
                availability_data[f'{day}_time_ranges'] = []
        
        # Create availability object
        Availability.objects.create(
            user=user,
            time_zone=random.choice(['UTC', 'EST', 'PST', 'CST']),
            **availability_data
        )

    def ensure_edu_email(self, email):
        if not email.endswith('.edu'):
            domain = fake.domain_name()
            email = f"{email.split('@')[0]}@{domain}.edu"
        return email
