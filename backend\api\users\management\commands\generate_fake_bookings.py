from django.core.management.base import BaseCommand
from faker import Faker
from api.users.models import User
from api.bookings.models import Booking
import random
from datetime import datetime, timedelta, timezone

class Command(BaseCommand):
    help = 'Generate fake bookings for counselors'

    def add_arguments(self, parser):
        parser.add_argument(
            '--single-trailblazer-iterations',
            type=int,
            default=1,
            help='Number of iterations to run the single trailblazer booking generation function'
        )
        parser.add_argument(
            '--multiple-trailblazers-iterations',
            type=int,
            default=1,
            help='Number of iterations to run the multiple trailblazers booking generation function'
        )
        parser.add_argument(
            '--booking_status',
            type=str,
            default='confirmed',
            help='The status of the booking'
        )

    def handle(self, *args, **options):
        fake = Faker()
        single_trailblazer_iterations = options['single_trailblazer_iterations']
        multiple_trailblazers_iterations = options['multiple_trailblazers_iterations']
        booking_status = options['booking_status']
        # Get all college students and counselors
        admin_email = '<EMAIL>'
        college_students = User.objects.filter(user_type=User.COLLEGE_STUDENT_TYPE).exclude(email=admin_email)
        counselors = User.objects.filter(user_type=User.COUNSELOR_ADMINISTRATOR_TYPE).exclude(email=admin_email)

        # Check if there are any college students or counselors in the database
        if not college_students.exists() or not counselors.exists():
            self.stdout.write(self.style.ERROR('No college students or counselors found in the database.'))
            return

        # Calculate the start and end times for the bookings in UTC
        now = datetime.now(timezone.utc)
        current_monday = now - timedelta(days=now.weekday())
        previous_monday = current_monday - timedelta(weeks=1)
        start_range = previous_monday + timedelta(minutes=1)
        end_range = current_monday

        # Run the single trailblazer function n number of times
        for _ in range(single_trailblazer_iterations):
            self.generate_booking_with_single_trailblazer(fake, counselors, college_students, start_range, end_range, booking_status)

        # Run the multiple trailblazers function n number of times
        for _ in range(multiple_trailblazers_iterations):
            self.generate_booking_with_multiple_trailblazers(fake, counselors, college_students, start_range, end_range, booking_status)

    def generate_booking_with_single_trailblazer(self, fake, counselors, college_students, start_range, end_range, booking_status):
        # Generate a booking with a single trailblazer
        booked_by = random.choice(counselors)
        start_time = fake.date_time_between(start_date=start_range, end_date=end_range, tzinfo=timezone.utc)
        end_time = start_time + timedelta(hours=random.randint(1, 3))

        booking = Booking.objects.create(
            booked_by=booked_by,
            start_time=start_time,
            end_time=end_time,
        )

        trailblazer = random.choice(college_students)
        booking.add_trailblazer(trailblazer, status=booking_status)

        self.stdout.write(self.style.SUCCESS(f'Created booking: {booking.id} with trailblazer: {trailblazer.username}'))

    def generate_booking_with_multiple_trailblazers(self, fake, counselors, college_students, start_range, end_range, booking_status):
        # Generate a booking with 2 or 3 trailblazers
        booked_by = random.choice(counselors)
        start_time = fake.date_time_between(start_date=start_range, end_date=end_range, tzinfo=timezone.utc)
        end_time = start_time + timedelta(hours=random.randint(1, 3))

        booking = Booking.objects.create(
            booked_by=booked_by,
            start_time=start_time,
            end_time=end_time,
        )

        num_trailblazers = random.randint(2, 3)
        added_trailblazers = set()
        for _ in range(num_trailblazers):
            trailblazer = random.choice(college_students)
            if trailblazer in added_trailblazers:
                continue
            try:
                booking.add_trailblazer(trailblazer, status=booking_status)
                added_trailblazers.add(trailblazer)
            except ValidationError as e:
                self.stdout.write(self.style.WARNING(f"Trailblazer {trailblazer.email} is already assigned to booking {booking.id}. Skipping."))

        self.stdout.write(self.style.SUCCESS(f'Created booking: {booking.id} with {len(added_trailblazers)} trailblazers'))

        self.stdout.write(self.style.SUCCESS('Successfully generated fake bookings.'))