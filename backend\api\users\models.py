import uuid
import pytz
import re
from datetime import datetime, timedelta

from django.db import models
from django.conf import settings
from django.dispatch import receiver
from django.contrib.auth.models import AbstractUser, BaseUserManager
from django.db.models.signals import post_save
from django.core.exceptions import ValidationError
from django.utils import timezone
from django.utils.translation import gettext_lazy as _
from rest_framework.authtoken.models import Token
from api.configuration.models import Configuration
from api.organizations.models import OrganizationTag   
from pgvector.django import VectorField
from django.db.models import <PERSON><PERSON><PERSON><PERSON>


def validate_edu_email(value):
    if not re.match(r"[^@]+@[^@]+\.(edu)$", value):
        raise ValidationError("College students must use a .edu email address.")


class UserManager(BaseUserManager):
    """
    Custom user manager where email is the unique identifiers
    for authentication instead of usernames.
    """
    def create_user(self, email, password=None, **extra_fields):
        """
        Create and return a regular user with an email and password.
        """
        if not email:
            raise ValueError('The Email field must be set')
        email = self.normalize_email(email)
        user = self.model(email=email, **extra_fields)
        user.username = email
        user.set_password(password)
        user.save(using=self._db)
        return user

    def create_superuser(self, email, password=None, **extra_fields):
        """
        Create and return a superuser with an email and password.
        """
        extra_fields.setdefault('is_staff', True)
        extra_fields.setdefault('is_superuser', True)

        return self.create_user(email, password, **extra_fields)


class User(AbstractUser):
    """
    Custom user model with email as the unique identifier.
    """
    COLLEGE_STUDENT_TYPE = 'CollegeStudent'
    HIGH_SCHOOL_STUDENT_TYPE = 'HighSchoolStudent'
    COUNSELOR_ADMINISTRATOR_TYPE = 'CounselorAdministrator'

    email = models.EmailField(unique=True)
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    user_type = models.CharField(
        choices=[
            (COLLEGE_STUDENT_TYPE, 'College Student'),
            (HIGH_SCHOOL_STUDENT_TYPE, 'High School Student'),
            (COUNSELOR_ADMINISTRATOR_TYPE, 'Counselor/Administrator')
        ],
        max_length=30,
        default=COLLEGE_STUDENT_TYPE
    )

    USERNAME_FIELD = 'email'
    REQUIRED_FIELDS = []

    objects = UserManager()

    def __str__(self):
        return self.username

    @property
    def profile(self):
        if self.user_type == self.COLLEGE_STUDENT_TYPE:
            return self.college_student_profile
        elif self.user_type == self.HIGH_SCHOOL_STUDENT_TYPE:
            return self.high_school_student_profile
        elif self.user_type == self.COUNSELOR_ADMINISTRATOR_TYPE:
            return self.counselor_administrator_profile
        return None


class Profile(models.Model):
    """
    User profile model.
    """
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
    )
    avatar = models.ImageField(
        upload_to='avatars/',
        null=True,
        blank=True,
        default=None
    )
    is_email_verified = models.BooleanField(default=False)
    time_zone = models.CharField(max_length=50, default='UTC')

    class Meta:
        abstract = True  # Make Profile abstract


class CollegeStudentProfile(Profile):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='college_student_profile'
    )
    bio = models.TextField(blank=True)
    university = models.CharField(max_length=255)
    graduation_year = models.CharField(max_length=255)
    paypal_email = models.CharField(max_length=255, default="")
    university_tags = models.JSONField(default=list)
    high_school_name = models.CharField(max_length=255)
    high_school_zip_code = models.CharField(max_length=10)
    high_school_city = models.CharField(max_length=100)
    high_school_state = models.CharField(max_length=100)
    college_major = models.CharField(max_length=255)
    interests = models.JSONField(default=list)
    organization_tags = models.ManyToManyField(
        OrganizationTag,
        blank=True,
        related_name='college_student_profile'
    )
    background_check_passed_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when the background check was passed")
    is_waitlisted = models.BooleanField(default=False)
    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} (College Student)"


class CounselorAdministratorProfile(Profile):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='counselor_administrator_profile'
    )
    position = models.CharField(max_length=255)
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='counselors_administrators',
        null=True,
        blank=True,
        default=None
    )
    verified_at = models.DateTimeField(null=True, blank=True, help_text="Timestamp when the background check was passed")

    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} (Counselor/Administrator)"


class HighSchoolStudentProfile(Profile):
    user = models.OneToOneField(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name='high_school_student_profile'
    )
    grade_level = models.IntegerField(null=True, blank=True, default=None)
    hours_used = models.FloatField(default=0.0) 
    organization = models.ForeignKey(
        'organizations.Organization',
        on_delete=models.CASCADE,
        related_name='high_school_students',
        null=True,
        blank=True,
        default=None
    )
    
    # Academic fields
    unweighted_gpa = models.FloatField(null=True, blank=True)
    weighted_gpa = models.FloatField(null=True, blank=True)
    sat_math_score = models.IntegerField(null=True, blank=True)
    sat_reading_score = models.IntegerField(null=True, blank=True)
    act_score = models.IntegerField(null=True, blank=True)
    high_school_name = models.CharField(max_length=255, null=True, blank=True)
    graduation_year = models.IntegerField(null=True, blank=True)
    
    # Preference fields
    intended_majors = models.JSONField(default=list)
    extracurriculars = models.CharField(max_length=255, null=True, blank=True)
    interests = models.CharField(max_length=255, null=True, blank=True)
    college_type_preferences = models.JSONField(default=list)
    location_type_preferences = models.JSONField(default=list)
    
    # Geographic preference fields
    GEOGRAPHIC_PREFERENCE_CHOICES = [
        ('states', 'States'),
        ('zip', 'ZIP Code'),
        ('none', 'None')
    ]
    geographic_preference_type = models.CharField(
        max_length=20, 
        choices=GEOGRAPHIC_PREFERENCE_CHOICES, 
        default='states'
    )
    preferred_states = models.JSONField(default=list)
    preferred_zip = models.CharField(max_length=10, null=True, blank=True)
    PREFERRED_RADIUS_CHOICES = [
        ('0-20', '0-20 miles (short drive)'),
        ('21-50', '21-50 miles (medium drive)'),
        ('51-200', '51-200 miles (long drive)'),
        ('201-500', '201-500 miles (short flight)'),
        ('501+', '501+ miles (long flight)'),
    ]
    preferred_radius = models.CharField(
        max_length=50,
        choices=PREFERRED_RADIUS_CHOICES,
        null=True,
        blank=True,
        help_text="Preferred radius from ZIP code"
    )
    
    # Demographic fields
    COUNTRY_CHOICES = [
        ('usa', 'USA'),
        ('other', 'Other')
    ]
    country = models.CharField(max_length=20, choices=COUNTRY_CHOICES, default='usa')
    current_zip_code = models.CharField(max_length=10, null=True, blank=True)
    GENDER_CHOICES = [
        ('female', 'Female'),
        ('male', 'Male'),
        ('non_binary', 'Non-binary'),
        ('other', 'Other'),
        ('prefer_not_to_say', 'Prefer not to say')
    ]
    gender = models.CharField(max_length=20, choices=GENDER_CHOICES, null=True, blank=True)
    
    ETHNICITY_CHOICES = [
        ('native_american', 'Native American or Alaska Native'),
        ('south_asian', 'South Asian'),
        ('east_asian', 'East Asian'),
        ('black', 'Black or African American'),
        ('hispanic', 'Hispanic or Latine'),
        ('middle_eastern', 'Middle Eastern or North African'),
        ('native_hawaiian', 'Native Hawaiian or Pacific Islander'),
        ('white', 'White'),
        ('multiracial', 'Multiracial'),
        ('other', 'Other'),
    ]
    ethnicity = models.CharField(max_length=50, choices=ETHNICITY_CHOICES, null=True, blank=True)
    
    INCOME_CHOICES = [
        ('under_30k', 'Under $30,000'),
        ('30k_48k', '$30,001-$48,000'),
        ('48k_75k', '$48,001-$75,000'),
        ('75k_100k', '$75,001-$100,000'),
        ('100k_150k', '$100,001 - $150,000'),
        ('150k_plus', '$150,001+'),
    ]
    household_income = models.CharField(max_length=50, choices=INCOME_CHOICES, null=True, blank=True)
    
    FINANCIAL_AID_CHOICES = [
        ('high', 'High need'),
        ('medium', 'Medium need'),
        ('low', 'Low need'),
        ('no_need', 'No financial aid need')
    ]
    financial_aid_need = models.CharField(max_length=20, choices=FINANCIAL_AID_CHOICES, null=True, blank=True)
    
    # Fields for ideal university matching
    ideal_university_description = models.TextField(
        null=True, 
        blank=True, 
        help_text="Generated description of the ideal university."
    )
    ideal_university_embedding = VectorField(
        dimensions=1536, 
        null=True, 
        blank=True, 
        help_text="Embedding vector for the ideal university description."
    )
    
    # Field to store university recommendations
    university_recommendations = JSONField(
        null=True, 
        blank=True,
        help_text="Stores categorized university recommendations (reach, target, safety)"
    )
    
    # Status of university recommendations
    RECOMMENDATIONS_STATUS_CHOICES = [
        ('not_requested', 'Not Requested'),
        ('pending', 'Pending'),
        ('ready', 'Ready'),
        ('error', 'Error'),
    ]
    recommendations_status = models.CharField(
        max_length=20,
        choices=RECOMMENDATIONS_STATUS_CHOICES,
        default='not_requested',
        help_text='Current status of university recommendations generation'
    )
    
    def __str__(self):
        return f"{self.user.first_name} {self.user.last_name} (High School Student)"


class Availability(models.Model):
    """
    Model representing a user's availability for mentorship sessions.
    """
    user = models.OneToOneField(User, on_delete=models.CASCADE, related_name='availability')
    time_zone = models.CharField(max_length=50)
    monday_available = models.BooleanField(default=False)
    tuesday_available = models.BooleanField(default=False)
    wednesday_available = models.BooleanField(default=False)
    thursday_available = models.BooleanField(default=False)
    friday_available = models.BooleanField(default=False)
    saturday_available = models.BooleanField(default=False)
    sunday_available = models.BooleanField(default=False)
    monday_time_ranges = models.JSONField(default=list, blank=True)
    tuesday_time_ranges = models.JSONField(default=list, blank=True)
    wednesday_time_ranges = models.JSONField(default=list, blank=True)
    thursday_time_ranges = models.JSONField(default=list, blank=True)
    friday_time_ranges = models.JSONField(default=list, blank=True)
    saturday_time_ranges = models.JSONField(default=list, blank=True)
    sunday_time_ranges = models.JSONField(default=list, blank=True)

    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"Availability for {self.user.email}"

    def clean(self):
        # Custom validation to ensure no overlapping time ranges per day
        import json
        from django.core.exceptions import ValidationError

        days = [
            'monday', 'tuesday', 'wednesday', 
            'thursday', 'friday', 'saturday', 'sunday'
        ]
        for day in days:
            if getattr(self, f"{day}_available"):
                time_ranges = getattr(self, f"{day}_time_ranges")
                sorted_ranges = sorted(time_ranges, key=lambda x: x['start_time'])
                for i in range(len(sorted_ranges) - 1):
                    current_end = sorted_ranges[i]['end_time']
                    next_start = sorted_ranges[i+1]['start_time']
                    if current_end > next_start:
                        raise ValidationError(f"Overlapping time ranges on {day.capitalize()}.")

    class Meta:
        unique_together = ('user', 'time_zone')
        ordering = ['user', 'time_zone']

    def get_time_ranges_for_date(self, requested_date, target_timezone=None):
        """
        Returns the time ranges for the given date as datetime objects, optionally converted to the target timezone.

        :param requested_date: A datetime.date object representing the date.
        :param target_timezone: (Optional) A string or pytz timezone object representing the target timezone.
        :return: A list of time ranges for the day as datetime objects.
        """

        weekday = requested_date.strftime('%A').lower()  # e.g., 'monday'
        available = getattr(self, f"{weekday}_available", False)
        if not available:
            return []

        time_ranges = getattr(self, f"{weekday}_time_ranges", [])

        user_tz = pytz.timezone(self.time_zone)
        target_tz = pytz.timezone(target_timezone) if target_timezone else user_tz

        converted_time_ranges = []
        for time_range in time_ranges:
            start_time_str = time_range['start_time']
            end_time_str = time_range['end_time']

            # Ensure only HH:MM is considered, ignoring seconds and milliseconds
            start_time_str = start_time_str.split(':')[:2]
            start_time_str = ':'.join(start_time_str)
            end_time_str = end_time_str.split(':')[:2]
            end_time_str = ':'.join(end_time_str)

            # Parse the time strings and combine with the requested date
            start_time = datetime.strptime(start_time_str, '%H:%M').time()
            end_time = datetime.strptime(end_time_str, '%H:%M').time()

            start_datetime = datetime.combine(requested_date, start_time)
            end_datetime = datetime.combine(requested_date, end_time)

            # Handle overnight times (e.g., end time is past midnight)
            if end_datetime <= start_datetime:
                end_datetime += timedelta(days=1)

            # Localize to user's timezone
            start_datetime = user_tz.localize(start_datetime)
            end_datetime = user_tz.localize(end_datetime)

            # Convert to target timezone if necessary
            start_datetime = start_datetime.astimezone(target_tz)
            end_datetime = end_datetime.astimezone(target_tz)

            converted_time_ranges.append({
                'start_time': start_datetime,
                'end_time': end_datetime,
            })

        return converted_time_ranges


class OnboardingProgress(models.Model):
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='onboarding_progress')
    steps_completed = models.JSONField(default=list)  # e.g., ["organization", "education"]

    def __str__(self):
        return f"OnboardingProgress for {self.user.email}"


@receiver(post_save, sender=settings.AUTH_USER_MODEL)
def create_auth_token_and_profile(
    sender, instance=None, created=False, **kwargs
):
    """
    Create a token and specific profile for the user when a new user is created.
    """
    if created:
        Token.objects.get_or_create(user=instance)
        if instance.user_type == User.COLLEGE_STUDENT_TYPE:
            config = Configuration.objects.first()
            is_waitlisted = config.use_waitlist if config else False
            CollegeStudentProfile.objects.create(user=instance, is_waitlisted=is_waitlisted)
        elif instance.user_type == User.HIGH_SCHOOL_STUDENT_TYPE:
            HighSchoolStudentProfile.objects.create(user=instance)
        elif instance.user_type == User.COUNSELOR_ADMINISTRATOR_TYPE:
            CounselorAdministratorProfile.objects.create(user=instance)