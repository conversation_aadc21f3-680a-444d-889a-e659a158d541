from django.urls import path, include
from rest_framework.routers import DefaultRouter
from api.bookings.views import BookingViewSet, BookingAvailabilityViewSet, TrailblazerBookingViewSet, TrailblazerBookingReminderView

router = DefaultRouter()
router.register(r'bookings', BookingViewSet, basename='booking')
router.register(r'availabilities', BookingAvailabilityViewSet, basename='booking-availability')
router.register(r'trailblazer', TrailblazerBookingViewSet, basename='booking-trailblazer')

urlpatterns = [
    path('', include(router.urls)),
    path('reminders/', TrailblazerBookingReminderView.as_view(), name='trailblazer-booking-reminders'),
]