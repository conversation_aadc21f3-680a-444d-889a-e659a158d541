import { useState, useMemo } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import { AvatarSelection } from "@/components/ui/avatarSelection"
import { FormSection } from './formSection'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from "@/components/ui/select"


const formSchema = z.object({
    firstName: z.string().min(2, "First name must be at least 2 characters"),
    lastName: z.string().min(2, "Last name must be at least 2 characters"),
    avatar: z.string(),
    schoolYear: z.string({ required_error: "Grade level is required" }),
})

export const HighSchoolStudentProfileForm = ({ userData, onSubmit }) => {
    const [selectedAvatar, setSelectedAvatar] = useState(userData.profile.avatar) // Initialize with current avatar

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            firstName: userData.first_name,
            lastName: userData.last_name,
            avatar: "",
            schoolYear: String(userData.profile.grade_level)
        }
    })

    const schoolYearOptions = useMemo(() => {
        const currentYear = new Date().getFullYear()
        const years = Array.from({ length: 9 }, (_, i) => currentYear + i)
        return years.map((year) => ({ value: year.toString(), label: year.toString() }))
    }, [])

    return (
        <Form {...form} >
            <form id="profile-form" onSubmit={form.handleSubmit(onSubmit)} className="flex-1 w-full md:w-3/4 lg:w-2/3">
                <FormSection title="About You">
                    <FormField
                        control={form.control}
                        name="firstName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>First Name*</FormLabel>
                                <FormControl>
                                    <Input {...field} disabled />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="lastName"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Last Name*</FormLabel>
                                <FormControl>
                                    <Input {...field} disabled />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="schoolYear"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>What year will you graduate high school?</FormLabel>
                                <Select onValueChange={field.onChange} defaultValue={field.value}>
                                    <FormControl>
                                        <SelectTrigger>
                                            <SelectValue placeholder="Select a school year" />
                                        </SelectTrigger>
                                    </FormControl>
                                    <SelectContent>
                                        {schoolYearOptions.map((option) => (
                                            <SelectItem key={option.value} value={option.value}>
                                                {option.label}
                                            </SelectItem>
                                        ))}
                                    </SelectContent>
                                </Select>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                    <FormField
                        control={form.control}
                        name="avatar"
                        render={({ field }) => (
                            <FormItem>
                                <FormLabel>Pick an avatar</FormLabel>
                                <FormControl>
                                    <AvatarSelection
                                        onSelect={(avatar) => {
                                            setSelectedAvatar(avatar)
                                            field.onChange(avatar)
                                        }}
                                        selectedAvatar={selectedAvatar}
                                        initialAvatarUrl={userData.profile.avatar}
                                    />
                                </FormControl>
                                <FormMessage />
                            </FormItem>
                        )}
                    />
                </FormSection>
            </form>
        </Form>
    )
}