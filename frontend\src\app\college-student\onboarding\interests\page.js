"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import * as z from 'zod'
import { OnboardingLayout } from "@/components/ui/onboardingLayout"
import { StepProgressDisplay } from "@/components/ui/stepProgressDisplay"
import { Button } from "@/components/ui/button"
import { Form, FormField, FormItem, FormLabel, FormControl, FormMessage } from "@/components/ui/form"
import { ArrowRight } from 'lucide-react'
import { useToast } from "@/hooks/use-toast"
import { getNextOnboardingStep } from '@/lib/utils'
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';
import { MajorSelect } from "@/components/ui/majorSelect"
import { InterestPicker } from '@/components/ui/interestPicker'
import { Loader2 } from 'lucide-react'

// Form schema
const formSchema = z.object({
    major: z.string().refine((s) => { return s.length > 0 }, "Please select a major"),
    interests: z.array(z.string()).min(1, "Please select at least one interest").max(3, "Please select no more than 3 interests"),
})

// NextButton component
const NextButton = ({ isValid, loading }) => (
    <Button
        type="submit"
        className="mt-8"
        disabled={loading}
    >
        {loading && <Loader2 className="mr-2 w-6 h-6 animate-spin" />}
        Next
        <ArrowRight className="ml-2 w-6 h-6" />
    </Button>
)

// Main InterestsPage component
const InterestsPage = () => {
    const router = useRouter()
    const { toast } = useToast()
    const { getToken } = useAuth()
    const [isLoading, setIsLoading] = useState(false)

    const form = useForm({
        resolver: zodResolver(formSchema),
        defaultValues: {
            major: "",
            interests: [],
        },
    })

    const onSubmit = async (data) => {
        setIsLoading(true)
        const authToken = getToken()
        try {
            const response = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/users/college-students/onboarding/`, {
                method: 'PATCH',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Token ${authToken}`,
                },
                body: JSON.stringify({
                    step: "interests",
                    college_major: data.major,
                    interests: data.interests,
                }),
            })
            if (response.ok) {
                toast({ 
                    title: 'Success!', 
                    description: 'Profile information uploaded successfully' 
                })
                router.push(getNextOnboardingStep('CollegeStudent', 'interests'))
            } else {
                console.error('Error Response:', response)
                toast({ 
                    variant: 'destructive',
                    description: result.detail || 'Failed to upload profile information' 
                })
                setIsLoading(false)
            }
        } catch (error) {
            console.error('Error:', error)
            toast({ 
                variant: 'destructive',
                description: 'An unexpected error occurred' 
            })
            setIsLoading(false)
        }
    }

    return (
        <OnboardingLayout>
            <div className="w-full">
                <div className="py-10">
                    <StepProgressDisplay currentStep={3} totalSteps={6} />
                </div>
                <div className="w-full md:w-5/6 lg:w-5/6">
                    <h1 className="text-4xl font-bold mb-14">Tell us about your interests</h1>
                    <Form {...form}>
                        <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
                            <FormField
                                control={form.control}
                                name="major"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>What&apos;s your current or most likely major?*</FormLabel>
                                        <FormControl>
                                            <MajorSelect field={field} form={form} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <FormField
                                control={form.control}
                                name="interests"
                                render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>What are some of your professional interests? (Select up to 3)</FormLabel>
                                        <FormControl>
                                            <InterestPicker field={field} />
                                        </FormControl>
                                        <FormMessage />
                                    </FormItem>
                                )}
                            />
                            <div>
                                <NextButton
                                    isValid={form.formState.isValid}
                                    loading={isLoading}
                                />
                            </div>
                        </form>
                    </Form>
                </div>
            </div>
        </OnboardingLayout>
    )
}

export default withAuth(InterestsPage)
