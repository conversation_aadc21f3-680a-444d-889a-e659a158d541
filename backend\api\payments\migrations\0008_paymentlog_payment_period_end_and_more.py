# Generated by Django 4.2.13 on 2025-02-23 20:07

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('payments', '0007_rename_reason_skippedpaymentlog_error_message_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='paymentlog',
            name='payment_period_end',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='paymentlog',
            name='payment_period_start',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='hours',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='payment_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='payment_period_end',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='payment_period_start',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AddField(
            model_name='skippedpaymentlog',
            name='payment_status',
            field=models.CharField(blank=True, max_length=1000, null=True),
        ),
        migrations.AlterField(
            model_name='paymentlog',
            name='hours',
            field=models.FloatField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='paymentlog',
            name='payment_date',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='paymentperiodtrailblazerhours',
            name='id',
            field=models.BigAutoField(primary_key=True, serialize=False),
        ),
        migrations.AlterField(
            model_name='paymentperiodtrailblazerhours',
            name='payment_period_end',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='paymentperiodtrailblazerhours',
            name='payment_period_start',
            field=models.DateTimeField(blank=True, null=True),
        ),
        migrations.AlterField(
            model_name='paymentperiodtrailblazerhours',
            name='trailblazer',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='payment_period_trailblazer_hours', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AlterField(
            model_name='skippedpaymentlog',
            name='id',
            field=models.BigAutoField(primary_key=True, serialize=False),
        ),
    ]
