"use client"

import { usePathname } from 'next/navigation'
import { MainLayout } from "@/components/ui/mainLayout"
import TrailblazerProfileView from "@/components/ui/TrailblazerProfileView"
import withAuth from '@/hoc/withAuth';
import { useAuth } from '@/context/AuthProvider';


const TrailblazerProfile = () => {
    const { user, loadingUser: userLoading, error: userError } = useAuth()
    const pathname = usePathname()
    const trailblazerId = pathname.split('/').pop()

    return (
        <MainLayout user={user} displaySidebarMenu={false}>
            <div className="container mx-auto px-4 pt-0 pb-8 md:pt-8">
                {trailblazerId && <TrailblazerProfileView trailblazerIds={[trailblazerId]} />}
            </div>
        </MainLayout>
    )
}

export default withAuth(TrailblazerProfile)
