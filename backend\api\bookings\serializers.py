from rest_framework import serializers

from django.contrib.auth import get_user_model
from django.utils import timezone
from datetime import timedelta, date

from api.bookings.models import Booking, TrailblazerBookingStatus
from api.users.models import CollegeStudentProfile, HighSchoolStudentProfile, CounselorAdministratorProfile

User = get_user_model()


class TrailblazerBookingStatusUpdateSerializer(serializers.ModelSerializer):
    meet_link = serializers.SerializerMethodField()

    class Meta:
        model = TrailblazerBookingStatus
        fields = ['status', 'decline_reason', 'meet_link']

    def validate_status(self, value):
        current_status = self.instance.status
        if current_status == 'pending' and value not in ['confirmed', 'declined']:
            raise serializers.ValidationError("Pending bookings can only be confirmed or declined.")
        if current_status == 'confirmed' and value != 'declined':
            raise serializers.ValidationError("Confirmed bookings can only be declined.")
        return value

    def validate_decline_reason(self, value):
        # Ensure decline_reason is provided only when status is 'declined'
        if self.initial_data.get('status') == 'declined' and not value:
            raise serializers.ValidationError("Decline reason is required when status is declined.")
        return value

    def update(self, instance, validated_data):
        instance.status = validated_data.get('status', instance.status)
        instance.decline_reason = validated_data.get('decline_reason', instance.decline_reason)
        instance.save()
        return instance

    def get_meet_link(self, obj):
        """
        Return the meet_link of the related booking after signals have executed.
        """
        return obj.booking.meet_link


class TrailblazerBookingStatusSerializer(serializers.ModelSerializer):
    class Meta:
        model = TrailblazerBookingStatus
        fields = ['id', 'booking', 'trailblazer', 'status', 'updated_at']
        read_only_fields = ['id', 'updated_at']

class BookingSerializer(serializers.ModelSerializer):
    # Accepting trailblazers as input for creating a booking
    trailblazers = serializers.ListField(
        child=serializers.PrimaryKeyRelatedField(queryset=User.objects.all()),
        write_only=True
    )
    creator_status = serializers.ChoiceField(choices=Booking.CREATOR_STATUS_CHOICES, default='confirmed')
    creator_status_updated_at = serializers.DateTimeField(read_only=True)
    is_historical = serializers.SerializerMethodField()
    status = serializers.ReadOnlyField()
    current_trailblazer_status = serializers.SerializerMethodField()
    booked_by = serializers.SerializerMethodField()

    class Meta:
        model = Booking
        fields = [
            'id',
            'trailblazers',
            'booked_by',
            'start_time',
            'end_time',
            'message',
            'number_of_students',
            'creator_status',
            'creator_status_updated_at', 
            'created_at',
            'status',
            'current_trailblazer_status',
            'is_historical',
            'meet_link',
            'creator_timezone',
            'creator_cancel_reason', 
            'proposed_times', 
            'proposed_time_confirmed'
        ]
        read_only_fields = [
            'id',
            'booked_by',
            'end_time',
            'creator_status_updated_at',
            'created_at',
            'status',
            'current_trailblazer_status',
            'meet_link'
        ]

    def get_current_trailblazer_status(self, obj):
        return obj.get_current_trailblazer_status(self.context['request'].user)

    def get_profile(self, obj):
        if obj.user_type == User.COLLEGE_STUDENT_TYPE:
            return {
                'university': obj.profile.university,
                'high_school_name': obj.profile.high_school_name,
                'high_school_zip_code': obj.profile.high_school_zip_code,
                'high_school_city': obj.profile.high_school_city,
                'high_school_state': obj.profile.high_school_state,
                'college_major': obj.profile.college_major
            }
        elif obj.user_type == User.HIGH_SCHOOL_STUDENT_TYPE:
            return {
                'organization': {
                    'name': obj.profile.organization.name,
                    'zip_code': obj.profile.organization.zip_code,
                    'city': obj.profile.organization.city,
                    'state': obj.profile.organization.state,
                    'school_district': obj.profile.organization.school_district
                },
                'grade_level': obj.profile.grade_level
            }
        elif obj.user_type == User.COUNSELOR_ADMINISTRATOR_TYPE:
            return {
                'organization': {
                    'name': obj.profile.organization.name,
                    'zip_code': obj.profile.organization.zip_code,
                    'city': obj.profile.organization.city,
                    'state': obj.profile.organization.state,
                    'school_district': obj.profile.organization.school_district
                },
                'position': obj.profile.position
            }
        return None

    def get_booked_by(self, obj):
        if obj.booked_by:
            return {
                'id': obj.booked_by.id,
                'first_name': obj.booked_by.first_name,
                'last_name': obj.booked_by.last_name,
                'email': obj.booked_by.email,
                'user_type': obj.booked_by.user_type,
                'profile': self.get_profile(obj.booked_by)
            }
        return None

    def to_representation(self, instance):
        """Customize the representation of the trailblazers field when reading data."""
        ret = super().to_representation(instance)
        # Get trailblazers with their associated status information
        ret['trailblazers'] = [
            {
                'id': status.trailblazer.id,
                'first_name': status.trailblazer.first_name,
                'last_name': status.trailblazer.last_name,
                'email': status.trailblazer.email,
                'updated_at': status.updated_at,
                'status': status.status,
                'profile': self.get_profile(status.trailblazer)
            }
            for status in instance.trailblazer_statuses.all()
        ]
        return ret

    def create(self, validated_data):
        # Pop trailblazers data from validated data
        trailblazers = validated_data.pop('trailblazers', [])
        booking = super().create(validated_data)

        # Create TrailblazerBookingStatus entries for each trailblazer
        for trailblazer in trailblazers:
            TrailblazerBookingStatus.objects.create(
                booking=booking,
                trailblazer=trailblazer,  # Using the user instance directly
                status='pending'  # Default status or you may use other logic to determine it
            )
        
        return booking

    def get_is_historical(self, obj):
        """Determine if the booking is upcoming or historical."""
        current_time = timezone.now()
        return True if obj.end_time < current_time else False

    def validate_trailblazers(self, value):
        if len(value) > 3:
            raise serializers.ValidationError("A booking can have up to three Trailblazers.")
        return value

    # def validate_start_time(self, value):
    #     if value < timezone.now() + timedelta(hours=72):
    #         raise serializers.ValidationError("Session must be booked at least 72 hours in advance.")
    #     return value

    def validate(self, data):
        trailblazers = data.get('trailblazers', [])
        start_time = data.get('start_time') or (self.instance.start_time if self.instance else None)

        # Only perform the overlap check if start_time is available
        if start_time:
            end_time = start_time + timedelta(hours=1)  # Assuming default duration

            for trailblazer in trailblazers:
                overlapping = Booking.objects.filter(
                    trailblazer_statuses__trailblazer=trailblazer,
                    start_time__lt=end_time,
                    end_time__gt=start_time, 
                    creator_status='confirmed',
                    trailblazer_statuses__status__in=['confirmed', 'pending']

                ).exclude(id=self.instance.id if self.instance else None)
                if overlapping.exists():
                    raise serializers.ValidationError(f"Time slot is already booked for Trailblazer {trailblazer.email}.")

        return data

class TrailblazerBookingStatusReminderSerializer(serializers.ModelSerializer):
    trailblazer_name = serializers.SerializerMethodField()
    trailblazer_account_email = serializers.SerializerMethodField()
    trailblazer_timezone = serializers.SerializerMethodField()
    creator_name = serializers.SerializerMethodField()
    start_time = serializers.SerializerMethodField()
    end_time = serializers.SerializerMethodField()
    session_type = serializers.SerializerMethodField()
    message = serializers.SerializerMethodField()
    organization = serializers.SerializerMethodField()

    class Meta:
        model = TrailblazerBookingStatus
        fields = ['trailblazer_name', 'trailblazer_account_email', 'trailblazer_timezone', 'status',
            'creator_name', 'session_type', 'message', 'start_time', 'end_time', 'organization'
        ]
        read_only_fields = ['id', 'updated_at']

    def get_trailblazer_account_email(self, obj):
        return obj.trailblazer.email
    
    def get_trailblazer_name(self, obj):
        return f"{obj.trailblazer.first_name} {obj.trailblazer.last_name}"
    
    def get_trailblazer_timezone(self, obj):
        return obj.trailblazer.availability.time_zone if obj.trailblazer.availability else None
    
    def get_creator_name(self, obj):
        return f"{obj.booking.booked_by.first_name } {obj.booking.booked_by.last_name}" if obj.booking.booked_by else None

    def get_start_time(self, obj):
        return obj.booking.start_time

    def get_end_time(self, obj):
        return obj.booking.end_time
    
    def get_session_type(self, obj):
        return "Group Session" if obj.booking.trailblazers.count() > 1 else "Individual Session"
    
    def get_message(self, obj):
        return obj.booking.message
    
    def get_organization(self, obj):
        return obj.booking.booked_by.profile.organization.name if obj.booking.booked_by.profile.organization else ''
        

class AvailableDatesSerializer(serializers.Serializer):
    available_dates = serializers.ListField(
        child=serializers.DateField()
    )

class AvailableTimeSlotSerializer(serializers.Serializer):
    start = serializers.TimeField(format='%H:%M', input_formats=['%H:%M'])
    end = serializers.TimeField(format='%H:%M', input_formats=['%H:%M'])

    def validate(self, data):
        start = datetime.combine(date.today(), data['start'])
        end = datetime.combine(date.today(), data['end'])
        if (end - start) != timedelta(hours=1):
            raise serializers.ValidationError("Each time slot must be exactly 1 hour long.")
        return data


class AvailableTimesSerializer(serializers.Serializer):
    # User's local timezone
    time_zone = serializers.CharField()
    # List of available 1-hour time slots
    available_time_slots = AvailableTimeSlotSerializer(many=True)


class ImpactStatisticsSerializer(serializers.Serializer):
    total_hours = serializers.FloatField()
    total_schools_connected = serializers.IntegerField()
