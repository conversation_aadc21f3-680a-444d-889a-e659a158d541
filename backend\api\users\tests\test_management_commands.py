from django.core.management import call_command, CommandError
from django.test import TestCase
from django.contrib.auth import get_user_model
from api.users.models import CollegeStudentProfile
from api.organizations.models import Organization

User = get_user_model()

class GenerateFakeTrailblazersCommandTest(TestCase):
    def setUp(self):
        self.command = 'generate_fake_trailblazers'

    def test_generate_default_number_of_profiles(self):
        call_command(self.command)
        self.assertEqual(CollegeStudentProfile.objects.count(), 10)
        self.assertEqual(Organization.objects.count(), 3)

    def test_generate_specific_number_of_profiles(self):
        call_command(self.command, 5)
        self.assertEqual(CollegeStudentProfile.objects.count(), 5)
        self.assertEqual(Organization.objects.count(), 3)

    def test_emails_end_with_edu(self):
        call_command(self.command, 10)
        profiles = CollegeStudentProfile.objects.all()
        for profile in profiles:
            self.assertTrue(profile.user.email.endswith('.edu'))

    def test_profiles_approved_status(self):
        call_command(self.command, 10)
        profiles = CollegeStudentProfile.objects.all()
        for profile in profiles:
            self.assertIsNotNone(profile.background_check_passed_at)

    def test_handle_negative_number_of_profiles(self):
        with self.assertRaises(CommandError):
            call_command(self.command, -5)

    def test_handle_non_integer_input(self):
        with self.assertRaises(CommandError):
            call_command(self.command, 'ten')

    def test_unique_emails(self):
        call_command(self.command, 10)
        emails = CollegeStudentProfile.objects.values_list('user__email', flat=True)
        self.assertEqual(len(emails), len(set(emails)))
