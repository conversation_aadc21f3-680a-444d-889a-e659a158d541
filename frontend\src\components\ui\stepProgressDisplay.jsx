

export const StepProgressDisplay = ({ currentStep, totalSteps }) => {
    return (
      <div className="flex w-5/6 space-x-2 text-gray-500 text-sm align-start mb-8">
        {
          Array.from({ length: totalSteps }, (_, i) => (
            <div
              key={i}
              className={`grow h-2 rounded-full flex items-center justify-center ${i === currentStep - 1 ? "bg-primary" : "bg-[#E8E8EA]"}`}
            />
          ))
        }
      </div>
    )
}