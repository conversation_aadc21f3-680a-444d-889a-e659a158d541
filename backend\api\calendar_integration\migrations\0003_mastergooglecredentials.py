# Generated by Django 4.2.13 on 2024-11-05 19:29

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('calendar_integration', '0002_googleauthcredentials_delete_calendarcredentials'),
    ]

    operations = [
        migrations.CreateModel(
            name='MasterGoogleCredentials',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('token', models.TextField()),
                ('refresh_token', models.TextField()),
                ('token_uri', models.TextField()),
                ('client_id', models.TextField()),
                ('client_secret', models.TextField()),
                ('scopes', models.TextField()),
                ('expiry', models.DateTimeField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Master Google Credentials',
                'verbose_name_plural': 'Master Google Credentials',
            },
        ),
    ]
