from datetime import datetime, timedelta, time
import pytz

from django.utils import timezone
from django.db.models import Q

from rest_framework import viewsets, status
from rest_framework.views import APIView
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework import serializers

from api.users.serializers import AvailabilitySerializer
from api.bookings.serializers import BookingSerializer, AvailableDatesSerializer, AvailableTimesSerializer
from api.bookings.serializers import ImpactStatisticsSerializer
from api.bookings.serializers import TrailblazerBookingStatusUpdateSerializer, TrailblazerBookingStatusSerializer, TrailblazerBookingStatusReminderSerializer

from api.bookings.permissions import <PERSON>O<PERSON>er<PERSON>rAssociated, IsTrailblazer, IsAssociatedTrailblazer

from api.organizations.models import Organization
from api.bookings.models import Booking, TrailblazerBookingStatus
from api.users.models import User, Availability

from .services.email_service import BookingEmailService

from api.authentication import APIKeyAuthentication
from api.permissions import HasAPIKey

class TrailblazerBookingReminderView(APIView):
    """
    A view to fetch trailblazer booking statuses that need email reminders.
    """
    authentication_classes = [APIKeyAuthentication]
    permission_classes = [HasAPIKey]

    def get(self, request):
        # Filter trailblazer booking statuses that need email reminders (e.g., not confirmed)
        bookings_status = request.query_params.get('bookings_status')
        current_time = timezone.now()
        statuses = TrailblazerBookingStatus.objects.filter(
            status=bookings_status,
            booking__start_time__gte=current_time,  # Exclude bookings whose time has passed
            booking__creator_status='confirmed'
        ).select_related('booking', 'trailblazer')
        serializer = TrailblazerBookingStatusReminderSerializer(statuses, many=True, context={'request': request})
        return Response(serializer.data)
    
class BookingViewSet(viewsets.ModelViewSet):
    """
    A viewset for creating and managing bookings by the user who booked them.
    """
    queryset = Booking.objects.all()
    serializer_class = BookingSerializer
    permission_classes = [IsOwnerOrAssociated]

    def perform_create(self, serializer):
        user = self.request.user

        # Check if the user is a high school student
        if user.user_type == 'HighSchoolStudent':
            organization = user.profile.organization

            # Ensure the organization exists
            if not organization:
                raise serializers.ValidationError({"detail": "You are not associated with an organization."})

            # Check if the organization's total subscribed hours are exhausted
            if organization.total_hours_used >= organization.total_subscribed_hours:
                raise serializers.ValidationError(
                    {"detail": "Your organization has no available hours left.", "code": "ORG_NO_HOURS"}
                )

            # Check if the student has exceeded their individual cap
            if user.profile.hours_used >= organization.per_student_hour_cap:
                raise serializers.ValidationError(
                    {"detail": "You have reached your maximum allowed hours.", "code": "STUDENT_MAX_HOURS"}
                )

            # Save the booking
            booking = serializer.save(booked_by=user)

       
            # Calculate the hours to deduct
            hours_to_deduct = booking.duration.total_seconds() / 3600

            # Update the organization's total hours used
            organization.total_hours_used += hours_to_deduct
            organization.save()

            # Update the student's hours used
            user.profile.hours_used += hours_to_deduct
            user.profile.save()
        
        # Group meeting logic for counselors
        elif user.user_type == 'CounselorAdministrator':
            organization = user.profile.organization
            if not organization:
                raise serializers.ValidationError({"detail": "You are not associated with an organization."})

            trailblazer_count = len(serializer.validated_data.get('trailblazers', []))
            if trailblazer_count == 0:
                raise serializers.ValidationError({"detail": "At least one trailblazer is required."})

            hours_to_deduct = trailblazer_count  # 1 hour per trailblazer

            if organization.total_hours_used + hours_to_deduct > organization.total_subscribed_hours:
                raise serializers.ValidationError({"detail": "Not enough hours available for this group meeting."})

            booking = serializer.save(booked_by=user)

            organization.total_hours_used += hours_to_deduct
            organization.save()

        # Send the booking email
        BookingEmailService.send_session_request_email(booking)

    def partial_update(self, request, *args, **kwargs):
        booking = self.get_object()
        user = request.user

        # Handle cancellation by the creator
        if user.user_type == 'HighSchoolStudent' and request.data.get('creator_status') == 'cancelled':
            if booking.creator_status == 'cancelled' or booking.status == 'cancelled':
                raise serializers.ValidationError({"detail": "Booking has already been canceled."})

            if user.profile.organization:
                # Refund hours to the organization
                hours_to_refund = booking.duration.total_seconds() / 3600
                user.profile.organization.total_hours_used -= hours_to_refund
                user.profile.organization.save()

                # Refund hours to the student's profile
                user.profile.hours_used -= hours_to_refund
                user.profile.save()

            # Update the creator's status to 'cancelled'
            booking.creator_status = 'cancelled'
            booking.save()

        # Handle cancellation by counselor administrators
        if user.user_type == 'CounselorAdministrator' and request.data.get('creator_status') == 'cancelled':
            if booking.creator_status == 'cancelled' or booking.status == 'cancelled':
                raise serializers.ValidationError({"detail": "Booking has already been canceled."})

            organization = user.profile.organization
            if organization:
                # Count only trailblazers who have NOT declined
                active_trailblazers = TrailblazerBookingStatus.objects.filter(
                    booking=booking,
                ).exclude(status='declined').count()
                organization.total_hours_used = max(0, organization.total_hours_used - active_trailblazers)
                organization.save()

            booking.creator_status = 'cancelled'
            booking.save()

        return super().partial_update(request, *args, **kwargs)

    def get_queryset(self):
        return Booking.objects.filter(booked_by=self.request.user).order_by('start_time')

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsOwnerOrAssociated])
    def confirm_proposed_time(self, request, pk=None):
        booking = self.get_object()
        start_time = request.data.get('start_time')
        creator_timezone = request.data.get('creator_timezone')

        if not start_time:
            return Response({"detail": "start time is required."}, status=status.HTTP_400_BAD_REQUEST)

        # Prepare data for creating a new booking
        new_booking_data = {
            'trailblazers': [trailblazer.id for trailblazer in booking.trailblazers.all()],
            'start_time': start_time,
            'message': booking.message,
            'number_of_students': booking.number_of_students,
            'creator_timezone': creator_timezone,
            'creator_status': 'confirmed'
        }

        booking.proposed_time_confirmed = True
        booking.save()

        # Use the BookingSerializer to create a new booking
        serializer = self.get_serializer(data=new_booking_data)
        serializer.is_valid(raise_exception=True)
        new_booking = serializer.save(booked_by=request.user)

        # Update the trailblazer status to "confirmed"
        for trailblazer_status in new_booking.trailblazer_statuses.all():
            trailblazer_status.status = 'confirmed'
            trailblazer_status.save()

        return Response(serializer.data, status=status.HTTP_201_CREATED)

class TrailblazerBookingViewSet(viewsets.ModelViewSet):
    """
    A viewset for trailblazers to view and manage their bookings.
    """
    serializer_class = BookingSerializer
    permission_classes = [IsAuthenticated, IsTrailblazer]

    def get_queryset(self):
        # General queryset with all bookings, no specific filtering here
        return Booking.objects.all()

    def list(self, request, *args, **kwargs):
        """
        List bookings associated with the authenticated trailblazer,
        including pagination.
        """
        queryset = Booking.objects.filter(
            trailblazer_statuses__trailblazer=request.user
        ).distinct().order_by('start_time')

        # Apply pagination
        page = self.paginate_queryset(queryset)
        if page is not None:
            serializer = self.get_serializer(page, many=True)
            return self.get_paginated_response(serializer.data)

        # Return the response without pagination if not applicable
        serializer = self.get_serializer(queryset, many=True)
        return Response(serializer.data)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsTrailblazer, IsAssociatedTrailblazer])
    def propose_times(self, request, pk=None):
        """
        Propose new times for a booking.
        """
        booking = self.get_object()
        proposed_times = request.data.get('proposed_times', [])
        
        if not proposed_times:
            return Response({"detail": "No proposed times provided."}, status=status.HTTP_400_BAD_REQUEST)
        
        if booking.proposed_times is None:
            booking.proposed_times = []

        # Clear existing proposed times
        booking.proposed_times.clear()

        #print(proposed_times, "proposed_times IN DB")
        booking.proposed_times.extend(proposed_times)
        booking.save()
        BookingEmailService.send_proposed_times_email(booking, proposed_times)
        
        return Response({"detail": "Proposed times added successfully."}, status=status.HTTP_200_OK)

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsTrailblazer, IsAssociatedTrailblazer])
    def remove_proposed_time(self, request, pk=None):
        """
        Remove a proposed time from a booking.
        """
        booking = self.get_object()
        start_time_to_remove = request.data.get('start_time')
        print(start_time_to_remove, "start_time_to_remove")

        if not start_time_to_remove:
            return Response({"detail": "No start time provided."}, status=status.HTTP_400_BAD_REQUEST)

        # Remove the proposed time
        booking.proposed_times = [time for time in booking.proposed_times if time.get("start_time") != start_time_to_remove]
        booking.save()
        
        return Response({"detail": "Proposed time removed successfully."}, status=status.HTTP_200_OK)
   

    @action(detail=True, methods=['post'], permission_classes=[IsAuthenticated, IsTrailblazer, IsAssociatedTrailblazer])
    def update_status(self, request, pk=None):
        """
        Update the status of a booking for the authenticated trailblazer.
        """
        booking = self.get_object()
        trailblazer = request.user
        new_status = request.data.get('status')
        decline_reason = request.data.get('decline_reason')  # Get the optional decline_reason

        # Ensure 'status' is provided
        if not new_status:
            return Response({"detail": "'status' is required."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            status_obj = TrailblazerBookingStatus.objects.get(booking=booking, trailblazer=trailblazer)
        except TrailblazerBookingStatus.DoesNotExist:
            return Response(
                {"detail": "TrailblazerBookingStatus not found."},
                status=status.HTTP_404_NOT_FOUND
            )

        # Update the trailblazer's status
        serializer = TrailblazerBookingStatusUpdateSerializer(
            status_obj, data={'status': new_status, 'decline_reason': decline_reason}, partial=True
        )
        if serializer.is_valid():
            serializer.save()

            # Check if the booking was created by a high school student
            if (
                booking.booked_by.user_type == 'HighSchoolStudent' and
                booking.trailblazers.count() == 1 and  # Only one trailblazer attached
                new_status == 'declined' and  # Trailblazer is canceling
                booking.creator_status != 'cancelled'  # High school student hasn't already cancelled
            ):
                # Refund hours to the organization and the student
                hours_to_refund = booking.duration.total_seconds() / 3600
                organization = booking.booked_by.profile.organization
                if organization:
                    # Refund hours to the organization's total_hours_used
                    organization.total_hours_used -= hours_to_refund
                    organization.save()

                    # Refund hours to the student's hours_used
                    booking.booked_by.profile.hours_used -= hours_to_refund
                    booking.booked_by.profile.save()

            # Check if the booking was created by a counselor administrator
            if (
                booking.booked_by.user_type == 'CounselorAdministrator' and
                new_status == 'declined'
            ):
                organization = booking.booked_by.profile.organization
                if organization:
                    organization.total_hours_used = max(0, organization.total_hours_used - 1)
                    organization.save()

            return Response(serializer.data, status=status.HTTP_200_OK)

        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'], url_path='impact-statistics', permission_classes=[IsAuthenticated])
    def impact_statistics(self, request):
        # Filter bookings with 'completed' status
        completed_bookings = Booking.objects.filter(
            trailblazer_statuses__trailblazer=request.user,
            end_time__lt=timezone.now(),
            creator_status='confirmed',
            trailblazer_statuses__status='confirmed'
        ).distinct()

        # Calculate total hours
        total_hours = 0
        for booking in completed_bookings:
            duration = (booking.end_time - booking.start_time).total_seconds() / 3600
            if duration > 0:
                total_hours += duration
        total_hours = round(total_hours)

        # Identify unique schools
        unique_schools = Organization.objects.filter(
            Q(high_school_students__user__user_bookings__in=completed_bookings) |
            Q(counselors_administrators__user__user_bookings__in=completed_bookings)
        ).distinct().count()

        data = {
            'total_hours': total_hours,
            'total_schools_connected': unique_schools
        }

        serializer = ImpactStatisticsSerializer(data)
        return Response(serializer.data, status=status.HTTP_200_OK)

class BookingAvailabilityViewSet(viewsets.ModelViewSet):

    queryset = Availability.objects.all()
    serializer_class = AvailabilitySerializer
    
    @action(detail=False, methods=['get'], url_path='student-availability')
    def retrieve_student_days_availability(self, request):
        student_ids = request.query_params.getlist('student_ids')
        start_date_str = request.query_params.get('start_date')
        end_date_str = request.query_params.get('end_date')
        
        # Validate input parameters
        if not student_ids or not start_date_str or not end_date_str:
            return Response({"available_dates": []}, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            start_date = datetime.strptime(start_date_str, '%Y-%m-%d').date()
            end_date = datetime.strptime(end_date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({"available_dates": []}, status=status.HTTP_400_BAD_REQUEST)
        
        if start_date > end_date:
            return Response({"available_dates": []}, status=status.HTTP_400_BAD_REQUEST)
        
        students = User.objects.filter(id__in=student_ids)
        if students.count() != len(student_ids):
            return Response({"available_dates": []}, status=status.HTTP_400_BAD_REQUEST)
        
        available_dates = set()
        current_date = start_date
        while current_date <= end_date:
            weekday = current_date.strftime('%A').lower()  # e.g., 'monday'
            all_available = True
            for student in students:
                availability = student.availability
                if not getattr(availability, f"{weekday}_available"):
                    all_available = False
                    break
            if all_available:
                available_dates.add(current_date.isoformat())
            current_date += timedelta(days=1)
        
        return Response(
            AvailableDatesSerializer({"available_dates": sorted(list(available_dates))}).data,
            status=status.HTTP_200_OK
        )

    @action(detail=False, methods=['get'], url_path='student-times-availability')
    def retrieve_student_times_availability(self, request):
        student_ids = request.query_params.getlist('student_ids')
        date_str = request.query_params.get('date')
        user_timezone = request.query_params.get('timezone')

        # Validate input parameters
        if not student_ids or not date_str:
            return Response({"error": "Missing 'student_ids' or 'date' parameter."}, status=status.HTTP_400_BAD_REQUEST)
        
        # Determine user's timezone
        try:
            user_tz = pytz.timezone(user_timezone) if user_timezone else pytz.timezone(getattr(request.user.profile, 'time_zone', 'UTC'))
        except pytz.UnknownTimeZoneError:
            return Response({"error": "Invalid timezone parameter."}, status=status.HTTP_400_BAD_REQUEST)

        try:
            requested_date = datetime.strptime(date_str, '%Y-%m-%d').date()
        except ValueError:
            return Response({"error": "Invalid date format. Use YYYY-MM-DD."}, status=status.HTTP_400_BAD_REQUEST)

        # Retrieve users
        students = User.objects.filter(id__in=student_ids)
        if students.count() != len(student_ids):
            return Response({"error": "One or more 'student_ids' are invalid."}, status=status.HTTP_400_BAD_REQUEST)

        # Retrieve availability for each student
        availability_data = []
        for student in students:
            availability = student.availability
            time_ranges = availability.get_time_ranges_for_date(requested_date, user_tz.zone)
            availability_data.append(time_ranges)

        # Find overlapping available time slots
        def get_overlapping_intervals(slots1, slots2):
            overlapping = []
            for slot1 in slots1:
                start1, end1 = slot1['start_time'], slot1['end_time']
                for slot2 in slots2:
                    start2, end2 = slot2['start_time'], slot2['end_time']
                    latest_start = max(start1, start2)
                    earliest_end = min(end1, end2)
                    if latest_start < earliest_end:
                        overlapping.append({
                            'start_time': latest_start,
                            'end_time': earliest_end
                        })
            return overlapping

        if not availability_data:
            overlapping_slots = []
        else:
            overlapping_slots = availability_data[0]
            for slots in availability_data[1:]:
                overlapping_slots = get_overlapping_intervals(overlapping_slots, slots)
                if not overlapping_slots:
                    break  # No overlapping slots

        if not overlapping_slots:
            return Response({
                "time_zone": user_tz.zone,
                "available_time_slots": []
            }, status=status.HTTP_200_OK)

#CHECK OUT THE TIMEZONE LOGIC
        # Filter out booked slots
        # Define the start and end of the day in the user's timezone
        start_of_day = user_tz.localize(datetime.combine(requested_date, time.min))
        end_of_day = user_tz.localize(datetime.combine(requested_date, time.max))

        # Convert to UTC for querying the database
        max_timezone_offset = timedelta(hours=14)  # Maximum possible timezone difference
        start_of_day_utc = start_of_day.astimezone(pytz.UTC) - max_timezone_offset
        end_of_day_utc = end_of_day.astimezone(pytz.UTC) + max_timezone_offset

        # Retrieve booked slots overlapping with the requested date
        booked_slots = Booking.objects.filter(
            trailblazer_statuses__trailblazer__in=students,
            start_time__lt=end_of_day_utc,
            end_time__gt=start_of_day_utc,
            creator_status='confirmed',
            trailblazer_statuses__status__in=['confirmed', 'pending']
        ).values('start_time', 'end_time')

        # Convert booked slots to user's timezone
        booked_intervals = []
        for booking in booked_slots:
            start_time_local = booking['start_time'].astimezone(user_tz)
            end_time_local = booking['end_time'].astimezone(user_tz)
            booked_intervals.append({
                'start_time': start_time_local,
                'end_time': end_time_local
            })

        # Generate 1-hour blocks from overlapping slots
        def generate_one_hour_blocks(slot):
            blocks = []
            slot_start = slot['start_time']
            slot_end = slot['end_time']

            # Round up slot_start to next half hour
            minutes = slot_start.minute
            if minutes % 30 != 0:
                # Round up to next half hour
                rounded_minutes = 30 if minutes < 30 else 0
                if rounded_minutes == 0:
                    slot_start = slot_start.replace(hour=(slot_start.hour + 1) % 24, minute=0, second=0, microsecond=0)
                else:
                    slot_start = slot_start.replace(minute=30, second=0, microsecond=0)
            else:
                # Set seconds and microseconds to zero
                slot_start = slot_start.replace(second=0, microsecond=0)

            current_start = slot_start
            while current_start + timedelta(hours=1) <= slot_end:
                current_end = current_start + timedelta(hours=1)
                blocks.append({
                    'start_time': current_start,
                    'end_time': current_end
                })
                current_start += timedelta(minutes=30)

            return blocks

        # Generate and filter blocks
        blocks = []
        for slot in overlapping_slots:
            blocks.extend(generate_one_hour_blocks(slot))

        # Filter out blocks that overlap with booked intervals
        def is_block_available(block, booked_intervals):
            for booked in booked_intervals:
                if block['start_time'] < booked['end_time'] and block['end_time'] > booked['start_time']:
                    return False
            return True

        available_blocks = [block for block in blocks if is_block_available(block, booked_intervals)]

        # Filter out blocks within now and the next 72 hours
        # now_utc = timezone.now()
        # now_user_tz = now_utc.astimezone(user_tz)
        # cutoff_user_tz = now_user_tz + timedelta(hours=72)
        # available_blocks = [
        #     block for block in available_blocks
        #     if not (now_user_tz <= block['start_time'] < cutoff_user_tz)
        # ]

        if not available_blocks:
            return Response({
                "time_zone": user_tz.zone,
                "available_time_slots": []
            }, status=status.HTTP_200_OK)

        # Format the available time slots for the response
        formatted_blocks = []
        for block in available_blocks:
            formatted_blocks.append({
                "start": block['start_time'].strftime('%H:%M'),
                "end": block['end_time'].strftime('%H:%M')
            })

        serializer = AvailableTimesSerializer({
            "time_zone": user_tz.zone,
            "available_time_slots": formatted_blocks
        })
        return Response(serializer.data, status=status.HTTP_200_OK)

