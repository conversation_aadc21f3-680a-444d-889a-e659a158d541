"use client"

import React, { useState, useEffect, useMemo } from 'react'
import Link from 'next/link'
import {
    Bookmark, MessageCircle, Users, Settings2, Compass, SearchX
} from 'lucide-react'

import {
    Accordion,
    AccordionContent,
    AccordionItem,
    AccordionTrigger,
} from "@/components/ui/accordion"
import {
    Table,
    TableBody,
    TableCell,
    TableHead,
    TableHeader,
    TableRow,
} from "@/components/ui/table"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"

import { Skeleton } from "@/components/ui/skeleton"
import { useRouter } from 'next/navigation'
import { useAuth } from '@/context/AuthProvider'
import { useToast } from '@/hooks/use-toast'
import { useShortlist } from '@/hooks/useShortlist'
import DashboardLayout from '@/components/layouts/DashboardLayout'
import RecommendationsLoading from '@/components/layouts/RecommendationsLoading'

/**
 * Connect with Students promotional card component
 * Features responsive design and avatar group display
 */
const ConnectStudentsCard = ({ trailblazers }) => {
    return (
        <div className="my-6">
            <div className="connect-card bg-green-50 p-5 rounded-lg border border-slate-200 shadow-sm">
                <div className="md:flex md:items-center md:justify-between">
                    <div className="md:flex md:items-center">
                        {/* Icon - hidden on mobile for space */}
                        <div className="hidden md:flex items-center justify-center w-8 h-8 rounded-full bg-primary/10 text-primary mr-3">
                            <MessageCircle className="w-4 h-4" aria-hidden="true" />
                        </div>
                        <div className="flex-grow">
                            <h3 className="text-lg font-semibold text-gray-800 mb-1 md:mb-0">
                                Unlock Student Insights
                            </h3>
                            <p className="text-gray-700 text-sm mb-3 md:mb-0">
                                Chat with <span className="font-semibold text-primary">{trailblazers} current students</span> from your recommended colleges.
                            </p>
                        </div>
                    </div>
                    {/* Avatar group - hidden on mobile */}
                    <div className="hidden md:flex items-center ml-4 mr-4 shrink-0">
                        <div className="flex -space-x-2" role="group" aria-label="Student avatars">
                            <Avatar className="w-8 h-8 border-2 border-white">
                                <AvatarImage 
                                    src="/avatars/boy_1.png" 
                                    alt="Student 1" 
                                />
                                <AvatarFallback>S1</AvatarFallback>
                            </Avatar>
                            <Avatar className="w-8 h-8 border-2 border-white">
                                <AvatarImage 
                                    src="/avatars/boy_6.png" 
                                    alt="Student 2" 
                                />
                                <AvatarFallback>S2</AvatarFallback>
                            </Avatar>
                            <Avatar className="w-8 h-8 border-2 border-white">
                                <AvatarImage 
                                    src="/avatars/boy_8.png" 
                                    alt="Student 3" 
                                />
                                <AvatarFallback>S3</AvatarFallback>
                            </Avatar>
                            <Avatar className="w-8 h-8 border-2 border-white bg-gray-200">
                                <AvatarImage 
                                    src="/avatars/boy_4.png" 
                                    alt="Student 4" 
                                />
                                <AvatarFallback>S3</AvatarFallback>
                            </Avatar>
                        </div>
                    </div>
                    {/* Call-to-action button */}
                    <Button 
                        asChild 
                        className="bg-primary text-white hover:bg-primary/90 w-full md:w-auto mt-3 md:mt-0"
                    >
                        <Link href="/paywall">
                            <Users className="w-4 h-4 mr-2" aria-hidden="true" />
                            Connect with Students
                        </Link>
                    </Button>
                </div>
            </div>
        </div>
    )
}

/**
 * Individual college row component with bookmark functionality
 */
const CollegeRow = ({ college, isBookmarked, onBookmarkToggle }) => {
    const { addCollege, removeCollege } = useShortlist()

    const handleClick = async () => {
        if (isBookmarked) {
            const success = await removeCollege(college.id)
            if (success) {
                onBookmarkToggle(college.id)
            }
        } else {
            const success = await addCollege(college.id)
            if (success) {
                onBookmarkToggle(college.id)
            }
        }
    }

    return (
        <TableRow className="hover:bg-gray-50 transition-colors">
            <TableCell className="w-[200px] truncate overflow-hidden whitespace-nowrap">
                {college.unitid ? (
                    <Link 
                        href={`/colleges/${college.unitid}`}
                        className="text-sm font-medium text-gray-900 hover:underline hover:text-primary transition-colors"
                    >
                        {college.name}
                    </Link>
                ) : (
                    <span className="text-sm font-medium text-red-500">
                        {college.name} (No UNITID)
                    </span>
                )}
            </TableCell>
            <TableCell className="w-[150px] truncate overflow-hidden whitespace-nowrap text-sm text-gray-500">
                {college.location}
            </TableCell>
            <TableCell className="w-[120px] truncate overflow-hidden whitespace-nowrap text-sm text-gray-500">
                {college.type}
            </TableCell>
            <TableCell className="w-[130px] truncate overflow-hidden whitespace-nowrap text-sm text-gray-500">
                {college.acceptance}
            </TableCell>
            <TableCell className="w-[140px] truncate overflow-hidden whitespace-nowrap text-sm text-gray-500">
                ${college.cost.toLocaleString()}
            </TableCell>
            <TableCell className="w-[100px] truncate overflow-hidden whitespace-nowrap text-sm">
                <Button 
                    variant="ghost" 
                    size="icon" 
                    onClick={handleClick}
                    className={`hover:text-primary transition-colors ${
                        isBookmarked ? 'text-primary' : 'text-gray-400'
                    }`}
                    aria-label={`${isBookmarked ? 'Remove from' : 'Add to'} shortlist`}
                >
                    <Bookmark className={`w-5 h-5 ${isBookmarked ? 'fill-primary' : ''}`} />
                </Button>
            </TableCell>
        </TableRow>
    )
}

/**
 * College table component with sortable headers
 * Displays college information in a structured table format
 */
const CollegeTable = ({ colleges, bookmarkedColleges, onBookmarkToggle }) => {
    const [sortColumn, setSortColumn] = useState(null)
    const [sortDirection, setSortDirection] = useState('asc')

    const handleSort = (header) => {
        // Map header display names to data keys
        const columnKeyMap = {
          "College Name": "name",
          "Acceptance": "acceptance",
          "Annual Cost": "cost",
          "Location": "location",
          "Type": "type"
        }
        const key = columnKeyMap[header]
        if (!key) return
        if (sortColumn === key) {
          // Toggle sort direction if the same column is clicked again
          setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc')
        } else {
          setSortColumn(key)
          setSortDirection('asc')
        }
      }

    const tableHeaders = [
        "College Name", 
        "Location", 
        "Type", 
        "Acceptance", 
        "Annual Cost", 
        "Shortlist"
    ]

    const columnWidths = {
        "College Name": "w-[200px]",
        "Location": "w-[150px]",
        "Type": "w-[120px]",
        "Acceptance": "w-[130px]",
        "Annual Cost": "w-[140px]",
        "Shortlist": "w-[100px]"
      }

    const sortedColleges = useMemo(() => {
        if (!sortColumn) return colleges
        const sorted = [...colleges].sort((a, b) => {
          const valueA = a[sortColumn]
          const valueB = b[sortColumn]
          // For numeric fields: Acceptance and Annual Cost, compare as numbers
          if (sortColumn === 'acceptance' || sortColumn === 'cost') {
            const numA = parseFloat(valueA) || 0
            const numB = parseFloat(valueB) || 0
            return sortDirection === 'asc' ? numA - numB : numB - numA
          }
          // For text fields: college name etc.
          if (typeof valueA === 'string' && typeof valueB === 'string') {
            return sortDirection === 'asc'
              ? valueA.localeCompare(valueB)
              : valueB.localeCompare(valueA)
          }
          return 0
        })
        return sorted
      }, [colleges, sortColumn, sortDirection])

    return (
        <div className="overflow-x-auto bg-white rounded-lg shadow-md">
            <Table className="table-fixed min-w-full divide-y divide-gray-200">
                <TableHeader className="bg-gray-50">
                    <TableRow>
                        {tableHeaders.map((header, idx) => {
                            // Determine the data key for sortable headers
                            const columnKeyMap = {
                                "College Name": "name",
                                "Acceptance": "acceptance",
                                "Annual Cost": "cost",
                                "Location": "location",
                                "Type": "type"
                            }
                            const key = columnKeyMap[header]
                            const isActive = key && sortColumn === key
                            return (
                                <TableHead 
                                    key={idx} 
                                    className={`px-2 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100 transition-colors ${columnWidths[header] || ''} ${isActive ? 'bg-gray-200 text-primary' : ''}`}
                                    onClick={() => handleSort(header)}
                                    role="columnheader"
                                    tabIndex={0}
                                    onKeyDown={(e) => {
                                        if (e.key === 'Enter' || e.key === ' ') {
                                            handleSort(header)
                                        }
                                    }}
                                >
                                    <span>{header}</span>
                                    {isActive && (
                                        <span className="ml-1 text-xs">
                                            {sortDirection === 'asc' ? '↑' : '↓'}
                                        </span>
                                    )}
                                </TableHead>
                            )
                        })}
                    </TableRow>
                </TableHeader>
                <TableBody className="bg-white divide-y divide-gray-200">
                    {sortedColleges.map((college) => (
                        <CollegeRow
                            key={college.id}
                            college={college}
                            isBookmarked={bookmarkedColleges.has(college.id)}
                            onBookmarkToggle={onBookmarkToggle}
                        />
                    ))}
                </TableBody>
            </Table>
        </div>
    )
}

/**
 * Collapsible section for each college category (Reach, Target, Safety)
 * Uses Accordion component for expand/collapse functionality
 */
const CollegeCategorySection = ({ title, colleges, bookmarkedColleges, onBookmarkToggle, value }) => {
    return (
        <AccordionItem value={value} className="border-none mb-10">
            <AccordionTrigger className="text-2xl font-semibold text-gray-800 flex justify-start items-center py-2 hover:no-underline focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded">
                <span className="pr-4">{title}</span>
                <span className="text-sm font-normal text-gray-500 ml-2">
                    ({colleges.length} colleges)
                </span>
            </AccordionTrigger>
            <AccordionContent className="pt-2">
                <CollegeTable 
                    colleges={colleges} 
                    bookmarkedColleges={bookmarkedColleges} 
                    onBookmarkToggle={onBookmarkToggle} 
                />
            </AccordionContent>
        </AccordionItem>
    )
}

/**
 * Loading state component with skeleton placeholders
 */
const LoadingRecommendations = () => {
    return (
        <div role="status" aria-label="Loading recommendations">
            <div className="mb-8">
                <Skeleton className="h-10 w-3/4 mb-3 rounded-default" />
                <Skeleton className="h-5 w-full rounded-default" />
            </div>
            {[1, 2, 3].map((i) => (
                <div key={i} className="mb-10">
                    <Skeleton className="h-8 w-1/3 mb-4 rounded-default" />
                    <div className="bg-white rounded-lg shadow-md p-4 space-y-3">
                        <div className="flex space-x-4 border-b border-gray-200 pb-2">
                            <Skeleton className="h-4 flex-grow rounded-default" />
                            <Skeleton className="h-4 w-16 rounded-default" />
                            <Skeleton className="h-4 w-24 rounded-default" />
                            <Skeleton className="h-4 w-16 rounded-default" />
                            <Skeleton className="h-4 w-24 rounded-default" />
                            <Skeleton className="h-4 w-16 rounded-default" />
                        </div>
                        {[1, 2, 3, 4, 5].map((j) => (
                            <div key={j} className="flex space-x-4 py-2 border-b border-gray-100 last:border-b-0">
                                <Skeleton className="h-5 flex-grow rounded-default" />
                                <Skeleton className="h-5 w-16 rounded-default" />
                                <Skeleton className="h-5 w-24 rounded-default" />
                                <Skeleton className="h-5 w-16 rounded-default" />
                                <Skeleton className="h-5 w-24 rounded-default" />
                                <Skeleton className="h-5 w-16 rounded-default" />
                            </div>
                        ))}
                    </div>
                </div>
            ))}
        </div>
    )
}

/**
 * Empty state component when no recommendations are available
 */
const EmptyRecommendations = () => {
    const handleAdjustPreferences = () => {
        // Placeholder for future navigation implementation
        console.log("Adjust Preferences clicked")
    }

    return (
        <div className="text-center py-12 bg-white rounded-lg shadow-md" role="status">
            <div className="flex justify-center mb-6">
                <SearchX className="w-20 h-20 text-gray-300" aria-hidden="true" />
            </div>
            <h3 className="text-2xl font-semibold text-gray-800 mb-3">
                No Recommendations Yet
            </h3>
            <p className="text-gray-600 mb-8 max-w-md mx-auto">
                We couldn't find any colleges that match your current profile settings. 
                Try adjusting your preferences or explore all available colleges.
            </p>
            <div className="space-x-3">
                <Button 
                    className="bg-primary text-white hover:bg-primary/90" 
                    onClick={handleAdjustPreferences}
                >
                    <Settings2 className="w-5 h-5 mr-2" aria-hidden="true" />
                    Adjust Preferences
                </Button>
                <Button variant="secondary" asChild>
                    <Link href="/explore">
                        <Compass className="w-5 h-5 mr-2" aria-hidden="true" />
                        Explore All Colleges
                    </Link>
                </Button>
            </div>
        </div>
    )
}

/**
 * Main Recommendations Page Component
 * Manages state for loading, empty states, bookmarks, and mobile menu
 */
export default function RecommendationsPage() {
    // State management for different UI states
    const [isLoading, setIsLoading] = useState(false)
    const [showEmpty, setShowEmpty] = useState(false)
    const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false)
    
    // Initialize bookmarked colleges from data
    const [recommendationsData, setRecommendationsData] = useState({ reach: [], target: [], safety: [] })
    const [bookmarkedColleges, setBookmarkedColleges] = useState(new Set())
    const [errorMessage, setErrorMessage] = useState('')
    const [trailblazers, setTrailblazers] = useState(0)

    /**
     * Handle refresh recommendations button click
     * Simulates API call with loading state
     */
    const handleRefreshRecommendations = () => {
        router.push('/profile-setup')
    }

    /**
     * Handle bookmark toggle for individual colleges
     * Updates the bookmarked colleges set
     */
    const handleBookmarkToggle = (collegeId) => {
        setBookmarkedColleges(prev => {
            const newSet = new Set(prev)
            if (newSet.has(collegeId)) {
                newSet.delete(collegeId)
            } else {
                newSet.add(collegeId)
            }
            return newSet
        })
    }

    // Effect for initial load simulation (commented out to prevent auto-loading)
    useEffect(() => {
        pollRecommendationsStatus()
    }, [])
    // Ensure router and getToken are defined
    const router = useRouter()
    const { getToken, updateRecommendationsStatus, recommendationsStatus } = useAuth()
    const { toast } = useToast()

    const fetchRecommendations = async () => {
        setErrorMessage('')
        setIsLoading(true)
        try {
          const token = getToken() || localStorage.getItem('trailblazers.auth.token')
          if (!token) {
            router.push('/profile-setup')
            return
          }
          let url = process.env.NEXT_PUBLIC_API_BASE_URL + '/api/recommendations/'
          const res = await fetch(url, {
            headers: {
              'Authorization': `Token ${token}`
            }
          })
          if (!res.ok) {
            const errorData = await res.json()
            throw new Error(errorData.detail || 'Failed to fetch recommendations')
          }
          const data = await res.json()
          const formattedData = {
            reach: data.reach_universities.map((college) => ({
                id: college.unitid,
                unitid: college.unitid,
                name: college.college_name,
                location: college.location,
                type: college.type,
                acceptance: college.acceptance_rate,
                cost: college.annual_cost,
                bookmarked: college.bookmarked
            })),
            target: data.target_universities.map((college) => ({
                id: college.unitid,
                unitid: college.unitid,
                name: college.college_name,
                location: college.location,
                type: college.type,
                acceptance: college.acceptance_rate,
                cost: college.annual_cost,
                bookmarked: college.bookmarked
            })),
            safety: data.safety_universities.map((college) => ({
                id: college.unitid,
                unitid: college.unitid,
                name: college.college_name,
                location: college.location,
                type: college.type,
                acceptance: college.acceptance_rate,
                cost: college.annual_cost,
                bookmarked: college.bookmarked
            }))
            }
          setRecommendationsData(formattedData)
          setTrailblazers(data.connect_with_trailblazers || 0)
          const bookmarkedIds = new Set(
            [...formattedData.reach, ...formattedData.target, ...formattedData.safety]
              .filter(college => college.bookmarked)
              .map(college => college.id)
          )
          setBookmarkedColleges(bookmarkedIds)
        } catch (err) {
          setErrorMessage(err.message)
        } finally {
          setIsLoading(false)
        }
      }

    const pollRecommendationsStatus = async (attempt = 0) => {
        try {
          const token = getToken() || localStorage.getItem('trailblazers.auth.token')
          if (!token) {
            router.push('/profile-setup')
            return
          }
          const res = await fetch(process.env.NEXT_PUBLIC_API_BASE_URL + '/api/v1/users/recommendations/status/', {
            headers: {
              'Authorization': `Token ${token}`
            }
          })
          if (res.status === 401) {
            router.push('/profile-setup')
            return
          }
          if (!res.ok) {
            throw new Error('Failed to fetch recommendations status')
          }
          const data = await res.json()
          // Use updateRecommendationsStatus to update context values
          updateRecommendationsStatus(data.recommendations_status, data.recommendations_status !== 'ready')
          if (data.recommendations_status === 'ready') {
            await fetchRecommendations()
            return
          } else if (data.recommendations_status === 'error') {
            setErrorMessage('Failed to fetch recommendations. Please try again later.')
            toast({
                title: "Failed to create recommendations",
                description: "Please try again later.",
              })

            setTimeout(() => {
              router.push('/profile-setup')
            }, 5000)
            return
            }
          setTimeout(() => pollRecommendationsStatus(0), 5000)
        } catch (error) {
          const delay = Math.min(5000 * (2 ** attempt), 30000)
          setTimeout(() => pollRecommendationsStatus(attempt + 1), delay)
        }
      }

    useEffect(() => {
        pollRecommendationsStatus()
    }, [])

    if (recommendationsStatus !== 'ready') {
        return <RecommendationsLoading />
    }

    return (
        <DashboardLayout onRefresh={handleRefreshRecommendations}>
            {/* Main Content Area */}
                {/* Conditional rendering based on state */}
                {isLoading ? (
                    <LoadingRecommendations />
                ) : showEmpty ? (
                    <EmptyRecommendations />
                ) : (
                    <div>
                        {/* Page Title and Description */}
                        <div className="mb-8">
                            <h1 className="text-3xl font-bold text-gray-800 font-header mb-2">
                                Your College Recommendations
                            </h1>
                            <p className="text-gray-600">
                                Based on your profile, here are colleges that might be a great fit for you.
                            </p>
                        </div>

                        {errorMessage && (
                            <p className="text-red-500 text-sm mb-4">{errorMessage}</p>
                        )}

                        {/* Connect with Students Promotional Card */}
                        <ConnectStudentsCard trailblazers={trailblazers} />

                        {/* College Categories Accordion */}
                        <Accordion
                            type="multiple"
                            defaultValue={["reach-schools", "target-schools", "safety-schools"]}
                            className="w-full"
                        >
                            <CollegeCategorySection
                                value="reach-schools"
                                title="Reach Schools"
                                colleges={recommendationsData.reach}
                                bookmarkedColleges={bookmarkedColleges}
                                onBookmarkToggle={handleBookmarkToggle}
                            />
                            <CollegeCategorySection
                                value="target-schools"
                                title="Target Schools"
                                colleges={recommendationsData.target}
                                bookmarkedColleges={bookmarkedColleges}
                                onBookmarkToggle={handleBookmarkToggle}
                            />
                            <CollegeCategorySection
                                value="safety-schools"
                                title="Safety Schools"
                                colleges={recommendationsData.safety}
                                bookmarkedColleges={bookmarkedColleges}
                                onBookmarkToggle={handleBookmarkToggle}
                            />
                        </Accordion>
                    </div>
                )}
        </DashboardLayout>
    )
}