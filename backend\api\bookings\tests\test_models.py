from unittest.mock import patch
from django.test import TestCase
from django.utils import timezone
from django.core.exceptions import ValidationError
from datetime import timedelta
from django.contrib.auth import get_user_model
from api.bookings.models import Booking, TrailblazerBookingStatus

User = get_user_model()


@patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
@patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
class BookingModelTest(TestCase):
    def setUp(self):
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booked_by = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.start_time = timezone.now() + timedelta(days=4)
        self.booking = Booking(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Looking forward to the session.",
            number_of_students=5
        )

    def test_create_booking_with_valid_data(self, mock_create_events, mock_cancel_events):
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.booking.save()
        self.assertIsNotNone(self.booking.id)
        self.assertEqual(self.booking.end_time, self.start_time + self.booking.duration)
        self.assertEqual(self.booking.status, 'pending')

    def test_update_booking_status(self, mock_create_events, mock_cancel_events):
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'confirmed')])
        self.booking.save()
        self.assertEqual(Booking.objects.get(id=self.booking.id).status, 'confirmed')

    def test_retrieve_booking_instance(self, mock_create_events, mock_cancel_events):
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.booking.save()
        retrieved = Booking.objects.get(id=self.booking.id)
        self.assertEqual(retrieved.message, "Looking forward to the session.")
        self.assertEqual(retrieved.number_of_students, 5)

    def test_booking_times_stored_in_utc(self, mock_create_events, mock_cancel_events):
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.booking.save()
        retrieved = Booking.objects.get(id=self.booking.id)
        self.assertTrue(retrieved.start_time.tzinfo is timezone.utc)
        self.assertTrue(retrieved.end_time.tzinfo is timezone.utc)

    # this logic was commented in the Booking model
    # def test_72_hour_notice_enforcement(self, mock_create_events, mock_cancel_events):
    #     self.booking.save()
    #     self.booking.start_time = timezone.now() + timedelta(hours=71)
    #     with self.assertRaises(ValidationError):
    #         self.booking.clean()

    def test_negative_number_of_students(self, mock_create_events, mock_cancel_events):
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.booking.save()
        self.booking.number_of_students = -3
        with self.assertRaises(ValidationError):
            self.booking.clean()

    def test_double_booking_prevention(self, mock_create_events, mock_cancel_events):
        self.booking.save()
        self.booking.set_trailblazers([(self.trailblazer, 'pending')])
        self.booking.save()

        overlapping_booking = Booking(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Another session.",
            number_of_students=2,
            is_available=False
        )
        
        with self.assertRaises(ValidationError):
            overlapping_booking.save()
            overlapping_booking.set_trailblazers([(self.trailblazer, 'pending')])
            overlapping_booking.save()

    def test_create_booking_with_multiple_trailblazers(self, mock_create_events, mock_cancel_events):
        trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        trailblazer3 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booking.save()
        self.booking.add_trailblazer(self.trailblazer)
        self.booking.add_trailblazer(trailblazer2)
        self.booking.add_trailblazer(trailblazer3)
        self.assertEqual(self.booking.trailblazers.count(), 3)

    def test_create_booking_with_more_than_three_trailblazers(self, mock_create_events, mock_cancel_events):
        trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        trailblazer3 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        trailblazer4 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booking.save()
        with self.assertRaises(ValidationError):
            self.booking.add_trailblazer(self.trailblazer)
            self.booking.add_trailblazer(trailblazer2)
            self.booking.add_trailblazer(trailblazer3)
            self.booking.add_trailblazer(trailblazer4)
            self.booking.clean()

    def test_overlapping_booking_with_multiple_trailblazers(self, mock_create_events, mock_cancel_events):
        trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booking.is_available = False
        self.booking.save()
        self.booking.add_trailblazer(self.trailblazer)
        self.booking.add_trailblazer(trailblazer2)
        
        overlapping_booking = Booking(
            booked_by=self.booked_by,
            start_time=self.start_time,
            end_time=self.start_time + timedelta(hours=1),
            message="Overlapping session.",
            number_of_students=2,
            is_available=False
        )
        overlapping_booking.save()
        overlapping_booking.add_trailblazer(self.trailblazer)

        with self.assertRaises(ValidationError):
            overlapping_booking.save()

    # This test is based on the commented logic in the Booking model
    # def test_72_hour_rule_with_multiple_trailblazers(self, mock_create_events, mock_cancel_events):
    #     trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
    #     self.booking.save()
    #     self.booking.add_trailblazer(trailblazer2)
    #     self.booking.start_time = timezone.now() + timedelta(hours=71)
    #     with self.assertRaises(ValidationError):
    #         self.booking.clean()


@patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
@patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
class TrailblazerBookingStatusTest(TestCase):
    def setUp(self):
        self.booked_by = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.trailblazer = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=timezone.now() + timedelta(days=4),
            message="Test booking.",
            number_of_students=2
        )
        self.status = TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer)
    
    def test_trailblazer_status_creation(self, mock_create_events, mock_cancel_events):
        self.assertEqual(self.status.status, 'pending')
        self.assertEqual(self.status.booking, self.booking)
        self.assertEqual(self.status.trailblazer, self.trailblazer)
    
    def test_trailblazer_status_update(self, mock_create_events, mock_cancel_events):
        self.status.status = 'confirmed'
        self.status.save()
        updated_status = TrailblazerBookingStatus.objects.get(id=self.status.id)
        self.assertEqual(updated_status.status, 'confirmed')


@patch('api.calendar_integration.services.CalendarService.create_events_for_booking')
@patch('api.calendar_integration.services.CalendarService.delete_events_for_booking')
class BookingStatusLogicTest(TestCase):
    def setUp(self):
        self.booked_by = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.trailblazer1 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.trailblazer2 = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.start_time = timezone.now() + timedelta(days=4)
        self.booking = Booking.objects.create(
            booked_by=self.booked_by,
            start_time=self.start_time,
            message="Test booking.",
            number_of_students=3
        )
    
    def test_booking_cancelled_by_creator(self, mock_create_events, mock_cancel_events):
        self.booking.creator_status = 'cancelled'
        self.booking.save()
        self.assertEqual(self.booking.status, 'cancelled')
    
    def test_booking_declined_all_trailblazers_declined(self, mock_create_events, mock_cancel_events):
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer1, status='declined')
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer2, status='declined')
        self.assertEqual(self.booking.status, 'declined')
    
    def test_booking_pending(self, mock_create_events, mock_cancel_events):
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer1, status='pending')
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer2, status='pending')
        self.assertEqual(self.booking.status, 'pending')
    
    def test_booking_confirmed(self, mock_create_events, mock_cancel_events):
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer1, status='confirmed')
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer2, status='pending')
        self.assertEqual(self.booking.status, 'confirmed')
    
    def test_booking_completed(self, mock_create_events, mock_cancel_events):
        self.booking.end_time = timezone.now() - timedelta(hours=1)
        self.booking.save()
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer1, status='confirmed')
        TrailblazerBookingStatus.objects.create(booking=self.booking, trailblazer=self.trailblazer2, status='pending')
        self.assertEqual(self.booking.status, 'completed')
    
    def test_no_trailblazers_defaults_to_pending(self, mock_create_events, mock_cancel_events):
        self.booking.trailblazer_statuses.all().delete()
        self.assertEqual(self.booking.status, 'pending')
