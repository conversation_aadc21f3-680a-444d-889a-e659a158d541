from django.urls import path, include
from rest_framework.routers import DefaultRouter
from api.universities.views import RecommendationsView, UniversityDetailView, ShortlistView, ShortlistDeleteView, UniversitySearchView

router = DefaultRouter()

urlpatterns = [
    path('recommendations/', RecommendationsView.as_view(), name='recommendations'),
    path('universities/details/<str:unitid>/', UniversityDetailView.as_view(), name='university-detail'),
    path('shortlist/', ShortlistView.as_view(), name='shortlist'),
    path('shortlist/<str:college_id>/', ShortlistDeleteView.as_view(), name='shortlist-delete'),
    path('universities/', UniversitySearchView.as_view(), name='university-search'),
]