from django.test import TestCase
from django.contrib.auth import get_user_model
from api.organizations.models import Organization
import uuid

User = get_user_model()

class OrganizationModelTest(TestCase):

    def setUp(self):
        self.admin_user = User.objects.create_user(
            email='<EMAIL>',
            password='password123'
        )
        self.organization = Organization.objects.create(
            name='Test Organization',
            zip_code='12345',
            city='Test City',
            state='Test State',
            school_district='Test District',
            contact_phone='************',
            contact_email='<EMAIL>',
            registration_number='REG123456'
        )
        self.organization.administrators.add(self.admin_user)

    def test_organization_creation(self):
        self.assertIsInstance(self.organization, Organization)
        self.assertEqual(self.organization.name, 'Test Organization')
        self.assertEqual(self.organization.zip_code, '12345')
        self.assertEqual(self.organization.city, 'Test City')
        self.assertEqual(self.organization.state, 'Test State')
        self.assertEqual(self.organization.school_district, 'Test District')
        self.assertEqual(self.organization.contact_phone, '************')
        self.assertEqual(self.organization.contact_email, '<EMAIL>')
        self.assertEqual(self.organization.registration_number, 'REG123456')
        self.assertIsNotNone(self.organization.created_at)
        self.assertIsNotNone(self.organization.updated_at)

    def test_str_representation(self):
        self.assertEqual(str(self.organization), 'Test Organization')

    def test_administrators_relationship(self):
        self.assertIn(self.admin_user, self.organization.administrators.all())
        self.assertIn(self.organization, self.admin_user.organizations_managed.all())

    def test_unique_registration_number(self):
        with self.assertRaises(Exception):
            Organization.objects.create(
                name='Another Org',
                zip_code='54321',
                city='Another City',
                state='Another State',
                registration_number='REG123456'
            )

    def test_optional_school_district(self):
        org = Organization.objects.create(
            name='No District Org',
            zip_code='67890',
            city='No District City',
            state='No District State'
        )
        self.assertIsNone(org.school_district)
