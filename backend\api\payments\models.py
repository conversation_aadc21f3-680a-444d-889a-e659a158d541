from django.db import models
from django.contrib.auth import get_user_model
from django.conf import settings
from django.utils import timezone

User = get_user_model()

class PaymentAutomationSetting(models.Model):
    is_automated = models.BooleanField(default=True)

    def __str__(self):
        return "Payment Automation Setting"

class PaymentPeriodTrailblazerHours(models.Model):
    id = models.BigAutoField(primary_key=True)
    user = models.OneToOneField(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_period_trailblazer_hours')  
    paypal_email = models.EmailField(max_length=254, blank=True, null=True)
    total_hours = models.FloatField(default=0)
    payment_period_start = models.DateTimeField(null=True, blank=True)  # Set dedefault=timezone.nowfaultSet
    payment_period_end = models.DateTimeField(null=True, blank=True)  # Set dedefault=timezone.nowfaultSet
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True
        verbose_name = "Payment Period Trailblazer Hours"
        verbose_name_plural = "Payment Period Trailblazer Hours"
        indexes = [
            models.Index(fields=['user']),
        ]
        constraints = [
            models.UniqueConstraint(fields=['user', 'paypal_email', 'payment_period_start', 'payment_period_end'], name='unique_trailblazer_payment_period_timesheet')
        ]

    def __str__(self):
        return f"{self.user}: {self.total_hours} hours"
    
class PaymentLog(models.Model):
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, related_name='payment_logs')
    paypal_email = models.EmailField(max_length=254)
    hours = models.FloatField(null=True, blank=True)
    amount = models.DecimalField(max_digits=10, decimal_places=2, null=True)
    payment_status = models.CharField(max_length=1000)
    error_message = models.TextField(blank=True, null=True)
    payment_period_start = models.DateTimeField(null=True, blank=True)
    payment_period_end = models.DateTimeField(null=True, blank=True)
    payment_date = models.DateTimeField(null=True, blank=True)
    payout_batch_id = models.CharField(max_length=255, null=True, blank=True)
    payout_item_id = models.CharField(max_length=255, null=True, blank=True)
    time_processed = models.DateTimeField(null=True, blank=True) 
    batch_status = models.CharField(max_length=50, null=True, blank=True) 
    time_completed = models.DateTimeField(null=True, blank=True)  
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        managed = True 
        db_table = 'payment_log'
        ordering = ['-payment_date']
        indexes = [
            models.Index(fields=['user']),
            models.Index(fields=['paypal_email']),
        ]
        constraints = [
            models.UniqueConstraint(fields=['payout_item_id', 'payout_batch_id'], name='unique_payment_log')
        ]

    def __str__(self):
        return f"{self.paypal_email} - {self.payment_status}"

class SkippedPaymentLog(models.Model):
    id = models.BigAutoField(primary_key=True)
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True)
    paypal_email = models.CharField(max_length=254)
    hours = models.FloatField(null=True, blank=True)
    error_message = models.CharField(max_length=255)
    payment_status = models.CharField(max_length=1000, null=True, blank=True)
    payment_period_start = models.DateTimeField(null=True, blank=True)
    payment_period_end = models.DateTimeField(null=True, blank=True)
    payment_date = models.DateTimeField(null=True, blank=True)
    payout_batch_id = models.CharField(max_length=255, null=True, blank=True)
    payout_item_id = models.CharField(max_length=255, null=True, blank=True)
    time_processed = models.DateTimeField(null=True, blank=True)
    batch_status = models.CharField(max_length=50, null=True, blank=True) 
    time_completed = models.DateTimeField(null=True, blank=True) 
    created_at = models.DateTimeField(default=timezone.now)
    updated_at = models.DateTimeField(auto_now=True)
    

    class Meta:
        managed = True
        verbose_name = "Skipped Payment Log"
        verbose_name_plural = "Skipped Payment Logs"
        indexes = [
            models.Index(fields=['paypal_email']),
            models.Index(fields=['user']),
        ]
        constraints = [
            models.UniqueConstraint(fields=['payout_item_id', 'payout_batch_id'], name='unique_skipped_payment_log')
        ]

    def __str__(self):
        return f"SkippedPaymentLog(paypal_email={self.user}, reason={self.error_message})"