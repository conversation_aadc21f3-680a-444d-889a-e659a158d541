
// Parse the availability data from the API into a format that the Availability component can use
export const parseAvailabilityApiData = (data) => {
    const formatTimeSlots = (timeSlots) => {
        // If no time slots are available, return an empty array
        if (!timeSlots || timeSlots.length === 0) return [
            { start: '', end: '' }
        ]
        // Otherwise, format the time slots
        return timeSlots.map(slot => {
            return { start: slot.start_time, end: slot.end_time }
        })
    }

    return {
        timeZone: data.time_zone,
        days: [
            { day: 'Sunday', isAvailable: data.sunday_available, timeSlots: formatTimeSlots(data.sunday_time_ranges) },
            { day: 'Monday', isAvailable: data.monday_available, timeSlots: formatTimeSlots(data.monday_time_ranges) },
            { day: 'Tuesday', isAvailable: data.tuesday_available, timeSlots: formatTimeSlots(data.tuesday_time_ranges) },
            { day: 'Wednesday', isAvailable: data.wednesday_available, timeSlots: formatTimeSlots(data.wednesday_time_ranges) },
            { day: 'Thursday', isAvailable: data.thursday_available, timeSlots: formatTimeSlots(data.thursday_time_ranges) },
            { day: 'Friday', isAvailable: data.friday_available, timeSlots: formatTimeSlots(data.friday_time_ranges) },
            { day: 'Saturday', isAvailable: data.saturday_available, timeSlots: formatTimeSlots(data.saturday_time_ranges) },
        ]
    }
}

// Format the availability data from the form into a format that the API can accept
export const formatAvailabilityDataForApi = (formData) => {
    const formatTimeSlots = (slots) => {
        return slots.filter(slot => slot.start && slot.end).map(slot => {
            return { start_time: slot.start, end_time: slot.end }
        })
    }

    return {
        time_zone: formData.timeZone,
        sunday_available: formData.days[0].isAvailable,
        monday_available: formData.days[1].isAvailable,
        tuesday_available: formData.days[2].isAvailable,
        wednesday_available: formData.days[3].isAvailable,
        thursday_available: formData.days[4].isAvailable,
        friday_available: formData.days[5].isAvailable,
        saturday_available: formData.days[6].isAvailable,
        sunday_time_ranges: formatTimeSlots(formData.days[0].timeSlots),
        monday_time_ranges: formatTimeSlots(formData.days[1].timeSlots),
        tuesday_time_ranges: formatTimeSlots(formData.days[2].timeSlots),
        wednesday_time_ranges: formatTimeSlots(formData.days[3].timeSlots),
        thursday_time_ranges: formatTimeSlots(formData.days[4].timeSlots),
        friday_time_ranges: formatTimeSlots(formData.days[5].timeSlots),
        saturday_time_ranges: formatTimeSlots(formData.days[6].timeSlots),
    }
} 