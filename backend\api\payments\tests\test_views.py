from django.test import TestCase, TransactionTestCase
from django.urls import reverse
from django.utils.timezone import utc
from datetime import timedelta, datetime
from django.utils import timezone
from rest_framework.test import APIClient, APITestCase
from rest_framework import status
from api.bookings.models import Booking, TrailblazerBookingStatus
from api.payments.models import PaymentPeriodTrailblazerHours, PaymentLog, SkippedPaymentLog
from django.contrib.auth import get_user_model
import pytz
from django.conf import settings
from api.payments.views import calculate_payment_period, calculate_total_hours, convert_total_hours, update_payment_period_trailblazer_hours_table
from django.utils.dateparse import parse_datetime
import json

User = get_user_model()
"""
If a payment is made after the most recent Monday (e.g Monday Feb 17) of the week, eg. on Thursday (e.g Thursday Feb 20th ) 
but before next Monday (e.g Monday Feb 24th), the payment period should be from 
the previous Monday (e.g Monday Feb 10) to the most recent Monday (e.g Monday Feb 17th).

"""
class CalculateDateRangeTest(TestCase):

    def test_calculate_date_range(self):
        # Define a specific payroll date
        payroll_date = datetime(2025, 2, 20, tzinfo=utc)  # Thursday, February 20, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025
        expected_previous_monday = datetime(2025, 2, 10, tzinfo=utc)  # Monday, February 10, 2025
        
        # Call the function with the specific payroll date
        actual_previous_monday, actual_current_monday = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_previous_monday, expected_previous_monday)
        self.assertEqual(actual_current_monday, expected_current_monday)

    def test_calculate_date_range_on_monday(self):
        # Define a payroll date on a Monday
        payroll_date = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025
        expected_previous_monday = datetime(2025, 2, 10, tzinfo=utc)  # Monday, February 10, 2025
        

        # Call the function with the specific payroll date
        actual_start_time, actual_end_time = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_start_time, expected_previous_monday)
        self.assertEqual(actual_end_time, expected_current_monday)

    def test_calculate_date_range_on_sunday(self):
        # Define a payroll date on a Sunday
        payroll_date = datetime(2025, 2, 16, tzinfo=utc)  # Sunday, February 16, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 10, tzinfo=utc)  # Monday, February 10, 2025
        expected_previous_monday = datetime(2025, 2, 3, tzinfo=utc)  # Monday, February 3, 2025

        # Call the function with the specific payroll date
        actual_start_time, actual_end_time = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_start_time, expected_previous_monday)
        self.assertEqual(actual_end_time, expected_current_monday)

    def test_calculate_date_range_on_first_day_of_month(self):
        # Define a payroll date on the first day of the month
        payroll_date = datetime(2025, 3, 1, tzinfo=utc)  # Saturday, March 1, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 24, tzinfo=utc)  # Monday, February 24, 2025
        expected_previous_monday = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025
    
        # Call the function with the specific payroll date
        actual_start_time, actual_end_time = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_start_time, expected_previous_monday)
        self.assertEqual(actual_end_time, expected_current_monday)

    def test_calculate_date_range_on_last_day_of_month(self):
        # Define a payroll date on the last day of the month
        payroll_date = datetime(2025, 2, 28, tzinfo=utc)  # Friday, February 28, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 24, tzinfo=utc)  # Monday, February 24, 2025
        expected_previous_monday = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025
        expected_start_time = expected_previous_monday + timedelta(minutes=1)  # 12:01 AM UTC on February 17, 2025
        expected_end_time = expected_current_monday  # 12:00 AM UTC on February 24, 2025

        # Call the function with the specific payroll date
        actual_start_time, actual_end_time = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_start_time, expected_previous_monday)
        self.assertEqual(actual_end_time, expected_current_monday)

    def test_calculate_date_range_on_february_18(self):
        # Define a payroll date on February 18
        payroll_date = datetime(2025, 2, 18, tzinfo=utc)  # Tuesday, February 18, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025
        expected_previous_monday = datetime(2025, 2, 10, tzinfo=utc)  # Monday, February 10, 2025
        # 12:00 AM UTC on February 17, 2025

        # Call the function with the specific payroll date
        actual_start_time, actual_end_time = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_start_time, expected_previous_monday)
        self.assertEqual(actual_end_time, expected_current_monday)

    def test_calculate_date_range_on_february_24(self):
        # Define a payroll date on February 24
        payroll_date = datetime(2025, 2, 24, tzinfo=utc)  # Monday, February 24, 2025

        # Expected values
        expected_current_monday = datetime(2025, 2, 24, tzinfo=utc)  # Monday, February 24, 2025
        expected_previous_monday = datetime(2025, 2, 17, tzinfo=utc)  # Monday, February 17, 2025
        
        # Call the function with the specific payroll date
        actual_start_time, actual_end_time = calculate_payment_period(pay_roll_date=payroll_date)

        # Assert that the actual date range matches the expected date range
        self.assertEqual(actual_start_time, expected_previous_monday)
        self.assertEqual(actual_end_time, expected_current_monday)

class CalculateTotalHoursTest(TestCase):

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')

    def create_booking(self, start_time, end_time, creator_status='confirmed', trailblazer_status='confirmed'):
        booking = Booking.objects.create(
            booked_by=self.user,
            start_time=start_time,
            end_time=end_time,
            creator_status=creator_status
        )
        
        TrailblazerBookingStatus.objects.create(
            booking=booking,
            trailblazer=self.user,
            status=trailblazer_status
        )

        return booking

    def test_no_bookings(self):
        start_time = datetime(2025, 2, 10, tzinfo=utc)
        end_time = datetime(2025, 2, 17, tzinfo=utc)
        trailblazer_hours = calculate_total_hours(start_time, end_time)
        self.assertEqual(len(trailblazer_hours), 0)

    def test_creator_cancelled_booking(self):
        start_time = datetime(2025, 2, 10, tzinfo=utc)
        end_time = datetime(2025, 2, 17, tzinfo=utc)
        self.create_booking(start_time=start_time + timedelta(hours=1), end_time=start_time + timedelta(hours=2), creator_status='cancelled')
        trailblazer_hours = calculate_total_hours(start_time, end_time)
        self.assertEqual(len(trailblazer_hours), 0)

    """
    If a booking starts before the end of a payment period and ends within the next payment period,
    the total hours of that booking is added to the payment period in which it started.
    """
    def test_booking_spanning_boundaries(self):
        start_time = datetime(2025, 2, 10, tzinfo=utc)
        end_time = datetime(2025, 2, 17, tzinfo=utc)
        #TEST Calculated payment period
        self.create_booking(start_time=start_time - timedelta(hours=1), end_time=start_time + timedelta(hours=1))
        trailblazer_hours = calculate_total_hours(start_time, end_time)
        self.assertEqual(trailblazer_hours, [])

    def test_booking_spanning_two_periods(self):
        # First payment period
        start_time_1 = datetime(2025, 2, 3, tzinfo=utc) + timedelta(minutes=1)  # 12:01 AM on February 3, 2025
        end_time_1 = datetime(2025, 2, 10, tzinfo=utc)  # 12:00 AM on February 10, 2025

        # Second payment period
        start_time_2 = datetime(2025, 2, 10, tzinfo=utc) + timedelta(minutes=1)  # 12:01 AM on February 10, 2025
        end_time_2 = datetime(2025, 2, 17, tzinfo=utc)  # 12:00 AM on February 17, 2025

        # Create a booking that spans across the boundary times
        booking_start_time = end_time_1 - timedelta(hours=1)  # 11:00 PM on February 9, 2025
        booking_end_time = start_time_2 + timedelta(hours=1)  # 1:00 AM on February 10, 2025
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time)

        # Calculate the total hours within the first payment period
        trailblazer_hours_1 = calculate_total_hours(start_time_1, end_time_1)
        self.assertEqual(trailblazer_hours_1[0]['total_hours'], timedelta(hours=2, minutes=1))

        # Calculate the total hours within the second payment period
        trailblazer_hours_2 = calculate_total_hours(start_time_2, end_time_2)
        self.assertEqual(trailblazer_hours_2, [])

    def test_multiple_bookings_for_same_trailblazer(self):
        start_time = datetime(2025, 2, 10, tzinfo=utc)
        end_time = datetime(2025, 2, 17, tzinfo=utc)
        self.create_booking(start_time=start_time + timedelta(hours=1), end_time=start_time + timedelta(hours=2))
        self.create_booking(start_time=start_time + timedelta(hours=3), end_time=start_time + timedelta(hours=4))
        trailblazer_hours = calculate_total_hours(start_time, end_time)
        self.assertEqual(len(trailblazer_hours), 1)
        self.assertEqual(trailblazer_hours[0]['total_hours'], timedelta(hours=2))

class ConvertTotalHoursTest(TestCase):

    def test_convert_total_hours(self):
        trailblazer_hours = [{'trailblazer': 1, 'total_hours': timedelta(hours=2)}]
        converted_hours = convert_total_hours(trailblazer_hours)
        self.assertEqual(converted_hours[0]['total_hours'], 2.0)

    def test_convert_total_hours_with_no_hours(self):
        trailblazer_hours = [{'trailblazer': 1, 'total_hours': None}]
        converted_hours = convert_total_hours(trailblazer_hours)
        self.assertEqual(converted_hours[0]['total_hours'], 0.0)

class UpdatePaymentPeriodTrailblazerHoursTest(TestCase):

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')

    def tearDown(self):
        PaymentPeriodTrailblazerHours.objects.all().delete()

    def test_update_trailblazer_hours_table(self):
        payment_period_start = datetime(2025, 2, 10, tzinfo=utc)
        payment_period_end = datetime(2025, 2, 17, tzinfo=utc)
        trailblazer_hours = [{'trailblazer': self.user.id, 'total_hours': 2.0}]
        update_payment_period_trailblazer_hours_table(trailblazer_hours, payment_period_start, payment_period_end)
        trailblazer_hours_data = PaymentPeriodTrailblazerHours.objects.all()
        self.assertEqual(len(trailblazer_hours_data), 1)
        self.assertEqual(trailblazer_hours_data[0].trailblazer, self.user)
        self.assertEqual(trailblazer_hours_data[0].total_hours, 2.0)
        self.assertEqual(trailblazer_hours_data[0].payment_period_start, payment_period_start)
        self.assertEqual(trailblazer_hours_data[0].payment_period_end, payment_period_end)

    def test_update_trailblazer_hours_table_with_no_hours(self):
        payment_period_start = datetime(2025, 2, 10, tzinfo=utc)
        payment_period_end = datetime(2025, 2, 17, tzinfo=utc)
        trailblazer_hours = [{'trailblazer': self.user.id, 'total_hours': 0.0}]
        update_payment_period_trailblazer_hours_table(trailblazer_hours, payment_period_start, payment_period_end)
        trailblazer_hours_data = PaymentPeriodTrailblazerHours.objects.all()
        self.assertEqual(len(trailblazer_hours_data), 1)
        self.assertEqual(trailblazer_hours_data[0].trailblazer_id, self.user.id)
        self.assertEqual(trailblazer_hours_data[0].total_hours, 0.0)
        self.assertEqual(trailblazer_hours_data[0].payment_period_start, payment_period_start)
        self.assertEqual(trailblazer_hours_data[0].payment_period_end, payment_period_end)

    def test_update_trailblazer_hours_table_clears_existing_data(self):
        # Create existing data
        PaymentPeriodTrailblazerHours.objects.create(trailblazer=self.user, total_hours=1.0, payment_period_start=datetime(2025, 2, 3, tzinfo=utc), payment_period_end=datetime(2025, 2, 10, tzinfo=utc))
        payment_period_start = datetime(2025, 2, 10, tzinfo=utc)
        payment_period_end = datetime(2025, 2, 17, tzinfo=utc)
        trailblazer_hours = [{'trailblazer': self.user.id, 'total_hours': 2.0}]
        update_payment_period_trailblazer_hours_table(trailblazer_hours, payment_period_start, payment_period_end)
        trailblazer_hours_data = PaymentPeriodTrailblazerHours.objects.all()
        self.assertEqual(len(trailblazer_hours_data), 1)
        self.assertEqual(trailblazer_hours_data[0].trailblazer_id, self.user.id)
        self.assertEqual(trailblazer_hours_data[0].total_hours, 2.0)
        self.assertEqual(trailblazer_hours_data[0].payment_period_start, payment_period_start)
        self.assertEqual(trailblazer_hours_data[0].payment_period_end, payment_period_end)

class PaymentPeriodTrailblazerHoursViewTest(TransactionTestCase):

    def setUp(self):
        self.client = APIClient()
        """
        The APIClient class is a test client provided by the Django REST framework. 
        It is used to simulate HTTP requests to your API endpoints
        The APIClient simulates requests by constructing them in memory and passing them directly to the Django view functions. 
        and the responses are determined by the logic you have implemented in your views
        
        """
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.client.force_authenticate(user=self.user)
        self.api_key = settings.API_KEY

    def tearDown(self):
        Booking.objects.all().delete()
        TrailblazerBookingStatus.objects.all().delete()
        PaymentPeriodTrailblazerHours.objects.all().delete()
        User.objects.all().delete()

    def create_booking(self, start_time, end_time, creator_status='confirmed', trailblazer_status='confirmed'):
        booking = Booking.objects.create(
            booked_by=self.user,
            start_time=start_time,
            end_time=end_time,
            creator_status=creator_status
        )
        
        TrailblazerBookingStatus.objects.create(
            booking=booking,
            trailblazer=self.user,
            status=trailblazer_status
        )

        return booking

    def test_hours_not_counted_outside_date_range(self):
        # Create a booking outside the date range
        start_time = timezone.now() - timedelta(days=21, hours=1)
        end_time = timezone.now() - timedelta(days=21)
        self.create_booking(start_time=start_time, end_time=end_time)

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)
 
    def test_hours_not_counted_for_cancelled_counselor_confirmed_trailblazer(self):
        # Calculate the current payment period
        payroll_run_date = timezone.now().astimezone(timezone.utc)
        start_time, end_time = calculate_payment_period(pay_roll_date=payroll_run_date)

        # Create a booking within the date range but with cancelled creator status
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = booking_start_time + timedelta(hours=1)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time, creator_status='cancelled')

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_hours_not_counted_for_cancelled_counselor_pending_trailblazer(self):
        # Calculate the current payment period
        payroll_run_date = timezone.now().astimezone(timezone.utc)
        start_time, end_time = calculate_payment_period(pay_roll_date=payroll_run_date)

        # Create a booking within the date range but with cancelled creator status and pending trailblazer status
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = booking_start_time + timedelta(hours=1)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time, creator_status='cancelled', trailblazer_status='pending')

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_hours_not_counted_for_cancelled_counselor_cancelled_trailblazer(self):
        # Calculate the current payment period
        payroll_run_date = timezone.now().astimezone(timezone.utc)
        start_time, end_time = calculate_payment_period(pay_roll_date=payroll_run_date)

        # Create a booking within the date range but with cancelled creator status and cancelled trailblazer status
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = booking_start_time + timedelta(hours=1)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time, creator_status='cancelled', trailblazer_status='cancelled')  

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_hours_not_counted_for_confirmed_counselor_pending_trailblazer(self):
        # Calculate the current payment period
        payroll_run_date = timezone.now().astimezone(timezone.utc)
        start_time, end_time = calculate_payment_period(pay_roll_date=payroll_run_date)

        # Create a booking within the date range but with pending trailblazer status
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = booking_start_time + timedelta(hours=1)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time, trailblazer_status='pending')

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_hours_not_counted_for_confirmed_counselor_cancelled_trailblazer(self):
        # Calculate the current payment period
        payroll_run_date = timezone.now().astimezone(timezone.utc)
        start_time, end_time = calculate_payment_period(pay_roll_date=payroll_run_date)

        # Create a booking within the date range but with cancelled trailblazer status
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = booking_start_time + timedelta(hours=1)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time, trailblazer_status='cancelled')

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 0)

    def test_hours_counted_for_confirmed_counselor_confirmed_trailblazer(self):
        # Calculate the current payment period
        payroll_run_date = timezone.now().astimezone(timezone.utc)
        start_time, end_time = calculate_payment_period(pay_roll_date=payroll_run_date)

        # Create a booking within the date range
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = booking_start_time + timedelta(hours=1)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time)

        # Debug statements to verify the booking times
        print(f"Start Time: {start_time}")
        print(f"End Time: {end_time}")
        print(f"Booking Start Time: {booking_start_time}")
        print(f"Booking End Time: {booking_end_time}")

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        print(f"Response Data: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['total_hours'], 1)  # 1 hour
        
         # Convert the response data to datetime objects
        response_start_time = parse_datetime(response.data[0]['payment_period_start'])
        response_end_time = parse_datetime(response.data[0]['payment_period_end'])

        # Assert the converted datetime objects
        self.assertEqual(response_start_time, start_time)
        self.assertEqual(response_end_time, end_time)

    def test_booking_at_start_boundary(self):
        # Create a booking that starts exactly at the start boundary
        current_monday = timezone.now().astimezone(timezone.utc) - timedelta(days=timezone.now().weekday())
        current_monday = current_monday.replace(hour=0, minute=0, second=0, microsecond=0)
        previous_monday = current_monday - timedelta(days=7)
        start_time = previous_monday
        end_time = start_time + timedelta(hours=1)
        self.create_booking(start_time=start_time, end_time=end_time)

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data[0]['total_hours'], 1)  # 1 hour
         # Convert the response data to datetime objects
        response_start_time = parse_datetime(response.data[0]['payment_period_start'])
        response_end_time = parse_datetime(response.data[0]['payment_period_end'])

        # Assert the converted datetime objects
        self.assertEqual(response_start_time, previous_monday)
        self.assertEqual(response_end_time, current_monday)

    def test_booking_at_end_boundary(self):
        # Create a booking that ends exactly at the end boundary
        current_monday = timezone.now().astimezone(timezone.utc) - timedelta(days=timezone.now().weekday())
        current_monday = current_monday.replace(hour=0, minute=0, second=0, microsecond=0)
        end_time = current_monday
        start_time = end_time - timedelta(hours=1)
        self.create_booking(start_time=start_time, end_time=end_time)

        #Debug statements to verify the booking times
        print(f"Current Monday: {current_monday}")
        print(f"Start Time: {start_time}")
        print(f"End Time: {end_time}")

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        print(f"Response Data: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data[0]['total_hours'], 1)  # 1 hour

         # Convert the response data to datetime objects
        response_start_time = parse_datetime(response.data[0]['payment_period_start'])
        response_end_time = parse_datetime(response.data[0]['payment_period_end'])

        # Assert the converted datetime objects
        self.assertEqual(response_start_time, current_monday - timedelta(days=7))
        self.assertEqual(response_end_time, current_monday)
        
    def test_booking_in_different_timezones(self):
        # Create a booking in a different timezone (e.g., US/Pacific)
        pacific = pytz.timezone('US/Pacific')
        current_monday = timezone.now().astimezone(timezone.utc) - timedelta(days=timezone.now().weekday())
        current_monday = current_monday.replace(hour=0, minute=0, second=0, microsecond=0)
        previous_monday = current_monday - timedelta(days=7)
        previous_monday_naive = previous_monday.replace(tzinfo=None)  # Make the datetime naive
        start_time = pacific.localize(previous_monday_naive + timedelta(minutes=1))
        end_time = start_time + timedelta(hours=1)
        self.create_booking(start_time=start_time.astimezone(timezone.utc), end_time=end_time.astimezone(timezone.utc))

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['total_hours'], 1)  # 1 hour
            # Convert the response data to datetime objects
        response_start_time = parse_datetime(response.data[0]['payment_period_start'])
        response_end_time = parse_datetime(response.data[0]['payment_period_end'])

        # Assert the converted datetime objects
        self.assertEqual(response_start_time, previous_monday)
        self.assertEqual(response_end_time, current_monday)

    def test_payroll_run_on_later_day(self):
        # Simulate running payroll on a later day before the next Monday
        payroll_run_date = datetime(2025, 2, 20, tzinfo=timezone.utc)  # Simulate running payroll on Thursday, February 20th
        current_monday = payroll_run_date - timedelta(days=payroll_run_date.weekday())
        previous_monday = current_monday - timedelta(days=7)
        start_time = previous_monday + timedelta(minutes=1)
        end_time = current_monday

        # Debug statements to verify the date range
        print(f"Payroll Run Date: {payroll_run_date}")
        print(f"Current Monday: {current_monday}")
        print(f"Previous Monday: {previous_monday}")
        print(f"Start Time: {start_time}")
        print(f"End Time: {end_time}")

        # Create a booking within the date range
        booking_start_time = start_time + timedelta(hours=1)
        booking_end_time = start_time + timedelta(hours=2)
        self.create_booking(start_time=booking_start_time, end_time=booking_end_time)

        # Debug statements to verify the booking times
        print(f"Booking Start Time: {booking_start_time}")
        print(f"Booking End Time: {booking_end_time}")

        # Include the API key in the headers
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(reverse('trailblazer-hours'))
        print(f"Response Data: {response.data}")

        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data), 1)
        self.assertEqual(response.data[0]['total_hours'], 1)  # 1 hour

        # Convert the response data to datetime objects
        response_start_time = parse_datetime(response.data[0]['payment_period_start'])
        response_end_time = parse_datetime(response.data[0]['payment_period_end'])

        # Assert the converted datetime objects
        self.assertEqual(response_start_time, previous_monday)
        self.assertEqual(response_end_time, current_monday)



class PaymentWebhookListenerViewTests(TestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(username='testuser', email='<EMAIL>', password='password')
        self.api_key = settings.API_KEY
        self.url = reverse('update-payment-log')
        self.payment_log = PaymentLog.objects.create(
            user=self.user,
            payout_item_id='example_payout_item_id',
            payout_batch_id='example_payout_batch_id',
            payment_status='PENDING',
        )
    
    def tearDown(self):
        PaymentLog.objects.all().delete()
        SkippedPaymentLog.objects.all().delete()

    def test_successful_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(SkippedPaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        payment_log = PaymentLog.objects.get(payout_item_id='example_payout_item_id')
        self.assertEqual(payment_log.payment_status, 'SUCCESS')
    
    def test_failed_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'FAILED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.FAILED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id', payout_batch_id='example_payout_batch_id')
        self.assertEqual(skipped_payment_log.payment_status, 'FAILED')

    def test_blocked_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'BLOCKED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.BLOCKED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id', payout_batch_id='example_payout_batch_id')
        self.assertEqual(skipped_payment_log.payment_status, 'BLOCKED')

    def test_canceled_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'CANCELED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.CANCELED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id', payout_batch_id='example_payout_batch_id')
        self.assertEqual(skipped_payment_log.payment_status, 'CANCELED')

    def test_denied_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id', 
            'transaction_status': 'DENIED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.DENIED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id', payout_batch_id='example_payout_batch_id')
        self.assertEqual(skipped_payment_log.payment_status, 'DENIED')

    def test_refunded_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id', 
            'transaction_status': 'REFUNDED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.REFUNDED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id', payout_batch_id='example_payout_batch_id')
        self.assertEqual(skipped_payment_log.payment_status, 'REFUNDED')

    def test_returned_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id', 
            'transaction_status': 'RETURNED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.RETURNED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id', payout_batch_id='example_payout_batch_id')
        self.assertEqual(skipped_payment_log.payment_status, 'RETURNED')

    def test_held_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id', 
            'transaction_status': 'HELD',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.HELD',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(SkippedPaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        payment_log = PaymentLog.objects.get(payout_item_id='example_payout_item_id')
        self.assertEqual(payment_log.payment_status, 'HELD')

    def test_unclaimed_payout_item_event(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id', 
            'transaction_status': 'UNCLAIMED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.UNCLAIMED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        payment_log = PaymentLog.objects.get(payout_item_id='example_payout_item_id')
        self.assertEqual(payment_log.payment_status, 'UNCLAIMED')

    def test_processing_payout_batch_event(self):
        payload = {
            'resource_type': 'payouts',
            'payout_batch_id': 'example_payout_batch_id',
            'batch_status': 'PROCESSING',
            'event_type': 'PAYMENT.PAYOUTSBATCH.PROCESSING',
            'time_completed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        PaymentLog.objects.create(
            user=self.user,
            payout_item_id='example_payout_item_id_1',
            payout_batch_id='example_payout_batch_id',
            payment_status='PENDING',
        )
        PaymentLog.objects.create(
            user=self.user,
            payout_item_id='example_payout_item_id_2',
            payout_batch_id='example_payout_batch_id',
            payment_status='PENDING',
        )
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        payment_logs = PaymentLog.objects.filter(payout_batch_id='example_payout_batch_id')
        self.assertEqual(len(payment_logs), 3)
        for payment_log in payment_logs:
            self.assertIn(payment_log.payout_item_id, ['example_payout_item_id', 'example_payout_item_id_1', 'example_payout_item_id_2'])
            self.assertEqual(payment_log.batch_status, 'PROCESSING')
            self.assertEqual(payment_log.payment_status, 'PENDING')

    def test_failed_payout_batch_event(self):
        payload = {
            'resource_type': 'payouts',
            'payout_batch_id': 'example_payout_batch_id',
            'batch_status': 'DENIED',
            'event_type': 'PAYMENT.PAYOUTSBATCH.DENIED',
            'error_message': 'example_error_message',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        PaymentLog.objects.create(
            user=self.user,
            payout_item_id='example_payout_item_id_1',
            payout_batch_id='example_payout_batch_id',
            payment_status='PENDING',
        )
        PaymentLog.objects.create(
            user=self.user,
            payout_item_id='example_payout_item_id_2',
            payout_batch_id='example_payout_batch_id',
            payment_status='PENDING',
        )
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_batch_id='example_payout_batch_id').exists())
        skipped_payment_logs = SkippedPaymentLog.objects.filter(payout_batch_id='example_payout_batch_id')
        self.assertEqual(len(skipped_payment_logs), 3)
        for skipped_payment_log in skipped_payment_logs:
            self.assertIn(skipped_payment_log.payout_item_id, ['example_payout_item_id', 'example_payout_item_id_1', 'example_payout_item_id_2'])
            self.assertEqual(skipped_payment_log.payment_status, 'DENIED')
            self.assertEqual(skipped_payment_log.batch_status, 'DENIED')

    #test batch success but item fail 
    def test_successful_payout_batch_event_with_failed_payout_item(self):
        payload = {
            'resource_type': 'payouts',
            'payout_batch_id': 'example_payout_batch_id',
            'batch_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTSBATCH.SUCCESS',
            'error_message': 'example_error_message',
            'time_completed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        payload_2 = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'FAILED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.FAILED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        response = self.client.post(self.url, data=json.dumps(payload_2), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(PaymentLog.objects.filter(payout_batch_id='example_payout_batch_id').exists())
        skipped_payment_logs = SkippedPaymentLog.objects.filter(payout_batch_id='example_payout_batch_id')
        self.assertEqual(len(skipped_payment_logs), 1)
        for skipped_payment_log in skipped_payment_logs:
            self.assertIn(skipped_payment_log.payout_item_id, ['example_payout_item_id', 'example_payout_item_id_1', 'example_payout_item_id_2'])
            self.assertEqual(skipped_payment_log.payment_status, 'FAILED')
            self.assertEqual(skipped_payment_log.batch_status, 'SUCCESS')
         
    def test_webhook_with_missing_resource_type(self):
        payload = {
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'payout_item_id': 'example_payout_item_id',
            'transaction_status': 'SUCCESS',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        self.assertIn('resource_type is required', response.data['error'])

    def test_webhook_with_missing_event_type(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'transaction_status': 'SUCCESS',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        self.assertIn('Unknown event type', response.data['error'])

    def test_webhook_with_missing_transaction_status(self):
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'time_processed': datetime.now(timezone.utc).strftime('%Y-%m-%dT%H:%M:%SZ')
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 400)
        self.assertIn('transaction_status is required', response.data['error'])

    def test_webhook_with_older_time_processed(self):
    # Create the initial payload with the current time in UTC
        current_time_processed = datetime.now().strftime('%Y-%m-%dT%H:%M:%SZ')
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'time_processed': current_time_processed
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('success', response.data.get('status'))
        self.assertFalse(SkippedPaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        payment_log = PaymentLog.objects.get(payout_item_id='example_payout_item_id')
        self.assertEqual(payment_log.payment_status, 'SUCCESS')

        # Create the second payload with an older time in UTC
        older_time_processed = (datetime.now() - timedelta(days=1)).strftime('%Y-%m-%dT%H:%M:%SZ')
        payload = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'time_processed': older_time_processed
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        self.assertIn('skipped', response.data.get('status'))
        self.assertTrue(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        self.assertFalse(SkippedPaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())

    def test_webhook_with_concurrent_events_old(self):
        time_processed_1 = (datetime.now(timezone.utc) - timedelta(days=1)).replace(hour=12, minute=0, second=1, microsecond=0).strftime('%Y-%m-%dT%H:%M:%SZ')
        time_processed_2 = (datetime.now(timezone.utc) - timedelta(days=1)).replace(hour=12, minute=0, second=0, microsecond=0).strftime('%Y-%m-%dT%H:%M:%SZ')

        payload_1 = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'FAILED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.FAILED',
            'time_processed': time_processed_1
        }

        payload_2 = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'time_processed': time_processed_2
        }

        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response_1 = self.client.post(self.url, data=json.dumps(payload_1), content_type='application/json')
        response_2 = self.client.post(self.url, data=json.dumps(payload_2), content_type='application/json')

        self.assertEqual(response_1.status_code, 200)
        self.assertEqual(response_2.status_code, 200)

        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id')
        self.assertEqual(skipped_payment_log.payment_status, 'FAILED')
    
    def test_webhook_with_concurrent_events(self):
        time_processed_1 = (datetime.now(timezone.utc) - timedelta(days=1)).replace(hour=12, minute=0, second=0, microsecond=0).strftime('%Y-%m-%dT%H:%M:%SZ')
        time_processed_2 = (datetime.now(timezone.utc) - timedelta(days=1)).replace(hour=12, minute=0, second=1, microsecond=0).strftime('%Y-%m-%dT%H:%M:%SZ')

        payload_1 = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.SUCCEEDED',
            'time_processed': time_processed_1
        }

        payload_2 = {
            'resource_type': 'payouts_item',
            'payout_item_id': 'example_payout_item_id',
            'payout_batch_id': 'example_payout_batch_id',
            'transaction_status': 'FAILED',
            'event_type': 'PAYMENT.PAYOUTS-ITEM.FAILED',
            'time_processed': time_processed_2
        }

        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response_1 = self.client.post(self.url, data=json.dumps(payload_1), content_type='application/json')
        response_2 = self.client.post(self.url, data=json.dumps(payload_2), content_type='application/json')

        self.assertEqual(response_1.status_code, 200)
        self.assertEqual(response_2.status_code, 200)

        self.assertFalse(PaymentLog.objects.filter(payout_item_id='example_payout_item_id').exists())
        skipped_payment_log = SkippedPaymentLog.objects.get(payout_item_id='example_payout_item_id')
        self.assertEqual(skipped_payment_log.payment_status, 'FAILED')
    
    def test_batch_level_event_with_higher_precedence_status(self):
        # Set a specific time_completed value
        specific_time_completed = (datetime.now(timezone.utc) + timedelta(minutes=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

        # Update the initial PaymentLog object with the specific time_completed
        payment_log = PaymentLog.objects.get(payout_batch_id='example_payout_batch_id')
        payment_log.batch_status = 'PROCESSING'
        payment_log.time_completed = specific_time_completed
        payment_log.save()

        # Create the payload with the same specific time_completed
        payload = {
            'resource_type': 'payouts',
            'payout_batch_id': 'example_payout_batch_id',
            'batch_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTSBATCH.SUCCESS',
            'time_completed': specific_time_completed
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        payment_log = PaymentLog.objects.get(payout_batch_id='example_payout_batch_id')
        self.assertEqual(payment_log.batch_status, 'SUCCESS')
        self.assertEqual(payment_log.time_completed.strftime('%Y-%m-%dT%H:%M:%SZ'), specific_time_completed)

    def test_batch_level_event_with_lower_precedence_status(self):
         # Set a specific time_completed value
        specific_time_completed = (datetime.now(timezone.utc) + timedelta(minutes=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

        # Update the initial PaymentLog object with the specific time_completed
        payment_log = PaymentLog.objects.get(payout_batch_id='example_payout_batch_id')
        payment_log.batch_status = 'SUCCESS'
        payment_log.time_completed = specific_time_completed  
        payment_log.save()

        payload = {
            'resource_type': 'payouts',
            'payout_batch_id': 'example_payout_batch_id',
            'batch_status': 'PROCESSING',
            'event_type': 'PAYMENT.PAYOUTSBATCH.SUCCESS',
            'time_completed': specific_time_completed
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        payment_log = PaymentLog.objects.get(payout_batch_id='example_payout_batch_id')
        self.assertEqual(payment_log.batch_status, 'SUCCESS')
        self.assertEqual(payment_log.time_completed.strftime('%Y-%m-%dT%H:%M:%SZ'), specific_time_completed)

    def test_success_after_processing(self):
        # Update the initial PaymentLog object with the specific time_completed
        payment_log = PaymentLog.objects.get(payout_batch_id='example_payout_batch_id')
        payment_log.batch_status = 'PROCESSING'
        payment_log.time_completed = (datetime.now(timezone.utc)- timedelta(minutes=1)).strftime('%Y-%m-%dT%H:%M:%SZ')
        payment_log.save()

        specific_time_completed = (datetime.now(timezone.utc) + timedelta(minutes=1)).strftime('%Y-%m-%dT%H:%M:%SZ')

        payload = {
            'resource_type': 'payouts',
            'payout_batch_id': 'example_payout_batch_id',
            'batch_status': 'SUCCESS',
            'event_type': 'PAYMENT.PAYOUTSBATCH.SUCCESS',
            'time_completed': specific_time_completed
        }

        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data=json.dumps(payload), content_type='application/json')
        self.assertEqual(response.status_code, 200)
        payment_log = PaymentLog.objects.get(payout_batch_id='example_payout_batch_id')
        self.assertEqual(payment_log.batch_status, 'SUCCESS')
        self.assertEqual(payment_log.time_completed.strftime('%Y-%m-%dT%H:%M:%SZ'), specific_time_completed)


class SkippedPaymentLogTests(APITestCase):
    def setUp(self):
        self.client = APIClient()
        self.user = User.objects.create_user(username='testuser', email='<EMAIL>', password='testpass')
        self.client.force_authenticate(user=self.user)
        self.api_key = settings.API_KEY
        self.url = reverse('skipped-payment-logs')  # Ensure this matches your URL configuration

    def tearDown(self):
        SkippedPaymentLog.objects.all().delete()
    
    def test_successful_creation_of_skipped_payment_log(self):
        data = {
            'paypal_email': '<EMAIL>',
            'hours': 10,
            'error_message': 'Payment failed due to insufficient funds',
            'payment_status': 'FAILED',
            'payment_date': timezone.now(),
            'payment_period_start': timezone.now(),
            'payment_period_end': timezone.now(),
            'trailblazer': self.user.id,
            'payout_batch_id': 'batch_id_123',
            'payout_item_id': 'item_id_123',
            'time_processed': timezone.now()
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(SkippedPaymentLog.objects.filter(paypal_email='<EMAIL>').exists())

    def test_missing_required_fields(self):
        data = {
            'hours': 10,
            'payment_status': 'FAILED'
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('paypal_email', response.data)
        self.assertIn('error_message', response.data)

    def test_invalid_data_types(self):
        data = {
            'paypal_email': '<EMAIL>',
            'hours': 'ten',  # Invalid data type
            'error_message': 'Payment failed due to insufficient funds',
            'payment_status': 'FAILED',
            'payment_date': timezone.now(),
            'payment_period_start': timezone.now(),
            'payment_period_end': timezone.now(),
            'trailblazer': self.user.id,
            'payout_batch_id': 'batch_id_123',
            'payout_item_id': 'item_id_123',
            'time_processed': timezone.now()
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('hours', response.data)

    def test_edge_cases_for_field_lengths(self):
        data = {
            'paypal_email': 'a' * 254,
            'hours': 10,
            'error_message': 'a' * 255,
            'payment_status': 'a' * 1000,
            'payment_date': timezone.now(),
            'payment_period_start': timezone.now(),
            'payment_period_end': timezone.now(),
            'trailblazer': self.user.id,
            'payout_batch_id': 'a' * 255,
            'payout_item_id': 'a' * 255,
            'time_processed': timezone.now()
        }
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)
        self.assertTrue(SkippedPaymentLog.objects.filter(paypal_email='a' * 254).exists())

    def test_list_skipped_payment_logs(self):
        SkippedPaymentLog.objects.create(
            paypal_email='<EMAIL>',
            hours=10,
            error_message='Payment failed due to insufficient funds',
            payment_status='FAILED',
            payment_date=timezone.now(),
            payment_period_start=timezone.now(),
            payment_period_end=timezone.now(),
            trailblazer=self.user,
            payout_batch_id='batch_id_123',
            payout_item_id='item_id_123',
            time_processed=timezone.now()
        )
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.get(self.url, format='json')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(SkippedPaymentLog.objects.filter(payout_batch_id = 'batch_id_123').count(), 1)

    def test_authentication_and_permissions(self):
        data = {
            'paypal_email': '<EMAIL>',
            'hours': 10,
            'error_message': 'Payment failed due to insufficient funds',
            'payment_status': 'FAILED',
            'payment_date': timezone.now(),
            'payment_period_start': timezone.now(),
            'payment_period_end': timezone.now(),
            'trailblazer': self.user.id,
            'payout_batch_id': 'batch_id_123',
            'payout_item_id': 'item_id_123',
            'time_processed': timezone.now()
        }
        # Test without authentication
        self.client.logout()
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_403_FORBIDDEN)

        # Test with authentication
        self.client.force_authenticate(user=self.user)
        self.client.credentials(HTTP_AUTHORIZATION=self.api_key)
        response = self.client.post(self.url, data, format='json')
        self.assertEqual(response.status_code, status.HTTP_201_CREATED)


    