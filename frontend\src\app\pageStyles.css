:root {
    --foreground-rgb: 0, 0, 0;
    --background-start-rgb: 214, 219, 220;
    --background-end-rgb: 255, 255, 255;
}

/* Ensure tags have consistent styling */
.filter-tag {
    @apply flex items-center bg-gray-200 text-gray-700 px-3 py-1 rounded-full
}

@media (prefers-color-scheme: dark) {
    :root {
    --foreground-rgb: 255, 255, 255;
    --background-start-rgb: 0, 0, 0;
    --background-end-rgb: 0, 0, 0;
    }
}

body {
    color: rgb(var(--foreground-rgb));
    background: linear-gradient(
        to bottom,
        transparent,
        rgb(var(--background-end-rgb))
    )
    rgb(var(--background-start-rgb));
    font-family: 'Nunito Sans', sans-serif;
}
