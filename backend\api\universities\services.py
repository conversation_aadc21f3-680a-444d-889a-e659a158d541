import logging
import os
import json
from typing import <PERSON><PERSON>, List, Dict, Any, Optional

from django.conf import settings
from openai import OpenAI
from pgvector.django import CosineDistance
from tenacity import retry, wait_random_exponential, stop_after_attempt, retry_if_exception_type
from langchain.chat_models import init_chat_model

from api.users.models import HighSchoolStudentProfile
from api.universities.models import University

logger = logging.getLogger(__name__)

class OpenAIServiceException(Exception):
    """Exception raised for errors in the OpenAI service."""
    pass

# Initialize OpenAI client
client = OpenAI(api_key=settings.OPENAI_API_KEY)

@retry(
    wait=wait_random_exponential(min=1, max=20),
    stop=stop_after_attempt(3),
    retry=retry_if_exception_type(Exception),
    reraise=True
)
def get_embedding(text: str, model: str = "text-embedding-3-small") -> List[float]:
    """
    Get an embedding vector for the given text using OpenAI's API.
    
    Args:
        text: The text to generate an embedding for
        model: The embedding model to use
        
    Returns:
        A list of floating point numbers representing the embedding
        
    Raises:
        OpenAIServiceException: If there's an issue with the OpenAI API after retries
    """
    try:
        # Normalize text by replacing newlines with spaces
        text = text.replace("\n", " ")
        
        # Call OpenAI API to generate embedding
        response = client.embeddings.create(
            input=[text],
            model=model
        )
        
        # Extract and return the embedding
        return response.data[0].embedding
    except Exception as e:
        logger.error(f"Error generating embedding: {str(e)}")
        raise OpenAIServiceException(f"Failed to generate embedding: {str(e)}")

def validate_profile(profile: HighSchoolStudentProfile) -> None:
    """
    Validate that the profile contains sufficient information to generate a description.
    
    Args:
        profile: The student profile to validate
        
    Raises:
        ValueError: If the profile is missing required information
    """
    if not profile:
        raise ValueError("Profile cannot be None")
    
    # Check for minimal required fields
    # if not profile.intended_majors and not profile.interests:
    #     raise ValueError("Profile must include at least intended majors or interests")

def generate_ideal_university_description(profile: HighSchoolStudentProfile) -> Tuple[str, List[float]]:
    """
    Generate an ideal university description and embedding for a high school student profile.
    
    Args:
        profile: The high school student profile to generate a description for
        
    Returns:
        A tuple containing the generated description and its embedding vector
        
    Raises:
        ValueError: If the profile is invalid
        OpenAIServiceException: If there's an issue generating the embedding
    """
    # Validate the profile
    validate_profile(profile)
    
    # Build the description based on profile fields
    description_parts = []
    
    # Basic information
    description_parts.append("An ideal university for this student would")
    
    # Academic preferences
    if profile.intended_majors:
        majors_str = ", ".join(profile.intended_majors)
        description_parts.append(f"offer strong programs in {majors_str}")
    
    if profile.interests:
        interests_str = ", ".join(profile.interests if isinstance(profile.interests, list) else [profile.interests])
        description_parts.append(f"provide opportunities to pursue interests in {interests_str}")
    
    # Location preferences
    if profile.geographic_preference_type == "states" and profile.preferred_states:
        states_str = ", ".join(profile.preferred_states)
        description_parts.append(f"be located in one of these states: {states_str}")
    elif profile.geographic_preference_type == "zip" and profile.preferred_zip and profile.preferred_radius:
        description_parts.append(f"be within {profile.preferred_radius} miles of {profile.preferred_zip}")
    elif profile.geographic_preference_type == "none":
        description_parts.append("have no specific geographic preference")
    
    # College type preferences
    if "no_preference" in profile.college_type_preferences:
        description_parts.append("have no specific preference for college type")
    elif profile.college_type_preferences:
        types_str = ", ".join(profile.college_type_preferences)
        description_parts.append(f"be a {types_str} institution")
    
    # Location type preferences
    if "no_preference" in profile.location_type_preferences:
        description_parts.append("have no specific preference for location type")
    elif profile.location_type_preferences:
        loc_types_str = ", ".join(profile.location_type_preferences)
        description_parts.append(f"be in a {loc_types_str} setting")
    
    # Financial considerations
    if profile.financial_aid_need:
        if profile.financial_aid_need == "high":
            description_parts.append("offer substantial financial aid and scholarships")
        elif profile.financial_aid_need == "medium":
            description_parts.append("provide moderate financial aid options")
        elif profile.financial_aid_need == "low":
            description_parts.append("have affordable tuition or merit scholarships")
        elif profile.financial_aid_need == "no_need":
            description_parts.append("not require financial aid")
    
    # Academic profile matching
    if profile.unweighted_gpa is not None:
        if profile.unweighted_gpa >= 3.7:
            description_parts.append("have a moderately selective to highly selective admissions process")
        elif profile.unweighted_gpa >= 3.0:
            description_parts.append("have a moderately selective admissions process")
        else:
            description_parts.append("have an inclusive admissions process")
    
    # Test score considerations
    test_score_mentioned = False
    if profile.sat_math_score is not None and profile.sat_reading_score is not None:
        combined_sat = profile.sat_math_score + profile.sat_reading_score
        if combined_sat >= 1400:
            description_parts.append("match the student's high SAT scores")
            test_score_mentioned = True
    
    if profile.act_score is not None and not test_score_mentioned:
        if profile.act_score >= 30:
            description_parts.append("match the student's high ACT score")
    
    # Combine all parts into a cohesive description
    description = ". It should ".join(description_parts) + "."
    
    # Generate embedding for the description
    try:
        embedding = get_embedding(description)
        return description, embedding
    except OpenAIServiceException as e:
        logger.error(f"Failed to generate embedding for profile {profile.id}: {str(e)}")
        raise

def validate_profile_for_matching(profile: HighSchoolStudentProfile) -> None:
    """
    Validate that a profile exists and has the necessary data for university matching.
    
    Args:
        profile: The student profile to validate
        
    Raises:
        ValueError: If the profile is invalid or missing required fields
    """
    if not profile:
        raise ValueError("Profile cannot be None")
    
    # Change the check to avoid ambiguous truth value
    if not hasattr(profile, 'ideal_university_embedding') or profile.ideal_university_embedding is None:
        raise ValueError("Profile must have an ideal university embedding")

def get_similar_universities(profile: HighSchoolStudentProfile, limit: int = 100) -> List[University]:
    """
    Find universities similar to the profile's ideal university embedding.
    
    Args:
        profile: The student profile with an ideal university embedding
        limit: The maximum number of universities to return
        
    Returns:
        A list of University objects ordered by similarity
        
    Raises:
        ValueError: If the profile embedding is invalid
    """
    if profile.ideal_university_embedding is None:
        raise ValueError("Profile must have an ideal university embedding")
    
    # Convert embedding to a plain list if possible (e.g., if it's a numpy array)
    embedding = profile.ideal_university_embedding
    if hasattr(embedding, 'tolist'):
        embedding = embedding.tolist()
    
    # Perform vector similarity search using CosineDistance
    similar_universities = University.objects.order_by(
        CosineDistance('embedding', embedding)
    )[:limit]
    
    return list(similar_universities)

def include_value(val):
    if val is None:
        return False
    if isinstance(val, (list, str)) and len(val) == 0:
        return False
    return True

def get_profile_data(profile):
    fields = [
        "unweighted_gpa",
        "weighted_gpa",
        "sat_math_score",
        "sat_reading_score",
        "act_score",
        "high_school_name",
        "graduation_year",
        "intended_majors",
        "extracurriculars",
        "interests",
        "college_type_preferences",
        "location_type_preferences",
        "geographic_preference_type",
        "preferred_states",
        "preferred_zip",
        "preferred_radius",
        "country",
        "current_zip_code",
        "gender",
        "ethnicity",
        "household_income",
        "financial_aid_need"
    ]
    data = {}
    for field in fields:
        value = getattr(profile, field, None)
        if include_value(value):
            data[field] = value
    return data

def serialize_university(instance):
    """
    Serialize all fields for a Django model instance if they have a valid value.
    """
    data = {}
    for field in instance._meta.get_fields():
        if hasattr(field, 'attname') and field.attname != 'embedding':
            value = getattr(instance, field.attname, None)
            if include_value(value):
                data[field.name] = value
    return data

def classify_universities_with_llm(profile: HighSchoolStudentProfile, universities: List[University]) -> Dict[str, List[str]]:
    """
    Classify universities into safety, target, and reach categories using LangChain.
    
    Args:
        profile: The student profile
        universities: List of universities to classify
        
    Returns:
        Dictionary with categorized university IDs
        
    Raises:
        OpenAIServiceException: If there's an issue with the LLM
    """
    try:
        # Initialize the chat model
        
        chat_model = init_chat_model(
            "o3-mini",
            model_provider="openai", 
            openai_api_key=os.environ.get("OPENAI_API_KEY")
        )

        logger.info(f"Chat model class: {type(chat_model)}")
        logger.info(f"Model name used: {getattr(chat_model, 'model_name', 'unknown')}")
        
        '''
        chat_model = init_chat_model(
            "gpt-3.5-turbo",
            model_provider="openai", 
            openai_api_key=os.environ.get("OPENAI_API_KEY")
        )'''



        profile_data = get_profile_data(profile)

        # Prepare university data for classification
        universities_data = [serialize_university(univ) for univ in universities]

        # Construct prompt for classification
        prompt = f"""
        You are a university admissions expert tasked with classifying universities into three categories for a high school student.

        Student Profile:
        {json.dumps(profile_data, indent=2)}

        University Categories:
        - Safety: Universities where the student's academic profile is significantly stronger than the average admitted student. Acceptance is highly likely (>80% chance).
        - Target: Universities where the student's academic profile aligns with the average admitted student. Acceptance is plausible (40-60% chance).
        - Reach: Universities where the student's academic profile is somewhat below the average admitted student. Acceptance is possible but challenging (<30% chance).

        Classify the following universities into these three categories based on the student's profile and university data.

        University Data:
        {json.dumps(universities_data, indent=2)}

        Respond with a JSON object that has three keys: "safety_universities", "target_universities", and "reach_universities".
        Each key should contain a list of university IDs. Each list should have exactly 10 university IDs.

        Example response format:
        {{
          "safety_universities": ["123", "456", ...],
          "target_universities": ["789", "101", ...],
          "reach_universities": ["112", "131", ...]
        }}
        """
        
        # Invoke the LLM to classify universities
        response = chat_model.invoke(prompt)
        
        # Parse the response to get the classification
        if hasattr(response, 'content'):
            response_text = response.content
        else:
            response_text = str(response)
        
        # Extract JSON from the response
        # First try to find JSON in code blocks
        import re
        json_match = re.search(r'```(?:json)?\s*([\s\S]*?)\s*```', response_text)
        if json_match:
            json_str = json_match.group(1)
        else:
            # If no code blocks, try to find JSON directly
            json_str = response_text
        
        # Clean up potential non-JSON parts
        json_str = json_str.strip()
        if not json_str.startswith('{'):
            json_str = '{' + json_str.split('{', 1)[1]
        if not json_str.endswith('}'):
            json_str = json_str.rsplit('}', 1)[0] + '}'
        
        classification = json.loads(json_str)
        
        # Ensure we have all required categories
        if 'safety_universities' not in classification:
            classification['safety_universities'] = []
        if 'target_universities' not in classification:
            classification['target_universities'] = []
        if 'reach_universities' not in classification:
            classification['reach_universities'] = []
        
        # Limit to 10 universities per category
        classification['safety_universities'] = classification['safety_universities'][:10]
        classification['target_universities'] = classification['target_universities'][:10]
        classification['reach_universities'] = classification['reach_universities'][:10]
        
        return classification
        
    except Exception as e:
        logger.error(f"Error classifying universities: {str(e)}")
        raise OpenAIServiceException(f"Failed to classify universities: {str(e)}")

def match_universities(profile: HighSchoolStudentProfile) -> Dict[str, List[str]]:
    """
    Match a high school student with universities categorized as safety, target, and reach.
    
    Args:
        profile: The high school student profile to match
        
    Returns:
        A dictionary with three categories of university IDs:
        {
          "safety_universities": [list of 10 safety university ids],
          "target_universities": [list of 10 target university ids],
          "reach_universities": [list of 10 reach university ids]
        }
        
    Raises:
        ValueError: If the profile is invalid or empty
        OpenAIServiceException: If there's an issue with the LLM
    """
    try:
        # Validate the profile
        validate_profile_for_matching(profile)
        
        # Get similar universities
        similar_universities = get_similar_universities(profile, limit=30)
        
        # If we found fewer than 3 universities, return empty lists
        if len(similar_universities) < 3:
            return {
                "safety_universities": [],
                "target_universities": [],
                "reach_universities": []
            }
        
        # Classify universities
        classification = classify_universities_with_llm(profile, similar_universities)
        
        return classification
        
    except ValueError as e:
        logger.error(f"Invalid profile for matching: {str(e)}")
        raise
    except OpenAIServiceException as e:
        logger.error(f"OpenAI service error during matching: {str(e)}")
        raise
    except Exception as e:
        logger.error(f"Unexpected error during university matching: {str(e)}")
        raise