import logging
from rest_framework import views, status, filters, exceptions, viewsets, generics, serializers
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.authentication import TokenAuthentication
from rest_framework.exceptions import NotFound, PermissionDenied, NotAuthenticated, ValidationError
from django.shortcuts import get_object_or_404
from django.contrib.auth import get_user_model
from django.db.models import Q, CharField, Value, TextField
from django.contrib.postgres.search import TrigramSimilarity
from django.db.models.functions import Cast
from rest_framework.pagination import PageNumberPagination

from api.universities import services as uni_services
from api.universities.serializers import UniversitySerializer, UniversityDetailSerializer, ShortlistItemSerializer, UniversitySearchSerializer
from api.users.models import HighSchoolStudentProfile, CollegeStudentProfile
from api.universities.models import University, ShortlistItem
from api.users.permissions import IsHighSchoolStudent, IsOwner

User = get_user_model()
logger = logging.getLogger(__name__)

class RecommendationsView(generics.GenericAPIView):
    """
    API endpoint for retrieving university recommendations.
    
    GET /api/recommendations/
    
    Returns categorized recommendations (reach, target, safety) for the authenticated user.
    Each university now includes a 'bookmarked' attribute indicating if it's in the user's shortlist.
    
    Query parameters:
    - ordering: Field to order universities by (e.g., "acceptance_rate" or "-avg_net_price").
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]
    
    def permission_denied(self, request, message=None, code=None):
        # Override to return 401 for unauthenticated requests
        if not request.user or not request.user.is_authenticated:
            raise exceptions.NotAuthenticated(detail=message or "Authentication credentials were not provided.")
        return super().permission_denied(request, message, code)
    
    def get(self, request):
        # Explicitly check authentication to return 401 if needed
        if not request.user or not request.user.is_authenticated:
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
        
        # Retrieve the user's high school student profile
        try:
            profile = get_object_or_404(HighSchoolStudentProfile, user=request.user)
        except Exception:
            return Response({"detail": "Profile not found."}, status=status.HTTP_404_NOT_FOUND)

        if not profile.university_recommendations:
            return Response({"detail": "No recommendations available."}, status=status.HTTP_404_NOT_FOUND)

        recommendations = profile.university_recommendations
        # Get the ordering parameter, defaulting to 'institution'
        ordering = request.query_params.get('ordering', 'institution')

        valid_ordering_fields = [field.name for field in University._meta.fields if field.name != 'embedding']
        ordering_field = ordering[1:] if ordering.startswith('-') else ordering
        if ordering_field not in valid_ordering_fields:
            return Response({"detail": f"Invalid sorting field. Valid options are: {', '.join(valid_ordering_fields)}"}, status=status.HTTP_400_BAD_REQUEST)
        
        # Build response data by serializing universities per category
        response_data = {}

        all_unitids = []
        for category, university_ids in recommendations.items():
            all_unitids.extend(university_ids or [])
            if university_ids:
                universities = University.objects.filter(unitid__in=university_ids).order_by(ordering)
                serializer = UniversitySerializer(
                    universities, 
                    many=True, 
                    context={'request': request}
                )
                response_data[category] = serializer.data
            else:
                response_data[category] = []
        
        trailblazer_count = 0  # Default

        if all_unitids:
            universities = University.objects.filter(unitid__in=all_unitids)
            university_names = list(universities.values_list('institution', flat=True))

            if university_names:
                name_filter = Q()
                for name in university_names:
                    name_filter |= Q(university__icontains=name)

                trailblazer_count = CollegeStudentProfile.objects.filter(name_filter).count()

        response_data['connect_with_trailblazers'] = trailblazer_count

        return Response(response_data)

class UniversityDetailView(generics.RetrieveAPIView):
    """
    API endpoint that returns detailed information about a university.
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated]
    serializer_class = UniversityDetailSerializer
    lookup_field = 'unitid'
    
    def get_queryset(self):
        return University.objects.all()
    
    def get_object(self):
        try:
            return super().get_object()
        except Exception:
            raise NotFound(detail="University not found")

class ShortlistDeleteView(generics.DestroyAPIView):
    """
    DELETE API endpoint for removing a college from a user's shortlist.

    Permissions:
    - User must be authenticated as a high school student
    - User must own the shortlist item
    
    Returns:
    - 204 No Content on successful deletion
    - 404 Not Found if shortlist item doesn't exist
    """
    # Added IsAuthenticated to ensure unauthenticated users get a 401
    permission_classes = [IsAuthenticated, IsHighSchoolStudent, IsOwner]
    queryset = ShortlistItem.objects.all()

    def check_permissions(self, request):
        if not request.user or not request.user.is_authenticated:
            self.permission_denied(request, message="Authentication credentials were not provided.", code="not_authenticated")
        super().check_permissions(request)

    def get_object(self):
        """
        Get the ShortlistItem object based on the college_id URL parameter
        and the authenticated user.
        """
        college_id = self.kwargs.get('college_id')
        try:
            obj = ShortlistItem.objects.get(
                user=self.request.user,
                university_id=college_id
            )
            self.check_object_permissions(self.request, obj)
            return obj
        except ShortlistItem.DoesNotExist:
            raise generics.Http404({"error": "Shortlist item not found"})
    
    def destroy(self, request, *args, **kwargs):
        """
        Delete the shortlist item and return a 204 No Content response.
        """
        instance = self.get_object()
        self.perform_destroy(instance)
        return Response(status=status.HTTP_204_NO_CONTENT)

    def handle_exception(self, exc):
        """
        Custom exception handling to format errors correctly.
        """
        if isinstance(exc, generics.Http404):
            data = {"error": "Shortlist item not found"}
            return Response(data, status=status.HTTP_404_NOT_FOUND)
        if isinstance(exc, NotAuthenticated):
            return Response({"detail": "Authentication credentials were not provided."}, status=status.HTTP_401_UNAUTHORIZED)
        return super().handle_exception(exc)

class ShortlistView(generics.ListCreateAPIView):
    """
    API endpoint for retrieving and creating shortlist items for the authenticated user.
    GET returns the list of universities shortlisted by the user.
    POST creates a new shortlist item.
    """
    authentication_classes = [TokenAuthentication]
    permission_classes = [IsAuthenticated, IsHighSchoolStudent]

    def get_queryset(self):
        user = self.request.user
        # Use distinct() to avoid duplicate university entries
        return University.objects.filter(shortlisted_by__user=user).distinct()

    def get_serializer_class(self):
        if self.request.method == 'GET':
            return UniversitySerializer
        else:
            return ShortlistItemSerializer

    def perform_create(self, serializer):
        serializer.save(user=self.request.user)

    def get_serializer_context(self):
        context = super().get_serializer_context()
        context['request'] = self.request

        try:
            profile = HighSchoolStudentProfile.objects.get(user=self.request.user)
            context['recommendations'] = profile.university_recommendations or {}
        except HighSchoolStudentProfile.DoesNotExist:
            context['recommendations'] = {}

        return context
    
    def list(self, request, *args, **kwargs):
        queryset = self.get_queryset()

        serializer = self.get_serializer(queryset, many=True)

        # Compute connect_with_trailblazers count
        trailblazer_count = 0
        university_names = list(queryset.values_list('institution', flat=True))

        if university_names:
            name_filter = Q()
            for name in university_names:
                name_filter |= Q(university__icontains=name)
            trailblazer_count = CollegeStudentProfile.objects.filter(name_filter).distinct().count()

        return Response({
            "shortlist_data": serializer.data,
            "connect_with_trailblazers": trailblazer_count
        })

    def create(self, request, *args, **kwargs):
        serializer = self.get_serializer(data=request.data)
        try:
            serializer.is_valid(raise_exception=True)
            self.perform_create(serializer)
        except serializers.ValidationError as e:
            university_errors = e.detail.get("university_id", [])
            if "University already in shortlist" in university_errors:
                return Response({"error": "University already in shortlist"}, status=status.HTTP_409_CONFLICT)
            elif "Invalid university ID" in university_errors:
                return Response({"error": "Invalid university ID"}, status=status.HTTP_400_BAD_REQUEST)
            return Response({"error": "Invalid request format"}, status=status.HTTP_400_BAD_REQUEST)

        return Response(serializer.data, status=status.HTTP_201_CREATED)

class UniversitySearchFilter(filters.BaseFilterBackend):
    """
    Filter backend that searches universities by name, city, or state,
    and optionally filters by state_territory.
    """
    def filter_queryset(self, request, queryset, view):
        search_query = request.query_params.get('search', None)
        state_filter = request.query_params.get('state', None)

        if search_query:
            queryset = queryset.annotate(
                name_similarity=TrigramSimilarity(Cast('institution', TextField()), Value(search_query, output_field=TextField())),
                city_similarity=TrigramSimilarity(Cast('city', TextField()), Value(search_query, output_field=TextField())),
                state_similarity=TrigramSimilarity(Cast('state_territory', TextField()), Value(search_query, output_field=TextField()))
            )

            threshold = 0.3
            queryset = queryset.filter(
                Q(name_similarity__gt=threshold) |
                Q(city_similarity__gt=threshold) |
                Q(state_similarity__gt=threshold)
            ).order_by(
                '-name_similarity',
                '-city_similarity',
                '-state_similarity'
            )

        # Apply exact match filter for state_territory if a specific state is requested
        if state_filter and state_filter.lower() != "all":
            queryset = queryset.filter(state_territory__iexact=state_filter)

        return queryset

class UniversityPagination(PageNumberPagination):
    page_size_query_param = 'page_size'

class UniversitySearchView(generics.ListAPIView):
    """
    API endpoint that allows universities to be searched.
    
    Performs a case-insensitive partial match against the university's
    name, city, or state fields using PostgreSQL trigram similarity.
    """
    serializer_class = UniversitySearchSerializer
    permission_classes = [IsAuthenticated]
    authentication_classes = [TokenAuthentication]
    filter_backends = [UniversitySearchFilter]
    pagination_class = UniversityPagination
    queryset = University.objects.all()

    def list(self, request, *args, **kwargs):
        try:
            # Check for invalid parameters
            allowed_params = ['search', 'page', 'page_size', 'state']
            invalid_params = [param for param in request.query_params if param not in allowed_params]
            if invalid_params:
                return Response({"error": f"Invalid query parameters: {', '.join(invalid_params)}"}, status=status.HTTP_400_BAD_REQUEST)
            return super().list(request, *args, **kwargs)
        except NotAuthenticated as e:
            return Response({"error": str(e)}, status=status.HTTP_401_UNAUTHORIZED)
        except ValidationError as e:
            return Response({"error": str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            import logging
            logger = logging.getLogger(__name__)
            logger.error(f"Error in university search: {str(e)}")
            return Response({"error": "An unexpected error occurred"}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)