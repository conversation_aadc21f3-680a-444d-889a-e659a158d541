import Image from 'next/image'
import { useEffect } from 'react'

// Base route for avatar images
const BASE_ROUTE = '/avatars/'

const getAvatarFileName = (avatar) => `${avatar}.png`

const getAvatarUrl = (avatar) => `${BASE_ROUTE}${getAvatarFileName(avatar)}`

// Avatar Selection component
export const AvatarSelection = ({ onSelect, selectedAvatar, initialAvatarUrl = null }) => {
    const avatars = [
        'girl_1',
        'girl_2',
        'girl_3',
        'girl_4',
        'girl_5',
        'girl_6',
        'girl_7',
        'girl_8',
        'girl_9',
        'girl_10',
        'boy_1',
        'boy_2',
        'boy_3',
        'boy_4',
        'boy_5',
        'boy_6',
        'boy_7',
        'boy_8',
        'boy_9',
        'boy_10',
    ]

    // Set initial selection
    useEffect(() => {
        if (initialAvatarUrl) {
            // Find avatar that matches the initial selection
            const avatar = avatars.find(avatar => initialAvatarUrl.includes(avatar))
            if (avatar) {
                onSelect(avatar)
            }
        }
    }, [initialAvatarUrl])

    return (
        <div className="grid grid-cols-5 gap-3 mt-4 max-w-[400px]">
            {avatars.map((avatar, index) => (
                <div
                    key={index}
                    className={`cursor-pointer rounded-full w-[80px] border-4 ${selectedAvatar === avatar ? 'border-green-500' : 'border-transparent'}`}
                    onClick={() => onSelect(avatar)}
                >
                    <Image 
                        src={getAvatarUrl(avatar)}
                        lazyLoad={true}
                        alt={`Avatar option ${index + 1}`} 
                        width={80} 
                        height={80} 
                        aria-label={`Select avatar ${index + 1}`}
                    />
                </div>
            ))}
        </div>
    )
}

// Convert avatar image to a File
export const avatarToFile = async (avatar) => {
    // Fetch the avatar image as a Blob
    const avatarResponse = await fetch(getAvatarUrl(avatar))
    const blob = await avatarResponse.blob()
    // Convert the Blob to a File
    return new File([blob], getAvatarFileName(avatar), { type: blob.type })
}