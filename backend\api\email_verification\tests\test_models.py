from django.test import TestCase
from django.utils import timezone
from datetime import timedelta
from django.contrib.auth import get_user_model
from api.email_verification.models import EmailVerificationCode

User = get_user_model()

class EmailVerificationCodeModelTest(TestCase):

    def setUp(self):
        self.user = User.objects.create_user(email='<EMAIL>', password='Password123!')
        self.code = EmailVerificationCode.objects.create(user=self.user, code='123456')

    def test_code_creation(self):
        self.assertEqual(self.code.user, self.user)
        self.assertEqual(self.code.code, '123456')
        self.assertFalse(self.code.is_expired())

    def test_code_expiration(self):
        self.code.created_at = timezone.now() - timedelta(minutes=31)
        self.code.save()
        self.assertTrue(self.code.is_expired())
