import { useAuth } from '@/context/AuthProvider'
import { useToast } from './use-toast'
import { useRouter } from 'next/navigation'

export function useShortlist() {
  const { getToken } = useAuth()
  const { toast } = useToast()
  const router = useRouter()

  async function addCollege(collegeId) {
    const token = getToken()
    if (!token) {
      router.push('/login')
      return false
    }
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/shortlist/`, {
        method: 'POST',
        headers: { 'Content-Type': 'application/json', 'Authorization': `Token ${token}` },
        body: JSON.stringify({ university_id: collegeId })
      })
      if (!res.ok) {
        if (res.status === 401) {
          router.push('/login')
        }
        const data = await res.json()
        throw new Error(data.error || 'Failed to add college to shortlist')
      }
      return true
    } catch (error) {
      toast({ title: error.message, variant: 'destructive' })
      return false
    }
  }

  async function removeCollege(collegeId) {
    const token = getToken()
    if (!token) {
      router.push('/login')
      return false
    }
    try {
      const res = await fetch(`${process.env.NEXT_PUBLIC_API_BASE_URL}/api/shortlist/${collegeId}/`, {
        method: 'DELETE',
        headers: { 'Authorization': `Token ${token}` }
      })
      if (!res.ok) {
        if (res.status === 401) {
          router.push('/login')
        }
        const data = await res.json()
        throw new Error(data.error || 'Failed to remove college from shortlist')
      }
      return true
    } catch (error) {
      toast({ title: error.message, variant: 'destructive' })
      return false
    }
  }

  return { addCollege, removeCollege }
}