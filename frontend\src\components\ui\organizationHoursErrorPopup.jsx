import React from 'react';
import { Button } from '@/components/ui/button';

function ErrorPopup({ message, onClose}) {
    if (!message) return null;

    return (
          <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>
            <div className="relative bg-white p-6 rounded-md shadow-md z-10 w-1/3">
                <p>{message}</p>
                <div className="flex space-x-4 mt-4">
                    <Button onClick={onClose} className="bg-gray-300">
                        Close
                    </Button>
                </div>
            </div>
        </div>
    );
}

export default ErrorPopup;