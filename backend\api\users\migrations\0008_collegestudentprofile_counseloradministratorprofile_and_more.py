# Generated by Django 4.2.13 on 2024-10-09 00:44

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('organizations', '0001_initial'),
        ('users', '0007_migrate_existing_users'),
    ]

    operations = [
        migrations.CreateModel(
            name='CollegeStudentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.ImageField(blank=True, default=None, null=True, upload_to='avatars/')),
                ('is_email_verified', models.BooleanField(default=False)),
                ('university', models.CharField(max_length=255)),
                ('interests', models.TextField()),
                ('high_school_name', models.Char<PERSON>ield(max_length=255)),
                ('high_school_zip_code', models.Char<PERSON>ield(max_length=10)),
                ('high_school_city', models.Char<PERSON>ield(max_length=100)),
                ('high_school_state', models.Char<PERSON>ield(max_length=100)),
                ('college_major', models.CharField(max_length=255)),
                ('college_tags', models.JSONField(default=list)),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='CounselorAdministratorProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.ImageField(blank=True, default=None, null=True, upload_to='avatars/')),
                ('is_email_verified', models.BooleanField(default=False)),
                ('position', models.CharField(max_length=255)),
                ('organization', models.ForeignKey(blank=True, default=None, null=True, on_delete=django.db.models.deletion.CASCADE, related_name='counselors_administrators', to='organizations.organization')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.CreateModel(
            name='HighSchoolStudentProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('avatar', models.ImageField(blank=True, default=None, null=True, upload_to='avatars/')),
                ('is_email_verified', models.BooleanField(default=False)),
                ('grade_level', models.IntegerField(blank=True, default=None, null=True)),
                ('organization', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='high_school_students', to='organizations.organization')),
            ],
            options={
                'abstract': False,
            },
        ),
        migrations.RemoveField(
            model_name='counseloradministrator',
            name='organization',
        ),
        migrations.RemoveField(
            model_name='counseloradministrator',
            name='user_ptr',
        ),
        migrations.RemoveField(
            model_name='highschoolstudent',
            name='organization',
        ),
        migrations.RemoveField(
            model_name='highschoolstudent',
            name='user_ptr',
        ),
        migrations.RemoveField(
            model_name='profile',
            name='user',
        ),
        migrations.AddField(
            model_name='user',
            name='user_type',
            field=models.CharField(choices=[('CollegeStudent', 'College Student'), ('HighSchoolStudent', 'High School Student'), ('CounselorAdministrator', 'Counselor/Administrator')], default='CollegeStudent', max_length=30),
        ),
        migrations.DeleteModel(
            name='CollegeStudent',
        ),
        migrations.DeleteModel(
            name='CounselorAdministrator',
        ),
        migrations.DeleteModel(
            name='HighSchoolStudent',
        ),
        migrations.DeleteModel(
            name='Profile',
        ),
        migrations.AddField(
            model_name='highschoolstudentprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='high_school_student_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='counseloradministratorprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='counselor_administrator_profile', to=settings.AUTH_USER_MODEL),
        ),
        migrations.AddField(
            model_name='collegestudentprofile',
            name='user',
            field=models.OneToOneField(on_delete=django.db.models.deletion.CASCADE, related_name='college_student_profile', to=settings.AUTH_USER_MODEL),
        ),
    ]
