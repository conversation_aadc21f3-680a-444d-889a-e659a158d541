import { clsx } from "clsx";
import { twMerge } from "tailwind-merge"

export function cn(...inputs) {
  return twMerge(clsx(inputs));
}

export const isValidEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

// User types
export const UserType = {
  COLLEGE_STUDENT_TYPE: 'CollegeStudent',
  HIGH_SCHOOL_STUDENT_TYPE: 'HighSchoolStudent',
  COUNSELOR_ADMINISTRATOR_TYPE: 'CounselorAdministrator',
}

export const onboardingSteps = {
  HighSchoolStudent: ['profile'],
  CollegeStudent: ['high-school', 'university', 'interests', 'profile', 'availability', 'background-check'],
  CounselorAdministrator: ['organization', 'profile']
}

const onboardingRoutes = {
  HighSchoolStudent: '/high-schooler/onboarding',
  CollegeStudent: '/college-student/onboarding',
  CounselorAdministrator: '/counselor/onboarding'
}

export const dashboardRoutes = {
  HighSchoolStudent: '/find-trailblazers',
  CollegeStudent: '/college-student/bookings',
  CounselorAdministrator: '/find-trailblazers'
}

export const getUseWaitlist = async (token) => {
  try{
    const url = `${process.env.NEXT_PUBLIC_API_BASE_URL}/api/v1/configuration/configuration/`
    const response = await fetch(url,
      {headers: {
      'Content-Type': 'application/json',
      'Authorization': `Token ${token}`
    }})
    const data = await response.json()
    if(response.ok){
      return data.use_waitlist
    }
  }
  catch(e){
    console.error(e)
    return false;
  }
}

export const getNextOnboardingStep = (userType, currentStep = null) => {
  const steps = onboardingSteps[userType]
  const onboardingRoute = onboardingRoutes[userType]

  if (!currentStep) {
    return `${onboardingRoute}/${steps[0]}`
  }
  const currentStepMap = {
    basic_info: 'profile'
  }
  const currentIndex = steps.indexOf(currentStepMap[currentStep] || currentStep)
  if (currentIndex === -1 || currentIndex === steps.length - 1) {
    return dashboardRoutes[userType]
  }

  return `${onboardingRoute}/${steps[currentIndex + 1]}`
}

export const getFullTimeZoneName = () => {
  const date = new Date();
  const formatter = new Intl.DateTimeFormat('en-US', { timeZoneName: 'long' });
  const parts = formatter.formatToParts(date);
  const timeZonePart = parts.find(part => part.type === 'timeZoneName');
  return timeZonePart ? timeZonePart.value : null;
}

export const rankSearchAlphabetically = (value, search) => {
  // Convert both strings to lowercase for case-insensitive comparison
  const lowerValue = value.toLowerCase();
  const lowerSearch = search.toLowerCase();

  // Check if the search term is present in the value
  const includesSearch = lowerValue.includes(lowerSearch);

  // If the value includes the search term, calculate the rank
  if (includesSearch) {
      // Rank based on alphabetical order (A-Z)
      const alphabeticalRank = 1 - (lowerValue.charCodeAt(0) / 122); // Normalize against 'z'

      // Combine alphabetical rank with a boost for containing the search term
      return 0.5 + alphabeticalRank * 0.5; // Adjust weights as needed
  }

  // If the search term is not included, return 0 to hide it
  return 0;
};
